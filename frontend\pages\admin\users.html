<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .users-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .filters-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .users-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr 120px;
            gap: 1rem;
            font-weight: 600;
        }

        .user-row {
            padding: 1rem;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr 120px;
            gap: 1rem;
            border-bottom: 1px solid #eee;
            align-items: center;
        }

        .user-row:hover {
            background: #f8f9fa;
        }

        .user-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            text-align: center;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #007bff;
            color: white;
        }

        .btn-edit:hover {
            background: #0056b3;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .btn-view {
            background: #28a745;
            color: white;
        }

        .btn-view:hover {
            background: #1e7e34;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: var(--primary-color);
            color: white;
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.875rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .table-header,
            .user-row {
                grid-template-columns: 1fr 1fr 80px;
                font-size: 0.875rem;
            }

            .table-header span:nth-child(3),
            .table-header span:nth-child(4),
            .user-row span:nth-child(3),
            .user-row span:nth-child(4) {
                display: none;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="users-container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1>👥 User Management</h1>
                <p>Manage system users, roles, and permissions</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="showAddUserModal()">
                    ➕ Add New User
                </button>
                <a href="../../home.html" class="btn btn-secondary">
                    ← Back to Home
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-users">0</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-users">0</div>
                <div class="stat-label">Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pending-users">0</div>
                <div class="stat-label">Pending Approval</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="doctors-count">0</div>
                <div class="stat-label">Doctors</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h3>🔍 Filter Users</h3>
            <div class="filter-row">
                <div class="form-group">
                    <label for="filter-role">Role</label>
                    <select id="filter-role" onchange="filterUsers()">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="doctor">Doctor</option>
                        <option value="user">User</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="filter-status">Status</label>
                    <select id="filter-status" onchange="filterUsers()">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="search-users">Search</label>
                    <input type="text" id="search-users" placeholder="Search by name or email..." oninput="filterUsers()">
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-secondary" onclick="clearFilters()">Clear Filters</button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="users-table">
            <div class="table-header">
                <span>Name</span>
                <span>Email</span>
                <span>Role</span>
                <span>Status</span>
                <span>Joined</span>
                <span>Actions</span>
            </div>
            <div id="users-list">
                <!-- Users will be loaded here -->
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination">
            <button onclick="previousPage()">← Previous</button>
            <span id="page-info">Page 1 of 1</span>
            <button onclick="nextPage()">Next →</button>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div id="user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Add New User</h3>
                <button class="close-btn" onclick="closeUserModal()">&times;</button>
            </div>
            <form id="user-form">
                <div class="form-group">
                    <label for="user-name">Full Name *</label>
                    <input type="text" id="user-name" required>
                </div>
                <div class="form-group">
                    <label for="user-email">Email *</label>
                    <input type="email" id="user-email" required>
                </div>
                <div class="form-group">
                    <label for="user-phone">Phone</label>
                    <input type="tel" id="user-phone">
                </div>
                <div class="form-group">
                    <label for="user-role">Role *</label>
                    <select id="user-role" required>
                        <option value="">Select Role</option>
                        <option value="admin">Admin</option>
                        <option value="doctor">Doctor</option>
                        <option value="user">User</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="user-status">Status</label>
                    <select id="user-status">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div class="form-group" id="password-group">
                    <label for="user-password">Password *</label>
                    <input type="password" id="user-password">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeUserModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Application Scripts -->
    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        // User Management System
        let currentUsers = [];
        let filteredUsers = [];
        let currentPage = 1;
        const usersPerPage = 10;
        let editingUserId = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            updateStatistics();
        });

        // Load users from API/Supabase
        async function loadUsers() {
            try {
                // Try to load from Supabase first
                if (window.SupabaseDB) {
                    const result = await SupabaseDB.select('users');
                    if (result.success && result.data) {
                        currentUsers = result.data.map(user => ({
                            id: user.id,
                            name: user.full_name || user.email,
                            email: user.email,
                            role: user.role || 'user',
                            status: user.status || 'active',
                            joined: user.created_at ? user.created_at.split('T')[0] : new Date().toISOString().split('T')[0],
                            phone: user.phone || ''
                        }));
                        filteredUsers = [...currentUsers];
                        displayUsers();
                        updateStatistics();
                        return;
                    }
                }

                // Fallback to mock data for demonstration
                currentUsers = [
                    {
                        id: 1,
                        name: 'Dr. Sarah Johnson',
                        email: '<EMAIL>',
                        role: 'doctor',
                        status: 'active',
                        joined: '2024-01-15',
                        phone: '******-0123'
                    },
                    {
                        id: 2,
                        name: 'Admin User',
                        email: '<EMAIL>',
                        role: 'admin',
                        status: 'active',
                        joined: '2024-01-01',
                        phone: '******-0100'
                    },
                    {
                        id: 3,
                        name: 'Mary Wilson',
                        email: '<EMAIL>',
                        role: 'user',
                        status: 'active',
                        joined: '2024-02-20',
                        phone: '******-0456'
                    },
                    {
                        id: 4,
                        name: 'Dr. Michael Brown',
                        email: '<EMAIL>',
                        role: 'doctor',
                        status: 'pending',
                        joined: '2024-03-01',
                        phone: '******-0789'
                    },
                    {
                        id: 5,
                        name: 'Jennifer Davis',
                        email: '<EMAIL>',
                        role: 'user',
                        status: 'inactive',
                        joined: '2024-01-30',
                        phone: '******-0321'
                    }
                ];

                filteredUsers = [...currentUsers];
                displayUsers();
                updateStatistics();
            } catch (error) {
                console.error('Error loading users:', error);
                showNotification('Error loading users', 'error');
            }
        }

        // Display users in table
        function displayUsers() {
            const usersList = document.getElementById('users-list');
            const startIndex = (currentPage - 1) * usersPerPage;
            const endIndex = startIndex + usersPerPage;
            const usersToShow = filteredUsers.slice(startIndex, endIndex);

            usersList.innerHTML = usersToShow.map(user => `
                <div class="user-row">
                    <span>${user.name}</span>
                    <span>${user.email}</span>
                    <span>${user.role.charAt(0).toUpperCase() + user.role.slice(1)}</span>
                    <span class="user-status status-${user.status}">${user.status.charAt(0).toUpperCase() + user.status.slice(1)}</span>
                    <span>${formatDate(user.joined)}</span>
                    <div class="user-actions">
                        <button class="action-btn btn-view" onclick="viewUser(${user.id})" title="View Details">👁️</button>
                        <button class="action-btn btn-edit" onclick="editUser(${user.id})" title="Edit User">✏️</button>
                        <button class="action-btn btn-delete" onclick="deleteUser(${user.id})" title="Delete User">🗑️</button>
                    </div>
                </div>
            `).join('');

            updatePagination();
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
            document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
        }

        // Update statistics
        function updateStatistics() {
            const totalUsers = currentUsers.length;
            const activeUsers = currentUsers.filter(u => u.status === 'active').length;
            const pendingUsers = currentUsers.filter(u => u.status === 'pending').length;
            const doctorsCount = currentUsers.filter(u => u.role === 'doctor').length;

            document.getElementById('total-users').textContent = totalUsers;
            document.getElementById('active-users').textContent = activeUsers;
            document.getElementById('pending-users').textContent = pendingUsers;
            document.getElementById('doctors-count').textContent = doctorsCount;
        }

        // Filter users
        function filterUsers() {
            const roleFilter = document.getElementById('filter-role').value;
            const statusFilter = document.getElementById('filter-status').value;
            const searchTerm = document.getElementById('search-users').value.toLowerCase();

            filteredUsers = currentUsers.filter(user => {
                const matchesRole = !roleFilter || user.role === roleFilter;
                const matchesStatus = !statusFilter || user.status === statusFilter;
                const matchesSearch = !searchTerm || 
                    user.name.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm);

                return matchesRole && matchesStatus && matchesSearch;
            });

            currentPage = 1;
            displayUsers();
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('filter-role').value = '';
            document.getElementById('filter-status').value = '';
            document.getElementById('search-users').value = '';
            filteredUsers = [...currentUsers];
            currentPage = 1;
            displayUsers();
        }

        // Pagination functions
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                displayUsers();
            }
        }

        function nextPage() {
            const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                displayUsers();
            }
        }

        // Modal functions
        function showAddUserModal() {
            editingUserId = null;
            document.getElementById('modal-title').textContent = 'Add New User';
            document.getElementById('user-form').reset();
            document.getElementById('password-group').style.display = 'block';
            document.getElementById('user-password').required = true;
            document.getElementById('user-modal').style.display = 'block';
        }

        function editUser(userId) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            editingUserId = userId;
            document.getElementById('modal-title').textContent = 'Edit User';
            document.getElementById('user-name').value = user.name;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-phone').value = user.phone || '';
            document.getElementById('user-role').value = user.role;
            document.getElementById('user-status').value = user.status;
            document.getElementById('password-group').style.display = 'none';
            document.getElementById('user-password').required = false;
            document.getElementById('user-modal').style.display = 'block';
        }

        function closeUserModal() {
            document.getElementById('user-modal').style.display = 'none';
            editingUserId = null;
        }

        function viewUser(userId) {
            const user = currentUsers.find(u => u.id === userId);
            if (!user) return;

            alert(`User Details:\n\nName: ${user.name}\nEmail: ${user.email}\nRole: ${user.role}\nStatus: ${user.status}\nJoined: ${formatDate(user.joined)}\nPhone: ${user.phone || 'Not provided'}`);
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                currentUsers = currentUsers.filter(u => u.id !== userId);
                filteredUsers = filteredUsers.filter(u => u.id !== userId);
                displayUsers();
                updateStatistics();
                showNotification('User deleted successfully', 'success');
            }
        }

        // Handle form submission
        document.getElementById('user-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const userData = {
                name: document.getElementById('user-name').value,
                email: document.getElementById('user-email').value,
                phone: document.getElementById('user-phone').value,
                role: document.getElementById('user-role').value,
                status: document.getElementById('user-status').value
            };

            if (!editingUserId) {
                userData.password = document.getElementById('user-password').value;
                userData.id = Date.now();
                userData.joined = new Date().toISOString().split('T')[0];
                currentUsers.push(userData);
                showNotification('User created successfully', 'success');
            } else {
                const userIndex = currentUsers.findIndex(u => u.id === editingUserId);
                if (userIndex !== -1) {
                    currentUsers[userIndex] = { ...currentUsers[userIndex], ...userData };
                    showNotification('User updated successfully', 'success');
                }
            }

            filteredUsers = [...currentUsers];
            displayUsers();
            updateStatistics();
            closeUserModal();
        });

        // Utility functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 2rem;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                border-radius: 4px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('user-modal');
            if (event.target === modal) {
                closeUserModal();
            }
        }
    </script>
</body>
</html>
