#!/usr/bin/env python3
"""
Main Launcher for Maternal-Child Health Care System
Runs the unified Flask server (frontend + backend on port 5000)
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🏥 MATERNAL-CHILD HEALTH CARE SYSTEM")
    print("=" * 60)
    print("🚀 Starting unified server on port 5000...")
    print("🌐 Application: http://localhost:5000")
    print("🔐 Login: http://localhost:5000/login")
    print("=" * 60)

    # Change to backend directory
    backend_dir = Path(__file__).parent / "backend"
    app_file = backend_dir / "app.py"

    if not app_file.exists():
        print(f"❌ Error: Backend app.py not found at {app_file}")
        sys.exit(1)

    try:
        # Change to backend directory and run the Flask app
        os.chdir(backend_dir)

        print("🔄 Launching server...")

        # Open browser after a short delay
        def open_browser_delayed():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:5000/")
                print("🌐 Browser opened")
            except:
                pass

        # Start browser opening in background
        import threading
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()

        # Start the Flask server (this will block)
        subprocess.run([sys.executable, "app.py"], check=True)

    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
