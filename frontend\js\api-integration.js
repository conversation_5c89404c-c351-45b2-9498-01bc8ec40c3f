/**
 * Comprehensive API Integration Service
 * Connects all frontend features to backend APIs
 */

class APIIntegration {
    constructor() {
        this.baseURL = 'http://localhost:5000/api';
        this.token = localStorage.getItem('access_token');
        this.retryAttempts = 3;
        this.retryDelay = 1000;
    }

    // Enhanced request method with retry logic and error handling
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Add authorization header if token exists
        if (this.token) {
            config.headers['Authorization'] = `Bearer ${this.token}`;
        }

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, config);
                const data = await response.json();

                if (!response.ok) {
                    if (response.status === 401) {
                        // Token expired, redirect to login
                        this.handleAuthError();
                        throw new Error('Authentication required');
                    }
                    throw new Error(data.error || `HTTP error! status: ${response.status}`);
                }

                return { success: true, data };
            } catch (error) {
                console.error(`API Request attempt ${attempt} failed:`, error);
                
                if (attempt === this.retryAttempts) {
                    return { success: false, error: error.message };
                }
                
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
            }
        }
    }

    // Handle authentication errors
    handleAuthError() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_data');
        this.token = null;
        
        if (window.location.pathname !== '/login.html') {
            window.location.href = '/login.html';
        }
    }

    // Update token
    setToken(token) {
        this.token = token;
        localStorage.setItem('access_token', token);
    }

    // Authentication APIs
    async register(userData) {
        const response = await this.makeRequest('/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });

        if (response.success && response.data.access_token) {
            this.setToken(response.data.access_token);
            localStorage.setItem('user_data', JSON.stringify(response.data.user));
        }

        return response;
    }

    async login(email, password) {
        const response = await this.makeRequest('/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });

        if (response.success && response.data.access_token) {
            this.setToken(response.data.access_token);
            localStorage.setItem('user_data', JSON.stringify(response.data.user));
        }

        return response;
    }

    // Pregnancy Profile APIs
    async getPregnancyProfile() {
        return await this.makeRequest('/pregnancy/profile');
    }

    async createPregnancyProfile(profileData) {
        return await this.makeRequest('/pregnancy/profile', {
            method: 'POST',
            body: JSON.stringify(profileData)
        });
    }

    async updatePregnancyProfile(profileData) {
        return await this.makeRequest('/pregnancy/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });
    }

    // Weight Tracking APIs
    async getWeightRecords() {
        return await this.makeRequest('/weight/track');
    }

    async addWeightRecord(weightData) {
        return await this.makeRequest('/weight/track', {
            method: 'POST',
            body: JSON.stringify(weightData)
        });
    }

    // Baby Profile APIs
    async getBabyProfiles() {
        return await this.makeRequest('/baby/profile');
    }

    async createBabyProfile(babyData) {
        return await this.makeRequest('/baby/profile', {
            method: 'POST',
            body: JSON.stringify(babyData)
        });
    }

    async updateBabyProfile(babyId, babyData) {
        return await this.makeRequest(`/baby/profile/${babyId}`, {
            method: 'PUT',
            body: JSON.stringify(babyData)
        });
    }

    // Sleep Tracking APIs
    async getSleepRecords(babyId) {
        return await this.makeRequest(`/baby/sleep?baby_id=${babyId}`);
    }

    async addSleepRecord(sleepData) {
        return await this.makeRequest('/baby/sleep', {
            method: 'POST',
            body: JSON.stringify(sleepData)
        });
    }

    // Vaccination APIs
    async getVaccinations(babyId = null) {
        const query = babyId ? `?baby_id=${babyId}` : '';
        return await this.makeRequest(`/vaccinations${query}`);
    }

    async addVaccination(vaccinationData) {
        return await this.makeRequest('/vaccinations', {
            method: 'POST',
            body: JSON.stringify(vaccinationData)
        });
    }

    async updateVaccination(vaccinationId, vaccinationData) {
        return await this.makeRequest(`/vaccinations/${vaccinationId}`, {
            method: 'PUT',
            body: JSON.stringify(vaccinationData)
        });
    }

    // Appointment APIs
    async getAppointments() {
        return await this.makeRequest('/appointments');
    }

    async createAppointment(appointmentData) {
        return await this.makeRequest('/appointments', {
            method: 'POST',
            body: JSON.stringify(appointmentData)
        });
    }

    async updateAppointment(appointmentId, appointmentData) {
        return await this.makeRequest(`/appointments/${appointmentId}`, {
            method: 'PUT',
            body: JSON.stringify(appointmentData)
        });
    }

    async cancelAppointment(appointmentId) {
        return await this.makeRequest(`/appointments/${appointmentId}`, {
            method: 'DELETE'
        });
    }

    // Nutrition APIs
    async getNutritionRecords() {
        return await this.makeRequest('/nutrition');
    }

    async addNutritionRecord(nutritionData) {
        return await this.makeRequest('/nutrition', {
            method: 'POST',
            body: JSON.stringify(nutritionData)
        });
    }

    // Meditation APIs
    async getMeditationSessions() {
        return await this.makeRequest('/meditation');
    }

    async addMeditationSession(sessionData) {
        return await this.makeRequest('/meditation', {
            method: 'POST',
            body: JSON.stringify(sessionData)
        });
    }

    // Chat APIs
    async getChatHistory() {
        return await this.makeRequest('/chat');
    }

    async sendChatMessage(message, messageType = 'general') {
        return await this.makeRequest('/chat', {
            method: 'POST',
            body: JSON.stringify({ message, message_type: messageType })
        });
    }

    // Admin APIs
    async getSystemStats() {
        return await this.makeRequest('/admin/stats');
    }

    async getUsers(page = 1, limit = 20) {
        return await this.makeRequest(`/admin/users?page=${page}&limit=${limit}`);
    }

    async createUser(userData) {
        return await this.makeRequest('/admin/users', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    async updateUser(userId, userData) {
        return await this.makeRequest(`/admin/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData)
        });
    }

    async deleteUser(userId) {
        return await this.makeRequest(`/admin/users/${userId}`, {
            method: 'DELETE'
        });
    }

    async getAnalytics(days = 30) {
        return await this.makeRequest(`/admin/analytics?days=${days}`);
    }

    async getSystemLogs(level = 'all', page = 1) {
        return await this.makeRequest(`/admin/logs?level=${level}&page=${page}`);
    }

    async getSystemSettings() {
        return await this.makeRequest('/admin/settings');
    }

    async updateSystemSettings(settings) {
        return await this.makeRequest('/admin/settings', {
            method: 'PUT',
            body: JSON.stringify(settings)
        });
    }

    // Doctor APIs
    async getDoctorPatients() {
        return await this.makeRequest('/doctor/patients');
    }

    async getDoctorAppointments() {
        return await this.makeRequest('/doctor/appointments');
    }

    async updateAppointmentStatus(appointmentId, status) {
        return await this.makeRequest(`/doctor/appointments/${appointmentId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ status })
        });
    }

    async addMedicalRecord(patientId, recordData) {
        return await this.makeRequest(`/doctor/patients/${patientId}/records`, {
            method: 'POST',
            body: JSON.stringify(recordData)
        });
    }

    // Admin management functions
    async getDoctors() {
        return await this.makeRequest('/admin/doctors');
    }

    async createDoctor(doctorData) {
        return await this.makeRequest('/admin/doctors', {
            method: 'POST',
            body: JSON.stringify(doctorData)
        });
    }

    async deleteDoctor(doctorId) {
        return await this.makeRequest(`/admin/doctors/${doctorId}`, {
            method: 'DELETE'
        });
    }

    async getNutritionPlans() {
        return await this.makeRequest('/admin/nutrition_plans');
    }

    async createNutritionPlan(planData) {
        return await this.makeRequest('/admin/nutrition_plans', {
            method: 'POST',
            body: JSON.stringify(planData)
        });
    }

    async getVaccinationSchedules() {
        return await this.makeRequest('/admin/vaccinations');
    }

    async createVaccinationSchedule(scheduleData) {
        return await this.makeRequest('/admin/vaccinations', {
            method: 'POST',
            body: JSON.stringify(scheduleData)
        });
    }

    async getSleepSchedules() {
        return await this.makeRequest('/admin/sleep_schedules');
    }

    async createSleepSchedule(scheduleData) {
        return await this.makeRequest('/admin/sleep_schedules', {
            method: 'POST',
            body: JSON.stringify(scheduleData)
        });
    }

    async getGovernmentSchemes() {
        return await this.makeRequest('/admin/schemes');
    }

    async createGovernmentScheme(schemeData) {
        return await this.makeRequest('/admin/schemes', {
            method: 'POST',
            body: JSON.stringify(schemeData)
        });
    }

    // Doctor patient management functions
    async getPatients() {
        return await this.makeRequest('/doctor/patients');
    }

    async getPatientDetails(patientId) {
        return await this.makeRequest(`/doctor/patient-details/${patientId}`);
    }

    async getPatientMedicalRecords(patientId) {
        return await this.makeRequest(`/doctor/patient-medical-records/${patientId}`);
    }

    async generatePatientReport(reportData) {
        return await this.makeRequest('/doctor/generate-patient-report', {
            method: 'POST',
            body: JSON.stringify(reportData)
        });
    }

    // Health check
    async healthCheck() {
        return await this.makeRequest('/health');
    }

    // File upload helper
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        const config = {
            method: 'POST',
            body: formData,
            headers: {}
        };

        // Add authorization header if token exists
        if (this.token) {
            config.headers['Authorization'] = `Bearer ${this.token}`;
        }

        // Don't set Content-Type for FormData, let browser set it
        delete config.headers['Content-Type'];

        return await this.makeRequest(endpoint, config);
    }
}

// Create global instance
window.apiClient = new APIIntegration();

// Global helper function for backward compatibility
window.apiCall = async (endpoint, method = 'GET', data = null) => {
    const options = { method };
    if (data) {
        options.body = JSON.stringify(data);
    }
    return await window.apiClient.makeRequest(endpoint, options);
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIIntegration;
}
