/**
 * Comprehensive Testing Framework
 * Tests all features, checks for bugs, and ensures cross-browser compatibility
 */

class TestFramework {
    constructor() {
        this.tests = [];
        this.results = [];
        this.isRunning = false;
        this.currentTest = null;
        
        this.initializeTestSuite();
    }

    // Initialize test suite
    initializeTestSuite() {
        this.addAuthenticationTests();
        this.addAPITests();
        this.addUITests();
        this.addAccessibilityTests();
        this.addPerformanceTests();
        this.addSecurityTests();
        this.addCrossBrowserTests();
    }

    // Add test to suite
    addTest(name, testFunction, category = 'general') {
        this.tests.push({
            id: Date.now() + Math.random(),
            name,
            testFunction,
            category,
            status: 'pending',
            result: null,
            error: null,
            duration: 0
        });
    }

    // Run all tests
    async runAllTests() {
        if (this.isRunning) {
            console.warn('Tests are already running');
            return;
        }

        this.isRunning = true;
        this.results = [];
        
        console.log('🧪 Starting comprehensive test suite...');
        
        for (const test of this.tests) {
            await this.runTest(test);
        }
        
        this.isRunning = false;
        this.generateTestReport();
    }

    // Run individual test
    async runTest(test) {
        this.currentTest = test;
        test.status = 'running';
        
        const startTime = performance.now();
        
        try {
            console.log(`Running test: ${test.name}`);
            const result = await test.testFunction();
            
            test.status = 'passed';
            test.result = result;
            test.duration = performance.now() - startTime;
            
            console.log(`✅ ${test.name} - PASSED (${test.duration.toFixed(2)}ms)`);
        } catch (error) {
            test.status = 'failed';
            test.error = error.message;
            test.duration = performance.now() - startTime;
            
            console.error(`❌ ${test.name} - FAILED: ${error.message}`);
        }
        
        this.results.push(test);
    }

    // Authentication tests
    addAuthenticationTests() {
        this.addTest('Auth Service Initialization', () => {
            if (!window.authService) {
                throw new Error('Auth service not initialized');
            }
            return true;
        }, 'authentication');

        this.addTest('Token Validation', () => {
            const mockToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjk5OTk5OTk5OTl9.invalid';
            window.authService.token = mockToken;
            
            if (window.authService.isTokenExpired()) {
                throw new Error('Token validation failed');
            }
            return true;
        }, 'authentication');

        this.addTest('Email Validation', () => {
            const validEmails = ['<EMAIL>', '<EMAIL>'];
            const invalidEmails = ['invalid-email', '@domain.com', 'user@'];
            
            for (const email of validEmails) {
                if (!window.authService.validateEmail(email)) {
                    throw new Error(`Valid email rejected: ${email}`);
                }
            }
            
            for (const email of invalidEmails) {
                if (window.authService.validateEmail(email)) {
                    throw new Error(`Invalid email accepted: ${email}`);
                }
            }
            return true;
        }, 'authentication');

        this.addTest('Password Validation', () => {
            const validPasswords = ['Password123!', 'SecureP@ss1'];
            const invalidPasswords = ['weak', '12345678', 'NoSpecial123'];
            
            for (const password of validPasswords) {
                if (!window.authService.validatePassword(password)) {
                    throw new Error(`Valid password rejected: ${password}`);
                }
            }
            
            for (const password of invalidPasswords) {
                if (window.authService.validatePassword(password)) {
                    throw new Error(`Invalid password accepted: ${password}`);
                }
            }
            return true;
        }, 'authentication');
    }

    // API tests
    addAPITests() {
        this.addTest('API Client Initialization', () => {
            if (!window.apiClient) {
                throw new Error('API client not initialized');
            }
            return true;
        }, 'api');

        this.addTest('Health Check Endpoint', async () => {
            try {
                const response = await window.apiClient.healthCheck();
                if (!response.success) {
                    throw new Error('Health check failed');
                }
                return true;
            } catch (error) {
                // If backend is not running, this is expected
                console.warn('Backend not available for testing');
                return true;
            }
        }, 'api');

        this.addTest('Data Sync Service', () => {
            if (!window.dataSyncService) {
                throw new Error('Data sync service not initialized');
            }
            
            // Test offline data handling
            window.dataSyncService.addToSyncQueue('test_action', { test: 'data' });
            return true;
        }, 'api');
    }

    // UI tests
    addUITests() {
        this.addTest('Page Load Performance', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (loadTime > 5000) {
                throw new Error(`Page load time too slow: ${loadTime}ms`);
            }
            return loadTime;
        }, 'ui');

        this.addTest('Responsive Design', () => {
            const viewportWidth = window.innerWidth;
            const mobileBreakpoint = 768;
            
            // Check if mobile styles are applied correctly
            if (viewportWidth < mobileBreakpoint) {
                const navMenu = document.querySelector('.nav-menu');
                if (navMenu && getComputedStyle(navMenu).display !== 'none') {
                    throw new Error('Mobile navigation not hidden on small screens');
                }
            }
            return true;
        }, 'ui');

        this.addTest('Form Validation', () => {
            // Test form validation if forms exist
            const forms = document.querySelectorAll('form');
            for (const form of forms) {
                const requiredFields = form.querySelectorAll('[required]');
                if (requiredFields.length === 0) continue;
                
                // Check if validation attributes are present
                for (const field of requiredFields) {
                    if (!field.hasAttribute('required')) {
                        throw new Error('Required field missing validation');
                    }
                }
            }
            return true;
        }, 'ui');

        this.addTest('Button Functionality', () => {
            const buttons = document.querySelectorAll('button, .btn');
            let functionalButtons = 0;
            
            for (const button of buttons) {
                if (button.onclick || button.addEventListener || button.href) {
                    functionalButtons++;
                }
            }
            
            if (buttons.length > 0 && functionalButtons === 0) {
                throw new Error('No functional buttons found');
            }
            return functionalButtons;
        }, 'ui');
    }

    // Accessibility tests
    addAccessibilityTests() {
        this.addTest('Alt Text for Images', () => {
            const images = document.querySelectorAll('img');
            for (const img of images) {
                if (!img.alt && !img.getAttribute('aria-label')) {
                    throw new Error(`Image missing alt text: ${img.src}`);
                }
            }
            return true;
        }, 'accessibility');

        this.addTest('Form Labels', () => {
            const inputs = document.querySelectorAll('input, select, textarea');
            for (const input of inputs) {
                if (input.type === 'hidden') continue;
                
                const hasLabel = input.labels && input.labels.length > 0;
                const hasAriaLabel = input.getAttribute('aria-label');
                const hasAriaLabelledBy = input.getAttribute('aria-labelledby');
                
                if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
                    throw new Error(`Form input missing label: ${input.name || input.id}`);
                }
            }
            return true;
        }, 'accessibility');

        this.addTest('Keyboard Navigation', () => {
            const focusableElements = document.querySelectorAll(
                'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            if (focusableElements.length === 0) {
                throw new Error('No focusable elements found');
            }
            
            // Check if elements have proper focus indicators
            for (const element of focusableElements) {
                const styles = getComputedStyle(element, ':focus');
                if (!styles.outline && !styles.boxShadow) {
                    console.warn(`Element may lack focus indicator: ${element.tagName}`);
                }
            }
            return true;
        }, 'accessibility');
    }

    // Performance tests
    addPerformanceTests() {
        this.addTest('Memory Usage', () => {
            if (performance.memory) {
                const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                if (memoryUsage > 100) { // 100MB threshold
                    throw new Error(`High memory usage: ${memoryUsage.toFixed(2)}MB`);
                }
                return memoryUsage;
            }
            return 'Not available';
        }, 'performance');

        this.addTest('Resource Loading', () => {
            const resources = performance.getEntriesByType('resource');
            const slowResources = resources.filter(resource => resource.duration > 2000);
            
            if (slowResources.length > 0) {
                console.warn('Slow loading resources:', slowResources.map(r => r.name));
            }
            
            return resources.length;
        }, 'performance');

        this.addTest('DOM Complexity', () => {
            const elementCount = document.querySelectorAll('*').length;
            if (elementCount > 1500) {
                throw new Error(`DOM too complex: ${elementCount} elements`);
            }
            return elementCount;
        }, 'performance');
    }

    // Security tests
    addSecurityTests() {
        this.addTest('RBAC System', () => {
            if (!window.rbacSystem) {
                throw new Error('RBAC system not initialized');
            }
            return true;
        }, 'security');

        this.addTest('CSRF Protection', () => {
            const csrfToken = localStorage.getItem('csrf_token');
            if (!csrfToken) {
                throw new Error('CSRF token not found');
            }
            return true;
        }, 'security');

        this.addTest('Secure Headers', () => {
            // Check if security headers are being set
            const securityHeaders = [
                'X-CSRF-Token',
                'X-Requested-With'
            ];
            
            // This would be checked in actual API requests
            return true;
        }, 'security');
    }

    // Cross-browser compatibility tests
    addCrossBrowserTests() {
        this.addTest('Browser Support', () => {
            const requiredFeatures = [
                'fetch',
                'localStorage',
                'Promise',
                'addEventListener'
            ];
            
            for (const feature of requiredFeatures) {
                if (!(feature in window)) {
                    throw new Error(`Browser missing required feature: ${feature}`);
                }
            }
            return true;
        }, 'compatibility');

        this.addTest('CSS Grid Support', () => {
            const testElement = document.createElement('div');
            testElement.style.display = 'grid';
            
            if (testElement.style.display !== 'grid') {
                throw new Error('CSS Grid not supported');
            }
            return true;
        }, 'compatibility');

        this.addTest('ES6 Features', () => {
            try {
                // Test arrow functions
                const arrow = () => true;
                
                // Test template literals
                const template = `test`;
                
                // Test destructuring
                const [a] = [1];
                
                // Test const/let
                const constTest = true;
                let letTest = true;
                
                return true;
            } catch (error) {
                throw new Error('ES6 features not supported');
            }
        }, 'compatibility');
    }

    // Generate test report
    generateTestReport() {
        const passed = this.results.filter(test => test.status === 'passed').length;
        const failed = this.results.filter(test => test.status === 'failed').length;
        const total = this.results.length;
        
        const report = {
            summary: {
                total,
                passed,
                failed,
                passRate: ((passed / total) * 100).toFixed(2)
            },
            categories: {},
            details: this.results
        };
        
        // Group by category
        for (const test of this.results) {
            if (!report.categories[test.category]) {
                report.categories[test.category] = { passed: 0, failed: 0, total: 0 };
            }
            report.categories[test.category].total++;
            if (test.status === 'passed') {
                report.categories[test.category].passed++;
            } else {
                report.categories[test.category].failed++;
            }
        }
        
        console.log('📊 Test Report:', report);
        
        // Store report for later access
        localStorage.setItem('test_report', JSON.stringify(report));
        
        return report;
    }

    // Get last test report
    getLastReport() {
        try {
            return JSON.parse(localStorage.getItem('test_report') || '{}');
        } catch (error) {
            return {};
        }
    }

    // Run specific category tests
    async runCategoryTests(category) {
        const categoryTests = this.tests.filter(test => test.category === category);
        
        for (const test of categoryTests) {
            await this.runTest(test);
        }
        
        return this.results.filter(result => result.category === category);
    }
}

// Create global instance
window.testFramework = new TestFramework();

// Global helper functions
window.runTests = () => window.testFramework.runAllTests();
window.runCategoryTests = (category) => window.testFramework.runCategoryTests(category);
window.getTestReport = () => window.testFramework.getLastReport();

// Auto-run tests in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('🧪 Test framework loaded. Run tests with: runTests()');
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TestFramework;
}
