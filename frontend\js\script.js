/**
 * Maternal-Child Health System - Main JavaScript File
 * Handles API calls, authentication, and UI interactions
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:5000/api',
    TOKEN_KEY: 'mchs_token',
    USER_KEY: 'mchs_user'
};

// Authentication Manager
class AuthManager {
    static getToken() {
        return localStorage.getItem(CONFIG.TOKEN_KEY);
    }

    static setToken(token) {
        localStorage.setItem(CONFIG.TOKEN_KEY, token);
    }

    static removeToken() {
        localStorage.removeItem(CONFIG.TOKEN_KEY);
        localStorage.removeItem(CONFIG.USER_KEY);
    }

    static getUser() {
        const userStr = localStorage.getItem(CONFIG.USER_KEY);
        return userStr ? JSON.parse(userStr) : null;
    }

    static setUser(user) {
        localStorage.setItem(CONFIG.USER_KEY, JSON.stringify(user));
    }

    static isAuthenticated() {
        return !!this.getToken();
    }

    static getUserRole() {
        const user = this.getUser();
        return user ? user.role : null;
    }

    static logout() {
        this.removeToken();
        window.location.href = '/home.html';
    }
}

// API Client
class APIClient {
    static async request(endpoint, options = {}) {
        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        const token = AuthManager.getToken();

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }




    static async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    static async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    static async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    static async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }
}

// Authentication API
class AuthAPI {
    static async login(email, password) {
        return APIClient.post('/auth/login', { email, password });
    }

    static async signup(userData) {
        return APIClient.post('/auth/signup', userData);
    }

    static async register(email, password, full_name, role = 'user', phone = null) {
        return this.signup({ email, password, full_name, role, phone });
    }

    static async logout() {
        return APIClient.post('/auth/logout');
    }

    static async getProfile() {
        return APIClient.get('/auth/profile');
    }

    static async updateProfile(data) {
        return APIClient.put('/auth/profile', data);
    }

    static async changePassword(currentPassword, newPassword) {
        return APIClient.post('/auth/change-password', {
            current_password: currentPassword,
            new_password: newPassword
        });
    }
}

// UI Utilities
class UIUtils {
    static showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <span>${message}</span>
            <button type="button" class="btn-close" onclick="this.parentElement.remove()">×</button>
        `;

        alertContainer.appendChild(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    static createAlertContainer() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        `;
        document.body.appendChild(container);
        return container;
    }

    static showLoading(element) {
        const originalContent = element.innerHTML;
        element.innerHTML = '<span class="spinner"></span> Loading...';
        element.disabled = true;
        
        return () => {
            element.innerHTML = originalContent;
            element.disabled = false;
        };
    }

    static formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    static formatDateTime(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    static validatePhone(phone) {
        const re = /^[\+]?[1-9][\d]{0,15}$/;
        return re.test(phone.replace(/\s/g, ''));
    }

    static validatePassword(password) {
        const errors = [];
        
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one digit');
        }
        
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// Form Handler
class FormHandler {
    static handleSubmit(formId, submitHandler) {
        const form = document.getElementById(formId);
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            try {
                await submitHandler(data);
            } catch (error) {
                UIUtils.showAlert(error.message, 'error');
            }
        });
    }

    static validateForm(formId, rules) {
        const form = document.getElementById(formId);
        if (!form) return false;

        let isValid = true;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Clear previous errors
        form.querySelectorAll('.form-error').forEach(error => error.remove());
        form.querySelectorAll('.form-control.error').forEach(input => {
            input.classList.remove('error');
        });

        // Validate each field
        for (const [field, rule] of Object.entries(rules)) {
            const input = form.querySelector(`[name="${field}"]`);
            const value = data[field];

            if (rule.required && (!value || value.trim() === '')) {
                this.showFieldError(input, 'This field is required');
                isValid = false;
                continue;
            }

            if (value && rule.validate && !rule.validate(value)) {
                this.showFieldError(input, rule.message || 'Invalid value');
                isValid = false;
            }
        }

        return isValid;
    }

    static showFieldError(input, message) {
        input.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        errorElement.textContent = message;
        
        input.parentNode.appendChild(errorElement);
    }
}

// Navigation Manager
class NavigationManager {
    static init() {
        this.setupSidebarToggle();
        this.setupActiveNavigation();
        this.checkAuthentication();
    }

    static setupSidebarToggle() {
        const toggleBtn = document.getElementById('sidebar-toggle');
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');

        if (toggleBtn && sidebar && mainContent) {
            toggleBtn.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }
    }

    static setupActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .sidebar-nav a');

        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    }

    static checkAuthentication() {
        const publicPages = ['/pages/login.html', '/index.html', '/'];
        const currentPath = window.location.pathname;

        if (!AuthManager.isAuthenticated() && !publicPages.includes(currentPath)) {
            window.location.href = '/pages/login.html';
        }
    }

    static redirectToMainPage() {
        const role = AuthManager.getUserRole();

        switch (role) {
            case 'admin':
                window.location.href = '/pages/admin/users.html';
                break;
            case 'doctor':
                window.location.href = '/pages/doctor/patients.html';
                break;
            case 'user':
                window.location.href = '/pages/mother/profile.html';
                break;
            default:
                window.location.href = '/pages/login.html';
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    NavigationManager.init();
});

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    UIUtils.showAlert('An unexpected error occurred. Please try again.', 'error');
});

// Handle unauthorized responses
window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && event.reason.message.includes('401')) {
        AuthManager.logout();
    }
});
