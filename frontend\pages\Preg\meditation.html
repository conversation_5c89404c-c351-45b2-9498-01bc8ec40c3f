<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meditation & Wellness - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">🧘‍♀️ MCHS - Meditation & Wellness</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Meditation & Wellness</h1>
                <p class="page-description">Find peace and relaxation during your pregnancy journey</p>
            </div>

            <!-- Wellness Categories -->
            <div class="wellness-categories mb-4">
                <div class="category-tabs">
                    <button class="category-tab active" onclick="showCategory('guided')">Guided Meditation</button>
                    <button class="category-tab" onclick="showCategory('breathing')">Breathing Exercises</button>
                    <button class="category-tab" onclick="showCategory('prenatal')">Prenatal Yoga</button>
                    <button class="category-tab" onclick="showCategory('sleep')">Sleep Stories</button>
                    <button class="category-tab" onclick="showCategory('music')">Relaxing Music</button>
                </div>
            </div>

            <!-- Current Session Card -->
            <div id="current-session-card" class="card mb-4" style="display: none;">
                <div class="card-body">
                    <div class="session-player">
                        <div class="session-info">
                            <h3 id="session-title">Session Title</h3>
                            <p id="session-description">Session description</p>
                            <div class="session-meta">
                                <span id="session-duration" class="badge badge-primary">0 min</span>
                                <span id="session-level" class="badge badge-secondary">Beginner</span>
                            </div>
                        </div>
                        <div class="player-controls">
                            <button id="play-pause-btn" onclick="togglePlayPause()" class="btn btn-primary btn-lg">
                                <span id="play-icon">▶️</span> Play
                            </button>
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div id="progress-fill" class="progress-fill"></div>
                                </div>
                                <div class="time-display">
                                    <span id="current-time">0:00</span> / <span id="total-time">0:00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guided Meditation Tab -->
            <div id="guided-tab" class="wellness-tab-content active">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Guided Meditation Sessions</h3>
                            </div>
                            <div class="card-body">
                                <div class="meditation-grid">
                                    <div class="meditation-card" onclick="startSession('morning-calm', 'guided')">
                                        <div class="meditation-image">🌅</div>
                                        <div class="meditation-content">
                                            <h4>Morning Calm</h4>
                                            <p>Start your day with peaceful energy</p>
                                            <div class="meditation-meta">
                                                <span class="duration">10 min</span>
                                                <span class="level">Beginner</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="meditation-card" onclick="startSession('pregnancy-bond', 'guided')">
                                        <div class="meditation-image">👶</div>
                                        <div class="meditation-content">
                                            <h4>Bonding with Baby</h4>
                                            <p>Connect with your growing baby</p>
                                            <div class="meditation-meta">
                                                <span class="duration">15 min</span>
                                                <span class="level">All Levels</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="meditation-card" onclick="startSession('anxiety-relief', 'guided')">
                                        <div class="meditation-image">🌸</div>
                                        <div class="meditation-content">
                                            <h4>Anxiety Relief</h4>
                                            <p>Release worry and find peace</p>
                                            <div class="meditation-meta">
                                                <span class="duration">12 min</span>
                                                <span class="level">Intermediate</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="meditation-card" onclick="startSession('body-scan', 'guided')">
                                        <div class="meditation-image">✨</div>
                                        <div class="meditation-content">
                                            <h4>Body Scan Relaxation</h4>
                                            <p>Release tension throughout your body</p>
                                            <div class="meditation-meta">
                                                <span class="duration">20 min</span>
                                                <span class="level">All Levels</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Your Progress</h3>
                            </div>
                            <div class="card-body">
                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <div class="stat-label">Sessions Completed</div>
                                        <div class="stat-value" id="completed-sessions">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Total Minutes</div>
                                        <div class="stat-value" id="total-minutes">0</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Current Streak</div>
                                        <div class="stat-value" id="current-streak">0 days</div>
                                    </div>
                                </div>
                                <div class="achievement-badges">
                                    <h5>Achievements</h5>
                                    <div id="badges-container">
                                        <!-- Badges will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Breathing Exercises Tab -->
            <div id="breathing-tab" class="wellness-tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Breathing Exercises</h3>
                    </div>
                    <div class="card-body">
                        <div class="breathing-exercises">
                            <div class="breathing-card" onclick="startBreathingExercise('4-7-8')">
                                <div class="breathing-icon">🫁</div>
                                <div class="breathing-content">
                                    <h4>4-7-8 Breathing</h4>
                                    <p>Inhale for 4, hold for 7, exhale for 8</p>
                                    <span class="duration">5-10 min</span>
                                </div>
                            </div>

                            <div class="breathing-card" onclick="startBreathingExercise('box')">
                                <div class="breathing-icon">📦</div>
                                <div class="breathing-content">
                                    <h4>Box Breathing</h4>
                                    <p>Equal counts for inhale, hold, exhale, hold</p>
                                    <span class="duration">5-15 min</span>
                                </div>
                            </div>

                            <div class="breathing-card" onclick="startBreathingExercise('prenatal')">
                                <div class="breathing-icon">🤱</div>
                                <div class="breathing-content">
                                    <h4>Prenatal Breathing</h4>
                                    <p>Gentle breathing for pregnancy</p>
                                    <span class="duration">10-20 min</span>
                                </div>
                            </div>
                        </div>

                        <!-- Breathing Exercise Player -->
                        <div id="breathing-player" class="breathing-player" style="display: none;">
                            <div class="breathing-circle">
                                <div id="breathing-animation" class="breathing-animation">
                                    <div class="circle-inner">
                                        <span id="breathing-instruction">Breathe</span>
                                    </div>
                                </div>
                            </div>
                            <div class="breathing-controls">
                                <button id="breathing-play-btn" onclick="toggleBreathing()" class="btn btn-primary">Start</button>
                                <button onclick="stopBreathing()" class="btn btn-secondary">Stop</button>
                            </div>
                            <div class="breathing-timer">
                                <span id="breathing-time">0:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prenatal Yoga Tab -->
            <div id="prenatal-tab" class="wellness-tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Prenatal Yoga</h3>
                    </div>
                    <div class="card-body">
                        <div class="yoga-sessions">
                            <div class="yoga-card" onclick="startSession('gentle-flow', 'yoga')">
                                <div class="yoga-image">🧘‍♀️</div>
                                <div class="yoga-content">
                                    <h4>Gentle Flow</h4>
                                    <p>Gentle movements for all trimesters</p>
                                    <div class="yoga-meta">
                                        <span class="duration">20 min</span>
                                        <span class="trimester">All Trimesters</span>
                                    </div>
                                </div>
                            </div>

                            <div class="yoga-card" onclick="startSession('hip-opener', 'yoga')">
                                <div class="yoga-image">🤸‍♀️</div>
                                <div class="yoga-content">
                                    <h4>Hip Openers</h4>
                                    <p>Prepare your body for birth</p>
                                    <div class="yoga-meta">
                                        <span class="duration">15 min</span>
                                        <span class="trimester">2nd & 3rd</span>
                                    </div>
                                </div>
                            </div>

                            <div class="yoga-card" onclick="startSession('back-relief', 'yoga')">
                                <div class="yoga-image">🌙</div>
                                <div class="yoga-content">
                                    <h4>Back Pain Relief</h4>
                                    <p>Gentle stretches for back comfort</p>
                                    <div class="yoga-meta">
                                        <span class="duration">12 min</span>
                                        <span class="trimester">All Trimesters</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sleep Stories Tab -->
            <div id="sleep-tab" class="wellness-tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Sleep Stories</h3>
                    </div>
                    <div class="card-body">
                        <div class="sleep-stories">
                            <div class="story-card" onclick="startSession('forest-walk', 'sleep')">
                                <div class="story-image">🌲</div>
                                <div class="story-content">
                                    <h4>Forest Walk</h4>
                                    <p>A peaceful journey through nature</p>
                                    <span class="duration">25 min</span>
                                </div>
                            </div>

                            <div class="story-card" onclick="startSession('ocean-waves', 'sleep')">
                                <div class="story-image">🌊</div>
                                <div class="story-content">
                                    <h4>Ocean Waves</h4>
                                    <p>Drift away with gentle ocean sounds</p>
                                    <span class="duration">30 min</span>
                                </div>
                            </div>

                            <div class="story-card" onclick="startSession('baby-dreams', 'sleep')">
                                <div class="story-image">👶</div>
                                <div class="story-content">
                                    <h4>Dreams of Baby</h4>
                                    <p>Visualize your future with your little one</p>
                                    <span class="duration">20 min</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Relaxing Music Tab -->
            <div id="music-tab" class="wellness-tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Relaxing Music</h3>
                    </div>
                    <div class="card-body">
                        <div class="music-playlists">
                            <div class="playlist-card" onclick="playPlaylist('nature-sounds')">
                                <div class="playlist-image">🎵</div>
                                <div class="playlist-content">
                                    <h4>Nature Sounds</h4>
                                    <p>Rain, birds, and gentle streams</p>
                                    <span class="track-count">12 tracks</span>
                                </div>
                            </div>

                            <div class="playlist-card" onclick="playPlaylist('classical')">
                                <div class="playlist-image">🎼</div>
                                <div class="playlist-content">
                                    <h4>Classical for Pregnancy</h4>
                                    <p>Soothing classical compositions</p>
                                    <span class="track-count">15 tracks</span>
                                </div>
                            </div>

                            <div class="playlist-card" onclick="playPlaylist('ambient')">
                                <div class="playlist-image">✨</div>
                                <div class="playlist-content">
                                    <h4>Ambient Relaxation</h4>
                                    <p>Peaceful ambient soundscapes</p>
                                    <span class="track-count">10 tracks</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Wellness Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Daily Wellness Tip</h3>
                </div>
                <div class="card-body">
                    <div id="daily-tip" class="daily-tip">
                        <div class="tip-icon">💡</div>
                        <div class="tip-content">
                            <h4 id="tip-title">Take Deep Breaths</h4>
                            <p id="tip-description">When feeling overwhelmed, take 5 deep breaths. This simple practice can help reduce stress and bring you back to the present moment.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentSession = null;
        let sessionTimer = null;
        let breathingTimer = null;
        let breathingActive = false;

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProgress();
            loadDailyTip();
        });

        async function loadUserProgress() {
            try {
                const response = await apiCall('/mother/wellness/progress', 'GET');
                if (response.success) {
                    const progress = response.data;
                    document.getElementById('completed-sessions').textContent = progress.completed_sessions || 0;
                    document.getElementById('total-minutes').textContent = progress.total_minutes || 0;
                    document.getElementById('current-streak').textContent = `${progress.current_streak || 0} days`;
                    
                    loadAchievements(progress.achievements || []);
                }
            } catch (error) {
                console.error('Error loading progress:', error);
            }
        }

        function loadAchievements(achievements) {
            const container = document.getElementById('badges-container');
            const allBadges = [
                { id: 'first_session', name: 'First Steps', icon: '🌱', description: 'Complete your first session' },
                { id: 'week_streak', name: 'Week Warrior', icon: '🔥', description: '7 day streak' },
                { id: 'meditation_master', name: 'Zen Master', icon: '🧘‍♀️', description: '50 sessions completed' },
                { id: 'breathing_expert', name: 'Breath Expert', icon: '🫁', description: '25 breathing exercises' }
            ];

            container.innerHTML = allBadges.map(badge => {
                const earned = achievements.includes(badge.id);
                return `
                    <div class="achievement-badge ${earned ? 'earned' : 'locked'}" title="${badge.description}">
                        <div class="badge-icon">${badge.icon}</div>
                        <div class="badge-name">${badge.name}</div>
                    </div>
                `;
            }).join('');
        }

        function loadDailyTip() {
            const tips = [
                {
                    title: "Take Deep Breaths",
                    description: "When feeling overwhelmed, take 5 deep breaths. This simple practice can help reduce stress and bring you back to the present moment."
                },
                {
                    title: "Connect with Your Baby",
                    description: "Spend a few minutes each day talking or singing to your baby. This helps build the bond between you and your little one."
                },
                {
                    title: "Practice Gratitude",
                    description: "Each morning, think of three things you're grateful for. This positive practice can improve your mood and outlook."
                },
                {
                    title: "Gentle Movement",
                    description: "Even light stretching or a short walk can help improve circulation and reduce pregnancy discomfort."
                },
                {
                    title: "Mindful Eating",
                    description: "Take time to savor your meals. Eating mindfully can improve digestion and help you tune into your body's needs."
                }
            ];

            const today = new Date().getDay();
            const tip = tips[today % tips.length];
            
            document.getElementById('tip-title').textContent = tip.title;
            document.getElementById('tip-description').textContent = tip.description;
        }

        function showCategory(category) {
            // Hide all tab contents
            document.querySelectorAll('.wellness-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.category-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab and activate button
            document.getElementById(`${category}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        function startSession(sessionId, type) {
            const sessions = {
                'morning-calm': { title: 'Morning Calm', description: 'Start your day with peaceful energy', duration: 10 },
                'pregnancy-bond': { title: 'Bonding with Baby', description: 'Connect with your growing baby', duration: 15 },
                'anxiety-relief': { title: 'Anxiety Relief', description: 'Release worry and find peace', duration: 12 },
                'body-scan': { title: 'Body Scan Relaxation', description: 'Release tension throughout your body', duration: 20 },
                'gentle-flow': { title: 'Gentle Flow', description: 'Gentle movements for all trimesters', duration: 20 },
                'hip-opener': { title: 'Hip Openers', description: 'Prepare your body for birth', duration: 15 },
                'back-relief': { title: 'Back Pain Relief', description: 'Gentle stretches for back comfort', duration: 12 },
                'forest-walk': { title: 'Forest Walk', description: 'A peaceful journey through nature', duration: 25 },
                'ocean-waves': { title: 'Ocean Waves', description: 'Drift away with gentle ocean sounds', duration: 30 },
                'baby-dreams': { title: 'Dreams of Baby', description: 'Visualize your future with your little one', duration: 20 }
            };

            const session = sessions[sessionId];
            if (!session) return;

            currentSession = { ...session, id: sessionId, type: type };
            
            document.getElementById('session-title').textContent = session.title;
            document.getElementById('session-description').textContent = session.description;
            document.getElementById('session-duration').textContent = `${session.duration} min`;
            document.getElementById('total-time').textContent = `${session.duration}:00`;
            
            document.getElementById('current-session-card').style.display = 'block';
            document.getElementById('current-session-card').scrollIntoView({ behavior: 'smooth' });
        }

        function togglePlayPause() {
            const playBtn = document.getElementById('play-pause-btn');
            const playIcon = document.getElementById('play-icon');
            
            if (sessionTimer) {
                // Pause
                clearInterval(sessionTimer);
                sessionTimer = null;
                playBtn.innerHTML = '<span id="play-icon">▶️</span> Play';
            } else {
                // Play
                playBtn.innerHTML = '<span id="play-icon">⏸️</span> Pause';
                startSessionTimer();
            }
        }

        function startSessionTimer() {
            let currentTime = 0;
            const totalTime = currentSession.duration * 60; // Convert to seconds
            
            sessionTimer = setInterval(() => {
                currentTime++;
                
                const minutes = Math.floor(currentTime / 60);
                const seconds = currentTime % 60;
                document.getElementById('current-time').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                const progress = (currentTime / totalTime) * 100;
                document.getElementById('progress-fill').style.width = `${progress}%`;
                
                if (currentTime >= totalTime) {
                    completeSession();
                }
            }, 1000);
        }

        async function completeSession() {
            clearInterval(sessionTimer);
            sessionTimer = null;
            
            document.getElementById('play-pause-btn').innerHTML = '<span id="play-icon">✅</span> Completed';
            document.getElementById('play-pause-btn').disabled = true;
            
            try {
                await apiCall('/mother/wellness/complete-session', 'POST', {
                    session_id: currentSession.id,
                    session_type: currentSession.type,
                    duration: currentSession.duration
                });
                
                showNotification('Session completed! Great job! 🎉', 'success');
                loadUserProgress();
            } catch (error) {
                console.error('Error recording session completion:', error);
            }
            
            setTimeout(() => {
                document.getElementById('current-session-card').style.display = 'none';
                document.getElementById('play-pause-btn').disabled = false;
                currentSession = null;
            }, 3000);
        }

        function startBreathingExercise(type) {
            const exercises = {
                '4-7-8': { inhale: 4, hold: 7, exhale: 8, name: '4-7-8 Breathing' },
                'box': { inhale: 4, hold: 4, exhale: 4, hold2: 4, name: 'Box Breathing' },
                'prenatal': { inhale: 6, hold: 2, exhale: 8, name: 'Prenatal Breathing' }
            };

            currentBreathingExercise = exercises[type];
            document.getElementById('breathing-player').style.display = 'block';
            document.getElementById('breathing-player').scrollIntoView({ behavior: 'smooth' });
        }

        function toggleBreathing() {
            const btn = document.getElementById('breathing-play-btn');
            
            if (breathingActive) {
                stopBreathing();
            } else {
                breathingActive = true;
                btn.textContent = 'Stop';
                startBreathingCycle();
            }
        }

        function startBreathingCycle() {
            if (!breathingActive) return;
            
            const instruction = document.getElementById('breathing-instruction');
            const animation = document.getElementById('breathing-animation');
            
            // Inhale phase
            instruction.textContent = 'Inhale';
            animation.classList.add('inhale');
            
            setTimeout(() => {
                if (!breathingActive) return;
                
                // Hold phase (if applicable)
                if (currentBreathingExercise.hold) {
                    instruction.textContent = 'Hold';
                    setTimeout(() => {
                        if (!breathingActive) return;
                        
                        // Exhale phase
                        instruction.textContent = 'Exhale';
                        animation.classList.remove('inhale');
                        animation.classList.add('exhale');
                        
                        setTimeout(() => {
                            if (!breathingActive) return;
                            animation.classList.remove('exhale');
                            startBreathingCycle(); // Repeat cycle
                        }, currentBreathingExercise.exhale * 1000);
                        
                    }, currentBreathingExercise.hold * 1000);
                } else {
                    // Direct to exhale
                    instruction.textContent = 'Exhale';
                    animation.classList.remove('inhale');
                    animation.classList.add('exhale');
                    
                    setTimeout(() => {
                        if (!breathingActive) return;
                        animation.classList.remove('exhale');
                        startBreathingCycle();
                    }, currentBreathingExercise.exhale * 1000);
                }
            }, currentBreathingExercise.inhale * 1000);
        }

        function stopBreathing() {
            breathingActive = false;
            document.getElementById('breathing-play-btn').textContent = 'Start';
            document.getElementById('breathing-instruction').textContent = 'Breathe';
            document.getElementById('breathing-animation').classList.remove('inhale', 'exhale');
        }

        function playPlaylist(playlistId) {
            showNotification(`Playing ${playlistId} playlist...`, 'info');
            // Playlist functionality would be implemented here
        }
    </script>
</body>
</html>
