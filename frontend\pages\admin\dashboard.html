<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Content Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            overflow-x: hidden;
            padding-top: 80px;
        }

        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .logo-icon {
            background: linear-gradient(135deg, var(--primary), #ff8a80);
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1.8rem;
            align-items: center;
        }

        .nav-menu a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            color: var(--primary);
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--primary);
            transition: var(--transition);
            border-radius: 10px;
        }

        .nav-menu a:hover::after,
        .nav-menu a.active::after {
            width: 100%;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), #ff8a80);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: linear-gradient(135deg, var(--primary), #ff8a80);
            color: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: rotate(45deg);
        }

        .welcome-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .category-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(135deg, var(--primary), #ff8a80);
        }

        .category-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary);
            box-shadow: 0 8px 30px rgba(233, 30, 99, 0.15);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            background: linear-gradient(135deg, var(--primary), #ff8a80);
            margin-bottom: 0.5rem;
        }

        .category-info h3 {
            font-size: 1.4rem;
            color: var(--dark);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .category-info p {
            color: var(--gray);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .category-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--light-gray);
        }

        .content-count {
            color: var(--gray);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.7rem 1.2rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .btn-secondary {
            background: var(--light-gray);
            color: var(--dark);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-secondary:hover {
            background: #d1d5db;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .btn-success {
            background: var(--secondary);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-success:hover {
            background: var(--secondary-dark);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .btn-danger {
            background: #f56565;
            box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
        }

        .btn-danger:hover {
            background: #e53e3e;
            box-shadow: 0 6px 20px rgba(245, 101, 101, 0.4);
        }

        .btn-icon {
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
        }

        .btn-edit {
            color: var(--accent);
            background: rgba(33, 150, 243, 0.1);
        }

        .btn-edit:hover {
            background: rgba(33, 150, 243, 0.2);
            transform: scale(1.1);
        }

        .btn-delete {
            color: #f56565;
            background: rgba(245, 101, 101, 0.1);
        }

        .btn-delete:hover {
            background: rgba(245, 101, 101, 0.2);
            transform: scale(1.1);
        }

        .content-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
        }

        .content-actions {
            display: flex;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge.active {
            background: rgba(76, 175, 80, 0.1);
            color: var(--secondary);
        }

        .status-badge.inactive {
            background: rgba(245, 101, 101, 0.1);
            color: #f56565;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 3% auto;
            padding: 2.5rem;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 650px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-title {
            font-size: 1.5rem;
            color: #2d3748;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #718096;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #4a5568;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .main-container {
                padding: 1rem;
            }

            .welcome-section {
                padding: 2rem 1.5rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .categories-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .category-card {
                padding: 1.5rem;
            }

            .modal-content {
                margin: 10% auto;
                padding: 1.5rem;
                width: 95%;
            }
        }

        @media (max-width: 480px) {
            .welcome-title {
                font-size: 1.8rem;
            }

            .category-stats {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <span>Admin Dashboard</span>
            </a>

            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.html" class="active">Dashboard</a></li>
                    <li><a href="/">Home</a></li>
                    <li><a href="/content">Health Content</a></li>
                </ul>
            </nav>

            <div class="user-profile">
                <div class="profile-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <span id="admin-name">Administrator</span>
                <a href="/logout" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </header>

    <main class="main-container">
        <div class="welcome-section">
            <h1 class="welcome-title">
                <i class="fas fa-heart" style="margin-right: 0.5rem;"></i>
                Content Management
            </h1>
            <p class="welcome-subtitle">Manage health content for mothers and babies - Create, edit, and organize valuable health information</p>
        </div>

        <div id="categories-container">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i> Loading content categories...
            </div>
        </div>
    </main>

    <!-- Add Content Modal -->
    <div id="addContentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Add New Content</h2>
                <button class="close" onclick="closeModal('addContentModal')">&times;</button>
            </div>
            <form id="addContentForm">
                <div class="form-group">
                    <label class="form-label">Category</label>
                    <select id="contentCategory" class="form-select" required>
                        <option value="">Select Category</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Title</label>
                    <input type="text" id="contentTitle" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <input type="text" id="contentDescription" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label">Content</label>
                    <textarea id="contentText" class="form-textarea" required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Image URL (optional)</label>
                    <input type="url" id="contentImageUrl" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label">Tags (comma-separated)</label>
                    <input type="text" id="contentTags" class="form-input">
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addContentModal')">Cancel</button>
                    <button type="submit" class="btn btn-success">Add Content</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Content List Modal -->
    <div id="contentListModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="contentListTitle">Content List</h2>
                <button class="close" onclick="closeModal('contentListModal')">&times;</button>
            </div>
            <div id="contentListContainer">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading content...
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let categories = [];
        let contentCounts = {};
        let currentCategory = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('addContentForm').addEventListener('submit', handleAddContent);
        }

        // Load content categories
        async function loadCategories() {
            try {
                const response = await fetch('/api/admin/content/categories');
                if (response.ok) {
                    categories = await response.json();
                    await loadContentCounts();
                    displayCategories();
                    populateCategorySelect();
                } else {
                    throw new Error('Failed to load categories');
                }
            } catch (error) {
                console.error('Error loading categories:', error);
                document.getElementById('categories-container').innerHTML =
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load categories</p></div>';
            }
        }

        // Load content counts for each category
        async function loadContentCounts() {
            for (const category of categories) {
                try {
                    const response = await fetch(`/api/admin/content?category=${category.name}`);
                    if (response.ok) {
                        const content = await response.json();
                        contentCounts[category.name] = content.length;
                    }
                } catch (error) {
                    console.error(`Error loading content count for ${category.name}:`, error);
                    contentCounts[category.name] = 0;
                }
            }
        }

        // Display categories
        function displayCategories() {
            const container = document.getElementById('categories-container');

            if (categories.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>No categories found</p></div>';
                return;
            }

            const categoriesHTML = `
                <div class="categories-grid">
                    ${categories.map(category => `
                        <div class="category-card" onclick="viewCategoryContent('${category.name}', '${category.display_name}')">
                            <div class="category-header">
                                <div class="category-icon">
                                    <i class="${category.icon}"></i>
                                </div>
                            </div>
                            <div class="category-info">
                                <h3>${category.display_name}</h3>
                                <p>${category.description}</p>
                            </div>
                            <div class="category-stats">
                                <span class="content-count">${contentCounts[category.name] || 0} items published</span>
                                <button class="btn btn-success" onclick="event.stopPropagation(); addContent('${category.name}')">
                                    <i class="fas fa-plus"></i> Add Content
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            container.innerHTML = categoriesHTML;
        }

        // Populate category select dropdown
        function populateCategorySelect() {
            const select = document.getElementById('contentCategory');
            select.innerHTML = '<option value="">Select Category</option>';

            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.name;
                option.textContent = category.display_name;
                select.appendChild(option);
            });
        }

        // Add new content
        function addContent(categoryName = null) {
            if (categoryName) {
                document.getElementById('contentCategory').value = categoryName;
            }
            openModal('addContentModal');
        }

        // View category content
        async function viewCategoryContent(categoryName, displayName) {
            currentCategory = categoryName;
            document.getElementById('contentListTitle').textContent = `${displayName} Content`;
            openModal('contentListModal');

            try {
                const response = await fetch(`/api/admin/content?category=${categoryName}`);
                if (response.ok) {
                    const content = await response.json();
                    displayContentList(content);
                } else {
                    throw new Error('Failed to load content');
                }
            } catch (error) {
                console.error('Error loading content:', error);
                document.getElementById('contentListContainer').innerHTML =
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load content</p></div>';
            }
        }

        // Display content list
        function displayContentList(content) {
            const container = document.getElementById('contentListContainer');

            if (content.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-file-alt"></i><p>No content found for this category</p></div>';
                return;
            }

            const contentHTML = `
                <div style="display: flex; flex-direction: column; gap: 1rem;">
                    ${content.map(item => `
                        <div style="background: white; padding: 1.5rem; border-radius: 12px; border-left: 4px solid var(--primary); box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                            <div class="content-item-header">
                                <h4 style="color: var(--dark); margin: 0; font-size: 1.1rem;">${item.title}</h4>
                                <div class="content-actions">
                                    <button class="btn-icon btn-edit" onclick="editContent(${item.id})" title="Edit Content">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn-icon btn-delete" onclick="deleteContent(${item.id}, '${item.title.replace(/'/g, "\\'")}', '${item.category}')" title="Delete Content">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            ${item.description ? `<p style="color: var(--gray); margin: 0.5rem 0; font-size: 0.95rem;">${item.description}</p>` : ''}
                            <p style="color: var(--dark); margin: 0.5rem 0; font-size: 0.9rem; line-height: 1.5;">${item.content.substring(0, 200)}${item.content.length > 200 ? '...' : ''}</p>
                            <div style="margin-top: 1rem; font-size: 0.85rem; color: var(--gray); display: flex; justify-content: space-between; align-items: center;">
                                <span>Created: ${new Date(item.created_at).toLocaleDateString()}${item.tags ? ` | Tags: ${item.tags}` : ''}</span>
                                <span class="status-badge ${item.is_active ? 'active' : 'inactive'}">
                                    ${item.is_active ? 'Published' : 'Draft'}
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            container.innerHTML = contentHTML;
        }

        // Handle add content form submission
        async function handleAddContent(event) {
            event.preventDefault();

            const formData = {
                category: document.getElementById('contentCategory').value,
                title: document.getElementById('contentTitle').value,
                description: document.getElementById('contentDescription').value,
                content: document.getElementById('contentText').value,
                image_url: document.getElementById('contentImageUrl').value,
                tags: document.getElementById('contentTags').value
            };

            try {
                const response = await fetch('/api/admin/content', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    alert('Content added successfully!');
                    closeModal('addContentModal');
                    document.getElementById('addContentForm').reset();
                    loadCategories(); // Refresh the dashboard
                } else {
                    const error = await response.json();
                    alert(`Error: ${error.error}`);
                }
            } catch (error) {
                console.error('Error adding content:', error);
                alert('Failed to add content. Please try again.');
            }
        }

        // Delete content with enhanced confirmation
        async function deleteContent(contentId, contentTitle, categoryName) {
            // Enhanced confirmation dialog
            const confirmMessage = `⚠️ DELETE CONTENT CONFIRMATION ⚠️\n\n` +
                                 `Title: "${contentTitle}"\n` +
                                 `Category: ${categoryName}\n\n` +
                                 `This action will:\n` +
                                 `• Permanently delete this content\n` +
                                 `• Remove it from all user views immediately\n` +
                                 `• Cannot be undone\n\n` +
                                 `Are you absolutely sure you want to delete this content?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            // Show loading state
            const deleteButtons = document.querySelectorAll('.btn-delete');
            deleteButtons.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            });

            try {
                const response = await fetch(`/api/admin/content/${contentId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    // Success notification
                    showNotification('✅ Content deleted successfully! Changes are now live for all users.', 'success');

                    // Refresh the content list immediately
                    if (currentCategory) {
                        const categoryDisplay = categories.find(c => c.name === currentCategory)?.display_name || currentCategory;
                        viewCategoryContent(currentCategory, categoryDisplay);
                    }

                    // Refresh the dashboard to update counts
                    loadCategories();
                } else {
                    const error = await response.json();
                    showNotification(`❌ Error: ${error.error}`, 'error');
                }
            } catch (error) {
                console.error('Error deleting content:', error);
                showNotification('❌ Failed to delete content. Please check your connection and try again.', 'error');
            } finally {
                // Restore delete buttons
                deleteButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-trash"></i>';
                });
            }
        }

        // Enhanced notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span>${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Add notification styles
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#667eea'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(0,0,0,0.2);
                z-index: 3000;
                max-width: 400px;
                animation: slideInRight 0.3s ease-out;
            `;

            // Add animation styles
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                .notification-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 1rem;
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 0.25rem;
                    border-radius: 50%;
                    transition: background 0.2s ease;
                }
                .notification-close:hover {
                    background: rgba(255,255,255,0.2);
                }
            `;
            document.head.appendChild(style);

            // Add to page
            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Edit content function (placeholder for future enhancement)
        function editContent(contentId) {
            showNotification('📝 Edit functionality coming soon! For now, you can delete and recreate content.', 'info');
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
