<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exercise Guides - Preg & Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #e91e63;
            --secondary: #2196f3;
            --accent: #ff9800;
            --success: #4caf50;
            --warning: #ff5722;
            --light-gray: #f5f5f5;
            --gray: #9e9e9e;
            --dark: #333;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: var(--primary);
            font-weight: bold;
            font-size: 1.5rem;
        }

        .logo-icon {
            background: var(--primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn {
            background: var(--light-gray);
            color: var(--dark);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .back-btn:hover {
            background: var(--gray);
            color: white;
        }

        /* Main Content */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-title {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Exercise Categories */
        .exercise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .exercise-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: var(--transition);
            cursor: pointer;
        }

        .exercise-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark);
        }

        .exercise-list {
            list-style: none;
        }

        .exercise-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .exercise-list li:last-child {
            border-bottom: none;
        }

        .exercise-name {
            font-weight: 500;
        }

        .exercise-duration {
            color: var(--primary);
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Safety Guidelines */
        .safety-section {
            background: linear-gradient(135deg, var(--warning), #ff7043);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
        }

        .safety-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .safety-list {
            list-style: none;
            max-width: 800px;
        }

        .safety-list li {
            padding: 0.5rem 0;
            font-size: 1.1rem;
        }

        .safety-list li::before {
            content: "⚠️ ";
            margin-right: 0.5rem;
        }

        /* Benefits Section */
        .benefits-section {
            background: linear-gradient(135deg, var(--success), #66bb6a);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 2rem;
        }

        .benefits-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .benefit-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .benefit-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        /* Weekly Plan */
        .weekly-plan {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .plan-title {
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            text-align: center;
        }

        .week-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .day-card {
            background: var(--light-gray);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .day-name {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .day-activity {
            font-size: 0.9rem;
            color: var(--dark);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .exercise-grid {
                grid-template-columns: 1fr;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
            }

            .week-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="pregcare.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Pregnancy Care
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-running"></i>
                Exercise Guides
            </h1>
            <p class="page-subtitle">
                Safe and effective exercises for a healthy pregnancy
            </p>
        </div>

        <!-- Exercise Categories -->
        <div class="exercise-grid">
            <!-- Low Impact Cardio -->
            <div class="exercise-card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--primary);">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="card-title">Low Impact Cardio</h3>
                </div>
                <ul class="exercise-list">
                    <li>
                        <span class="exercise-name">Walking</span>
                        <span class="exercise-duration">30 min</span>
                    </li>
                    <li>
                        <span class="exercise-name">Swimming</span>
                        <span class="exercise-duration">20-30 min</span>
                    </li>
                    <li>
                        <span class="exercise-name">Stationary Cycling</span>
                        <span class="exercise-duration">20-25 min</span>
                    </li>
                    <li>
                        <span class="exercise-name">Water Aerobics</span>
                        <span class="exercise-duration">30 min</span>
                    </li>
                </ul>
            </div>

            <!-- Strength Training -->
            <div class="exercise-card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--secondary);">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h3 class="card-title">Strength Training</h3>
                </div>
                <ul class="exercise-list">
                    <li>
                        <span class="exercise-name">Modified Squats</span>
                        <span class="exercise-duration">10-15 reps</span>
                    </li>
                    <li>
                        <span class="exercise-name">Wall Push-ups</span>
                        <span class="exercise-duration">8-12 reps</span>
                    </li>
                    <li>
                        <span class="exercise-name">Arm Circles</span>
                        <span class="exercise-duration">10 each way</span>
                    </li>
                    <li>
                        <span class="exercise-name">Leg Lifts</span>
                        <span class="exercise-duration">10-15 reps</span>
                    </li>
                </ul>
            </div>

            <!-- Flexibility & Yoga -->
            <div class="exercise-card">
                <div class="card-header">
                    <div class="card-icon" style="background: var(--success);">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="card-title">Flexibility & Yoga</h3>
                </div>
                <ul class="exercise-list">
                    <li>
                        <span class="exercise-name">Prenatal Yoga</span>
                        <span class="exercise-duration">30-45 min</span>
                    </li>
                    <li>
                        <span class="exercise-name">Cat-Cow Stretch</span>
                        <span class="exercise-duration">5-10 reps</span>
                    </li>
                    <li>
                        <span class="exercise-name">Pelvic Tilts</span>
                        <span class="exercise-duration">10-15 reps</span>
                    </li>
                    <li>
                        <span class="exercise-name">Deep Breathing</span>
                        <span class="exercise-duration">5-10 min</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Safety Guidelines -->
        <div class="safety-section">
            <h3 class="safety-title">
                <i class="fas fa-shield-alt"></i>
                Safety Guidelines
            </h3>
            <ul class="safety-list">
                <li>Always consult your doctor before starting any exercise program</li>
                <li>Stay hydrated and avoid overheating</li>
                <li>Avoid exercises lying flat on your back after first trimester</li>
                <li>Stop immediately if you feel dizzy, short of breath, or have chest pain</li>
                <li>Avoid contact sports and activities with fall risk</li>
                <li>Listen to your body and don't push beyond comfort</li>
            </ul>
        </div>

        <!-- Benefits Section -->
        <div class="benefits-section">
            <h3 class="benefits-title">Benefits of Exercise During Pregnancy</h3>
            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">💪</div>
                    <div>Improved Strength</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">❤️</div>
                    <div>Better Circulation</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">😴</div>
                    <div>Better Sleep</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🧘</div>
                    <div>Reduced Stress</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">⚡</div>
                    <div>More Energy</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🤱</div>
                    <div>Easier Labor</div>
                </div>
            </div>
        </div>
</body>
</html>
