<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Unique ID - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #e8f5e8 0%, #fff3e0 100%);
            min-height: 100vh;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: var(--primary);
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .logo-icon {
            background: var(--primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .back-btn {
            background: var(--light-gray);
            color: var(--dark);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }
        
        .back-btn:hover {
            background: var(--gray);
            color: white;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: var(--secondary);
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .id-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .id-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary), var(--accent));
        }
        
        .id-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--light-gray);
        }
        
        .id-title {
            font-size: 1.8rem;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }
        
        .id-subtitle {
            color: var(--gray);
        }
        
        .baby-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
        }
        
        .id-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .detail-label {
            font-weight: 600;
            color: var(--gray);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .detail-value {
            font-size: 1.1rem;
            color: var(--dark);
            font-weight: 500;
        }
        
        .unique-id-display {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin: 2rem 0;
        }
        
        .unique-id-label {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }
        
        .unique-id-value {
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 2px;
            font-family: 'Courier New', monospace;
        }
        
        .qr-code {
            width: 150px;
            height: 150px;
            background: white;
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 4rem;
            color: var(--gray);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-btn {
            background: var(--secondary);
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 12px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: var(--transition);
            font-weight: 500;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: var(--secondary-dark);
            transform: translateY(-2px);
        }
        
        .action-btn.primary {
            background: var(--primary);
        }
        
        .action-btn.primary:hover {
            background: var(--primary-dark);
        }
        
        .action-btn.accent {
            background: var(--accent);
        }
        
        .action-btn.accent:hover {
            background: #1976d2;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .id-details {
                grid-template-columns: 1fr;
            }
            
            .unique-id-value {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="baby-care.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Baby Care
            </a>
        </div>
    </header>

    <main class="main-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-id-card"></i>
                Baby Unique ID
            </h1>
            <p>Digital identification and records for your little one</p>
        </div>

        <div class="id-card">
            <div class="id-header">
                <h2 class="id-title">Official Baby ID Card</h2>
                <p class="id-subtitle">Digital Health & Identity Record</p>
            </div>

            <div style="text-align: center; margin-bottom: 2rem;">
                <div class="baby-photo">
                    <i class="fas fa-baby"></i>
                </div>
            </div>

            <div class="id-details">
                <div class="detail-item">
                    <span class="detail-label">Full Name</span>
                    <span class="detail-value" id="babyName">Emma Rose Johnson</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Date of Birth</span>
                    <span class="detail-value" id="birthDate">July 15, 2023</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Gender</span>
                    <span class="detail-value" id="gender">Female</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Birth Weight</span>
                    <span class="detail-value" id="birthWeight">3.2 kg</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Birth Length</span>
                    <span class="detail-value" id="birthLength">50 cm</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Blood Type</span>
                    <span class="detail-value" id="bloodType">O+</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Hospital</span>
                    <span class="detail-value" id="hospital">City General Hospital</span>
                </div>
                
                <div class="detail-item">
                    <span class="detail-label">Doctor</span>
                    <span class="detail-value" id="doctor">Dr. Sarah Wilson</span>
                </div>
            </div>

            <div class="unique-id-display">
                <div class="unique-id-label">Unique Baby ID</div>
                <div class="unique-id-value" id="uniqueId">BB-2023-ERJ-7845</div>
            </div>

            <div style="text-align: center; margin: 2rem 0;">
                <div class="qr-code">
                    <i class="fas fa-qrcode"></i>
                </div>
                <p style="margin-top: 1rem; color: var(--gray); font-size: 0.9rem;">
                    QR Code for quick access to baby's digital records
                </p>
            </div>

            <div class="actions-grid">
                <button class="action-btn primary" onclick="downloadId()">
                    <i class="fas fa-download"></i>
                    Download ID Card
                </button>
                
                <button class="action-btn accent" onclick="shareId()">
                    <i class="fas fa-share"></i>
                    Share ID
                </button>
                
                <button class="action-btn" onclick="editDetails()">
                    <i class="fas fa-edit"></i>
                    Edit Details
                </button>
                
                <button class="action-btn" onclick="printId()">
                    <i class="fas fa-print"></i>
                    Print ID Card
                </button>
            </div>
        </div>

        <!-- Additional Information -->
        <div style="background: white; border-radius: 16px; padding: 2rem; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <h3 style="color: var(--dark); margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i>
                About Baby Unique ID
            </h3>
            <div style="color: var(--gray); line-height: 1.6;">
                <p style="margin-bottom: 1rem;">
                    The Baby Unique ID is a secure digital identifier that helps maintain comprehensive health records, 
                    vaccination history, and important medical information for your child.
                </p>
                <ul style="margin-left: 1.5rem;">
                    <li>Secure and encrypted digital storage</li>
                    <li>Easy access for healthcare providers</li>
                    <li>Complete vaccination and health history</li>
                    <li>Emergency contact information</li>
                    <li>Growth and development tracking</li>
                </ul>
            </div>
        </div>
    </main>

    <script>
        function downloadId() {
            alert('ID Card download feature will be implemented. This will generate a PDF version of the baby\'s ID card.');
        }

        function shareId() {
            if (navigator.share) {
                navigator.share({
                    title: 'Baby ID Card',
                    text: 'Check out my baby\'s digital ID card',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('ID card link copied to clipboard!');
                });
            }
        }

        function editDetails() {
            alert('Edit details feature will open a form to update baby information.');
        }

        function printId() {
            window.print();
        }

        // Generate random ID on page load (in real app, this would come from database)
        document.addEventListener('DOMContentLoaded', function() {
            const currentYear = new Date().getFullYear();
            const randomNum = Math.floor(Math.random() * 9000) + 1000;
            const babyName = document.getElementById('babyName').textContent;
            const initials = babyName.split(' ').map(name => name.charAt(0)).join('');
            
            const uniqueId = `BB-${currentYear}-${initials}-${randomNum}`;
            document.getElementById('uniqueId').textContent = uniqueId;
        });
    </script>
</body>
</html>
