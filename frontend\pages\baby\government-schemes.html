<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Schemes for Babies - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: #e91e63;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .logo-icon {
            background: #e91e63;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn {
            background: #f5f5f5;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e91e63;
            color: white;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .schemes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .scheme-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(233, 30, 99, 0.1);
        }

        .scheme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .scheme-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .scheme-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .scheme-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .scheme-category {
            font-size: 0.9rem;
            color: #718096;
            background: #f7fafc;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            display: inline-block;
        }

        .scheme-description {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .scheme-benefits {
            margin-bottom: 1.5rem;
        }

        .benefits-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .benefits-list {
            list-style: none;
        }

        .benefits-list li {
            padding: 0.25rem 0;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .benefits-list li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
        }

        .scheme-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #ad1457, #880e4f);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #e91e63;
            border: 2px solid #e91e63;
        }

        .btn-outline:hover {
            background: #e91e63;
            color: white;
        }

        .age-categories {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .age-tab {
            padding: 0.75rem 1.5rem;
            background: white;
            border: 2px solid #e91e63;
            border-radius: 25px;
            color: #e91e63;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .age-tab.active,
        .age-tab:hover {
            background: #e91e63;
            color: white;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .schemes-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .scheme-card {
                padding: 1.5rem;
            }

            .scheme-actions {
                flex-direction: column;
            }

            .btn {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="baby-care.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Baby Care
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-hands-helping"></i>
                Government Schemes for Babies
            </h1>
            <p class="page-subtitle">
                Comprehensive guide to government benefits and support programs for babies and children
            </p>
        </div>

        <!-- Age Categories -->
        <div class="age-categories">
            <a href="#" class="age-tab active" onclick="filterByAge('all')">All Ages</a>
            <a href="#" class="age-tab" onclick="filterByAge('0-2')">0-2 Years</a>
            <a href="#" class="age-tab" onclick="filterByAge('2-6')">2-6 Years</a>
            <a href="#" class="age-tab" onclick="filterByAge('6-18')">6-18 Years</a>
        </div>

        <!-- Government Schemes Grid -->
        <div class="schemes-grid" id="schemes-container">
            <!-- Birth Certificate Registration -->
            <div class="scheme-card" data-age="0-2">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #4caf50, #2e7d32);">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Birth Certificate Registration</h3>
                        <span class="scheme-category">Legal Documentation</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Free birth registration within 21 days of birth to obtain official birth certificate.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Legal proof of birth and citizenship</li>
                        <li>Required for school admission</li>
                        <li>Access to government schemes</li>
                        <li>Passport and other document applications</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('BIRTH_CERT')">Register Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('BIRTH_CERT')">View Details</a>
                </div>
            </div>

            <!-- Aadhaar Card for Children -->
            <div class="scheme-card" data-age="0-2">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #2196f3, #1565c0);">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Aadhaar Card for Children</h3>
                        <span class="scheme-category">Identity Document</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Blue Aadhaar card for children under 5 years, upgradeable to regular Aadhaar at age 5 and 15.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Unique identity for children</li>
                        <li>Access to government services</li>
                        <li>School admission and scholarships</li>
                        <li>Banking and financial services</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('CHILD_AADHAAR')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('CHILD_AADHAAR')">View Details</a>
                </div>
            </div>

            <!-- Sukanya Samriddhi Yojana -->
            <div class="scheme-card" data-age="0-2">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #ff9800, #f57c00);">
                        <i class="fas fa-piggy-bank"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Sukanya Samriddhi Yojana</h3>
                        <span class="scheme-category">Savings Scheme</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Government savings scheme for girl children with attractive interest rates and tax benefits.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>High interest rate (currently 8.2%)</li>
                        <li>Tax deduction under Section 80C</li>
                        <li>Tax-free maturity amount</li>
                        <li>Partial withdrawal for higher education</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('SSY')">Open Account</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('SSY')">View Details</a>
                </div>
            </div>

            <!-- Mid Day Meal Scheme -->
            <div class="scheme-card" data-age="2-6">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #9c27b0, #6a1b9a);">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Mid Day Meal Scheme</h3>
                        <span class="scheme-category">Nutrition</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Free nutritious meals for children in government and aided schools to improve nutrition and school attendance.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Free nutritious hot meals</li>
                        <li>Improved school attendance</li>
                        <li>Better nutritional status</li>
                        <li>Reduced classroom hunger</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('MDM')">Enroll Child</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('MDM')">View Details</a>
                </div>
            </div>

            <!-- Sarva Shiksha Abhiyan -->
            <div class="scheme-card" data-age="6-18">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #f44336, #c62828);">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Sarva Shiksha Abhiyan</h3>
                        <span class="scheme-category">Education</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Universal elementary education program ensuring free and compulsory education for children aged 6-14 years.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Free elementary education</li>
                        <li>Free textbooks and uniforms</li>
                        <li>Infrastructure development</li>
                        <li>Teacher training programs</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('SSA')">Enroll Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('SSA')">View Details</a>
                </div>
            </div>

            <!-- Beti Bachao Beti Padhao -->
            <div class="scheme-card" data-age="0-2">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #e91e63, #ad1457);">
                        <i class="fas fa-female"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Beti Bachao Beti Padhao</h3>
                        <span class="scheme-category">Girl Child Welfare</span>
                    </div>
                </div>
                <p class="scheme-description">
                    National initiative to save and educate girl children, addressing declining child sex ratio and promoting girls' education.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Awareness campaigns for girl child rights</li>
                        <li>Educational scholarships</li>
                        <li>Healthcare support</li>
                        <li>Women empowerment programs</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('BBBP')">Learn More</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('BBBP')">View Details</a>
                </div>
            </div>

            <!-- National Child Health Program -->
            <div class="scheme-card" data-age="0-2">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #00bcd4, #0097a7);">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">National Child Health Program</h3>
                        <span class="scheme-category">Healthcare</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Comprehensive healthcare program for children including immunization, nutrition, and treatment services.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Free immunization services</li>
                        <li>Regular health check-ups</li>
                        <li>Treatment for childhood diseases</li>
                        <li>Nutritional support</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('NCHP')">Register Child</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('NCHP')">View Details</a>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Age filtering functionality
        function filterByAge(ageGroup) {
            const cards = document.querySelectorAll('.scheme-card');
            const tabs = document.querySelectorAll('.age-tab');

            // Update active tab
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // Filter cards
            cards.forEach(card => {
                if (ageGroup === 'all' || card.dataset.age === ageGroup) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Scheme application functionality
        function applyForScheme(schemeCode) {
            showNotification(`Redirecting to ${schemeCode} application portal...`, 'info');

            setTimeout(() => {
                showNotification(`Application for ${schemeCode} initiated. Please complete the process on the official portal.`, 'success');
            }, 2000);
        }

        function viewDetails(schemeCode) {
            const schemeDetails = {
                'BIRTH_CERT': {
                    title: 'Birth Certificate Registration',
                    details: 'Register your child\'s birth within 21 days at the local registrar office or online portal.',
                    documents: 'Hospital discharge summary, parents\' identity proof, address proof.'
                },
                'CHILD_AADHAAR': {
                    title: 'Aadhaar Card for Children',
                    details: 'Blue Aadhaar for children under 5, regular Aadhaar after biometric update at 5 and 15 years.',
                    documents: 'Birth certificate, parent\'s Aadhaar, address proof, child\'s photograph.'
                },
                'SSY': {
                    title: 'Sukanya Samriddhi Yojana',
                    details: 'Minimum deposit ₹250, maximum ₹1.5 lakh per year. Account matures after 21 years.',
                    documents: 'Girl child\'s birth certificate, parent\'s identity and address proof, passport-size photos.'
                },
                'MDM': {
                    title: 'Mid Day Meal Scheme',
                    details: 'Free meals provided in government and aided schools for children in classes I-VIII.',
                    documents: 'School enrollment certificate, caste certificate (if applicable).'
                },
                'SSA': {
                    title: 'Sarva Shiksha Abhiyan',
                    details: 'Free and compulsory education for children aged 6-14 years in neighborhood schools.',
                    documents: 'Birth certificate, address proof, caste certificate (if applicable).'
                },
                'BBBP': {
                    title: 'Beti Bachao Beti Padhao',
                    details: 'Multi-sectoral action plan for survival, protection and education of girl children.',
                    documents: 'Girl child\'s birth certificate, family income certificate.'
                },
                'NCHP': {
                    title: 'National Child Health Program',
                    details: 'Comprehensive healthcare including immunization, growth monitoring, and treatment.',
                    documents: 'Birth certificate, immunization card, parent\'s identity proof.'
                }
            };

            const scheme = schemeDetails[schemeCode];
            if (scheme) {
                alert(`${scheme.title}\n\nDetails: ${scheme.details}\n\nRequired Documents: ${scheme.documents}`);
            }
        }

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            switch(type) {
                case 'success':
                    notification.style.backgroundColor = '#4caf50';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#f44336';
                    break;
                case 'info':
                    notification.style.backgroundColor = '#2196f3';
                    break;
                default:
                    notification.style.backgroundColor = '#333';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
