<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nutrition Guide - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">🤱 MCHS - Nutrition Guide</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Nutrition Guide & Tracking</h1>
                <div class="page-actions">
                    <button onclick="logMeal()" class="btn btn-primary">Log Meal</button>
                    <button onclick="viewNutritionPlan()" class="btn btn-outline">View Nutrition Plan</button>
                </div>
            </div>

            <!-- Nutrition Tabs -->
            <div class="tabs">
                <button class="tab-button active" onclick="showTab('daily')">Daily Tracking</button>
                <button class="tab-button" onclick="showTab('guidelines')">Nutrition Guidelines</button>
                <button class="tab-button" onclick="showTab('meal-plans')">Meal Plans</button>
                <button class="tab-button" onclick="showTab('supplements')">Supplements</button>
                <button class="tab-button" onclick="showTab('water')">Water Intake</button>
            </div>

            <!-- Daily Tracking Tab -->
            <div id="daily-tab" class="tab-content active">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Today's Meals -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Today's Meals - <span id="current-date"></span></h3>
                                <button onclick="logMeal()" class="btn btn-sm btn-primary">Add Meal</button>
                            </div>
                            <div class="card-body">
                                <div id="todays-meals">
                                    <!-- Today's meals will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Nutrition History -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Nutrition History</h3>
                                <div class="date-picker">
                                    <input type="date" id="history-date" class="form-control" onchange="loadNutritionHistory()">
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="nutrition-history">
                                    <!-- Nutrition history will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Daily Nutrition Summary -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Daily Summary</h3>
                            </div>
                            <div class="card-body">
                                <div class="nutrition-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Calories</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="calories-progress"></div>
                                        </div>
                                        <span class="stat-value" id="calories-value">0 / 2200 kcal</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Protein</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="protein-progress"></div>
                                        </div>
                                        <span class="stat-value" id="protein-value">0 / 75g</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Carbs</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="carbs-progress"></div>
                                        </div>
                                        <span class="stat-value" id="carbs-value">0 / 300g</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Fat</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="fat-progress"></div>
                                        </div>
                                        <span class="stat-value" id="fat-value">0 / 75g</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Fiber</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="fiber-progress"></div>
                                        </div>
                                        <span class="stat-value" id="fiber-value">0 / 25g</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Water Intake -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Water Intake</h3>
                            </div>
                            <div class="card-body">
                                <div class="water-tracker">
                                    <div class="water-visual">
                                        <div class="water-bottle">
                                            <div class="water-level" id="water-level"></div>
                                        </div>
                                    </div>
                                    <div class="water-stats">
                                        <p><span id="water-consumed">0</span> / <span id="water-goal">2500</span> ml</p>
                                        <div class="water-actions">
                                            <button onclick="addWater(250)" class="btn btn-sm btn-outline">+250ml</button>
                                            <button onclick="addWater(500)" class="btn btn-sm btn-outline">+500ml</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Tips -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Today's Tip</h3>
                            </div>
                            <div class="card-body">
                                <div id="daily-tip">
                                    <!-- Daily nutrition tip will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Nutrition Guidelines Tab -->
            <div id="guidelines-tab" class="tab-content">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Essential Nutrients</h3>
                            </div>
                            <div class="card-body">
                                <div class="nutrient-guide">
                                    <div class="nutrient-item">
                                        <h4>Folic Acid</h4>
                                        <p><strong>Daily Need:</strong> 600 mcg</p>
                                        <p><strong>Sources:</strong> Leafy greens, citrus fruits, fortified cereals</p>
                                        <p><strong>Benefits:</strong> Prevents birth defects, supports DNA synthesis</p>
                                    </div>
                                    <div class="nutrient-item">
                                        <h4>Iron</h4>
                                        <p><strong>Daily Need:</strong> 27 mg</p>
                                        <p><strong>Sources:</strong> Red meat, poultry, fish, beans, spinach</p>
                                        <p><strong>Benefits:</strong> Prevents anemia, supports oxygen transport</p>
                                    </div>
                                    <div class="nutrient-item">
                                        <h4>Calcium</h4>
                                        <p><strong>Daily Need:</strong> 1000 mg</p>
                                        <p><strong>Sources:</strong> Dairy products, leafy greens, fortified foods</p>
                                        <p><strong>Benefits:</strong> Builds strong bones and teeth</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Foods to Avoid</h3>
                            </div>
                            <div class="card-body">
                                <div class="avoid-foods">
                                    <div class="avoid-item">
                                        <h4>Raw or Undercooked Foods</h4>
                                        <ul>
                                            <li>Raw fish (sushi, sashimi)</li>
                                            <li>Undercooked meat and poultry</li>
                                            <li>Raw eggs</li>
                                            <li>Unpasteurized dairy products</li>
                                        </ul>
                                    </div>
                                    <div class="avoid-item">
                                        <h4>High Mercury Fish</h4>
                                        <ul>
                                            <li>Shark</li>
                                            <li>Swordfish</li>
                                            <li>King mackerel</li>
                                            <li>Tilefish</li>
                                        </ul>
                                    </div>
                                    <div class="avoid-item">
                                        <h4>Other Restrictions</h4>
                                        <ul>
                                            <li>Alcohol</li>
                                            <li>Excessive caffeine (>200mg/day)</li>
                                            <li>Unwashed fruits and vegetables</li>
                                            <li>Processed deli meats</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Meal Plans Tab -->
            <div id="meal-plans-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Recommended Meal Plans</h3>
                        <div class="meal-plan-filters">
                            <select id="trimester-filter" class="form-control" onchange="filterMealPlans()">
                                <option value="">All Trimesters</option>
                                <option value="first">First Trimester</option>
                                <option value="second">Second Trimester</option>
                                <option value="third">Third Trimester</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="meal-plans-list">
                            <!-- Meal plans will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Supplements Tab -->
            <div id="supplements-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Prenatal Supplements</h3>
                        <button onclick="addSupplement()" class="btn btn-sm btn-primary">Add Supplement</button>
                    </div>
                    <div class="card-body">
                        <div id="supplements-list">
                            <!-- Supplements will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Water Intake Tab -->
            <div id="water-tab" class="tab-content">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Water Intake History</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="water-chart" width="600" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Hydration Tips</h3>
                            </div>
                            <div class="card-body">
                                <div class="hydration-tips">
                                    <ul>
                                        <li>Drink water before you feel thirsty</li>
                                        <li>Keep a water bottle with you</li>
                                        <li>Add lemon or cucumber for flavor</li>
                                        <li>Eat water-rich foods like watermelon</li>
                                        <li>Set reminders to drink water</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Log Meal Modal -->
    <div id="meal-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Log Meal</h3>
                <span class="close" onclick="closeMealModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="meal-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="meal_type">Meal Type</label>
                                <select id="meal_type" name="meal_type" class="form-control" required>
                                    <option value="">Select Meal Type</option>
                                    <option value="breakfast">Breakfast</option>
                                    <option value="lunch">Lunch</option>
                                    <option value="dinner">Dinner</option>
                                    <option value="snack">Snack</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="meal_time">Time</label>
                                <input type="time" id="meal_time" name="meal_time" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="food_items">Food Items</label>
                        <textarea id="food_items" name="food_items" class="form-control" rows="3" required placeholder="List the foods you ate..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="calories">Calories (optional)</label>
                                <input type="number" id="calories" name="calories" class="form-control" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="portion_size">Portion Size</label>
                                <select id="portion_size" name="portion_size" class="form-control">
                                    <option value="small">Small</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="large">Large</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="meal_notes">Notes</label>
                        <textarea id="meal_notes" name="notes" class="form-control" rows="2" placeholder="How did you feel after eating?"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Log Meal</button>
                        <button type="button" onclick="closeMealModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let dailyNutrition = {
            calories: 0,
            protein: 0,
            carbs: 0,
            fat: 0,
            fiber: 0
        };

        let waterConsumed = 0;
        const waterGoal = 2500; // ml

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('current-date').textContent = new Date().toLocaleDateString();
            document.getElementById('history-date').value = new Date().toISOString().split('T')[0];
            
            loadTodaysMeals();
            loadDailyNutrition();
            loadWaterIntake();
            loadDailyTip();
            updateNutritionDisplay();
            updateWaterDisplay();
        });

        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab and mark button as active
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        async function loadTodaysMeals() {
            try {
                const today = new Date().toISOString().split('T')[0];
                const response = await apiCall(`/mother/nutrition/meals?date=${today}`, 'GET');
                if (response.success) {
                    displayTodaysMeals(response.data);
                }
            } catch (error) {
                console.error('Error loading meals:', error);
            }
        }

        function displayTodaysMeals(meals) {
            const container = document.getElementById('todays-meals');
            
            if (meals.length === 0) {
                container.innerHTML = '<p class="text-secondary">No meals logged today. Start by adding your first meal!</p>';
                return;
            }

            const mealsByType = {
                breakfast: [],
                lunch: [],
                dinner: [],
                snack: []
            };

            meals.forEach(meal => {
                mealsByType[meal.meal_type].push(meal);
            });

            container.innerHTML = Object.keys(mealsByType).map(type => {
                const typeMeals = mealsByType[type];
                if (typeMeals.length === 0) return '';

                return `
                    <div class="meal-section">
                        <h4>${type.charAt(0).toUpperCase() + type.slice(1)}</h4>
                        ${typeMeals.map(meal => `
                            <div class="meal-item">
                                <div class="meal-time">${meal.meal_time}</div>
                                <div class="meal-content">
                                    <p><strong>${meal.food_items}</strong></p>
                                    ${meal.calories ? `<p class="text-secondary">${meal.calories} calories</p>` : ''}
                                    ${meal.notes ? `<p class="text-secondary">${meal.notes}</p>` : ''}
                                </div>
                                <div class="meal-actions">
                                    <button onclick="editMeal('${meal.id}')" class="btn btn-sm btn-outline">Edit</button>
                                    <button onclick="deleteMeal('${meal.id}')" class="btn btn-sm btn-danger">Delete</button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }).join('');
        }

        function updateNutritionDisplay() {
            const targets = {
                calories: 2200,
                protein: 75,
                carbs: 300,
                fat: 75,
                fiber: 25
            };

            Object.keys(dailyNutrition).forEach(nutrient => {
                const current = dailyNutrition[nutrient];
                const target = targets[nutrient];
                const percentage = Math.min((current / target) * 100, 100);
                
                document.getElementById(`${nutrient}-progress`).style.width = `${percentage}%`;
                document.getElementById(`${nutrient}-value`).textContent = `${current} / ${target}${nutrient === 'calories' ? ' kcal' : 'g'}`;
            });
        }

        function updateWaterDisplay() {
            const percentage = Math.min((waterConsumed / waterGoal) * 100, 100);
            document.getElementById('water-level').style.height = `${percentage}%`;
            document.getElementById('water-consumed').textContent = waterConsumed;
        }

        function addWater(amount) {
            waterConsumed += amount;
            updateWaterDisplay();
            
            // Save to backend
            apiCall('/mother/nutrition/water', 'POST', {
                amount: amount,
                date: new Date().toISOString().split('T')[0]
            }).catch(error => {
                console.error('Error logging water intake:', error);
            });
        }

        function logMeal() {
            document.getElementById('meal-modal').style.display = 'block';
            document.getElementById('meal_time').value = new Date().toTimeString().slice(0, 5);
        }

        function closeMealModal() {
            document.getElementById('meal-modal').style.display = 'none';
            document.getElementById('meal-form').reset();
        }

        // Form submission handler
        document.getElementById('meal-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                data.date = new Date().toISOString().split('T')[0];
                
                const response = await apiCall('/mother/nutrition/meals', 'POST', data);
                if (response.success) {
                    showNotification('Meal logged successfully!', 'success');
                    closeMealModal();
                    loadTodaysMeals();
                    loadDailyNutrition();
                }
            } catch (error) {
                showNotification('Error logging meal: ' + error.message, 'error');
            }
        });

        // Placeholder functions
        async function loadDailyNutrition() {
            // Load nutrition summary for today
        }

        async function loadWaterIntake() {
            // Load water intake for today
        }

        async function loadDailyTip() {
            const tips = [
                "Eat small, frequent meals to help with nausea.",
                "Include protein in every meal for baby's development.",
                "Choose whole grains over refined carbohydrates.",
                "Colorful fruits and vegetables provide essential vitamins.",
                "Don't forget to take your prenatal vitamins daily."
            ];
            
            const randomTip = tips[Math.floor(Math.random() * tips.length)];
            document.getElementById('daily-tip').innerHTML = `<p>${randomTip}</p>`;
        }
    </script>
</body>
</html>
