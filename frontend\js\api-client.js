/**
 * API Client for Preg and Baby Care Backend
 */

class APIClient {
    constructor() {
        // Use relative URL since frontend and backend are on same port
        this.baseURL = '/api';
        this.token = localStorage.getItem('access_token');
    }

    // Helper method to make HTTP requests
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Add authorization header if token exists
        if (this.token) {
            config.headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Authentication methods
    async register(userData) {
        const response = await this.makeRequest('/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });

        if (response.access_token) {
            this.token = response.access_token;
            localStorage.setItem('access_token', this.token);
            localStorage.setItem('user_data', JSON.stringify(response.user));
        }

        return response;
    }

    async login(email, password) {
        const response = await this.makeRequest('/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });

        if (response.access_token) {
            this.token = response.access_token;
            localStorage.setItem('access_token', this.token);
            localStorage.setItem('user_data', JSON.stringify(response.user));
        }

        return response;
    }

    logout() {
        this.token = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_data');
        window.location.href = '/pages/login.html';
    }

    // User data methods
    getCurrentUser() {
        const userData = localStorage.getItem('user_data');
        return userData ? JSON.parse(userData) : null;
    }

    isAuthenticated() {
        return !!this.token;
    }

    // Pregnancy profile methods
    async getPregnancyProfile() {
        return await this.makeRequest('/pregnancy/profile');
    }

    async savePregnancyProfile(profileData) {
        return await this.makeRequest('/pregnancy/profile', {
            method: 'POST',
            body: JSON.stringify(profileData)
        });
    }

    // Weight tracking methods
    async getWeightHistory() {
        return await this.makeRequest('/weight/track');
    }

    async addWeightEntry(weightData) {
        return await this.makeRequest('/weight/track', {
            method: 'POST',
            body: JSON.stringify(weightData)
        });
    }

    // Baby profile methods
    async getBabyProfiles() {
        return await this.makeRequest('/baby/profile');
    }

    async createBabyProfile(babyData) {
        return await this.makeRequest('/baby/profile', {
            method: 'POST',
            body: JSON.stringify(babyData)
        });
    }

    // Sleep tracking methods
    async getBabySleepRecords(babyId) {
        return await this.makeRequest(`/baby/sleep?baby_id=${babyId}`);
    }

    async addSleepRecord(sleepData) {
        return await this.makeRequest('/baby/sleep', {
            method: 'POST',
            body: JSON.stringify(sleepData)
        });
    }

    // Vaccination methods
    async getVaccinations(babyId = null) {
        const endpoint = babyId ? `/vaccinations?baby_id=${babyId}` : '/vaccinations';
        return await this.makeRequest(endpoint);
    }

    async addVaccination(vaccinationData) {
        return await this.makeRequest('/vaccinations', {
            method: 'POST',
            body: JSON.stringify(vaccinationData)
        });
    }

    // Appointment methods
    async getAppointments() {
        return await this.makeRequest('/appointments');
    }

    async scheduleAppointment(appointmentData) {
        return await this.makeRequest('/appointments', {
            method: 'POST',
            body: JSON.stringify(appointmentData)
        });
    }

    // Nutrition methods
    async getNutritionRecords() {
        return await this.makeRequest('/nutrition');
    }

    async addNutritionRecord(nutritionData) {
        return await this.makeRequest('/nutrition', {
            method: 'POST',
            body: JSON.stringify(nutritionData)
        });
    }

    // Meditation methods
    async getMeditationSessions() {
        return await this.makeRequest('/meditation');
    }

    async addMeditationSession(sessionData) {
        return await this.makeRequest('/meditation', {
            method: 'POST',
            body: JSON.stringify(sessionData)
        });
    }

    // Chat methods
    async getChatHistory() {
        return await this.makeRequest('/chat');
    }

    async sendChatMessage(message, messageType = 'general') {
        return await this.makeRequest('/chat', {
            method: 'POST',
            body: JSON.stringify({ message, message_type: messageType })
        });
    }

    // Health check
    async healthCheck() {
        return await this.makeRequest('/health');
    }
}

// Create global API client instance
const apiClient = new APIClient();

// Authentication helper functions
function checkAuthentication() {
    if (!apiClient.isAuthenticated()) {
        window.location.href = '/pages/login.html';
        return false;
    }
    return true;
}

function displayUserInfo() {
    const user = apiClient.getCurrentUser();
    if (user) {
        const userNameElements = document.querySelectorAll('#user-name, .user-name');
        userNameElements.forEach(element => {
            element.textContent = user.full_name;
        });
    }
}

// Initialize authentication check on page load
document.addEventListener('DOMContentLoaded', function() {
    // Skip authentication check for login and home pages
    const currentPage = window.location.pathname;
    const publicPages = ['/pages/login.html', '/home.html', '/index.html', '/'];
    
    if (!publicPages.some(page => currentPage.includes(page))) {
        if (checkAuthentication()) {
            displayUserInfo();
        }
    }
});

// Global logout function
function logout() {
    apiClient.logout();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIClient, apiClient };
}
