<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Tracker - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }
        
        body {
            font-family: 'Segoe <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #ffeef7 0%, #f0f9ff 100%);
            min-height: 100vh;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: var(--primary);
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .logo-icon {
            background: var(--primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .back-btn {
            background: var(--light-gray);
            color: var(--dark);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }
        
        .back-btn:hover {
            background: var(--gray);
            color: white;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            text-align: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        .chart-container {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .add-weight-form {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--light-gray);
            border-radius: 8px;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary);
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .weight-history {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-top: 2rem;
        }
        
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-date {
            font-weight: 500;
            color: var(--dark);
        }
        
        .history-weight {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary);
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="pregcare.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Pregnancy Care
            </a>
        </div>
    </header>

    <main class="main-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-weight"></i>
                Weight Tracker
            </h1>
            <p>Monitor your pregnancy weight gain with personalized tracking and insights</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-weight"></i>
                </div>
                <div class="stat-value" id="currentWeight">65.5</div>
                <div class="stat-label">Current Weight (kg)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--secondary);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value" id="weightGain">+8.2</div>
                <div class="stat-label">Total Weight Gain (kg)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: var(--accent);">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-value" id="pregnancyWeek">28</div>
                <div class="stat-label">Pregnancy Week</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: #ff9800;">
                    <i class="fas fa-target"></i>
                </div>
                <div class="stat-value" id="targetRange">11-16</div>
                <div class="stat-label">Target Range (kg)</div>
            </div>
        </div>

        <!-- Weight Chart -->
        <div class="chart-container">
            <h3 style="margin-bottom: 1rem; color: var(--dark);">
                <i class="fas fa-chart-area"></i>
                Weight Progress Chart
            </h3>
            <canvas id="weightChart" width="400" height="200"></canvas>
        </div>

        <!-- Add Weight Form -->
        <div class="add-weight-form">
            <h3 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-plus-circle"></i>
                Add New Weight Entry
            </h3>
            <form id="weightForm">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label" for="weightDate">Date</label>
                        <input type="date" id="weightDate" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="weightValue">Weight (kg)</label>
                        <input type="number" id="weightValue" class="form-input" step="0.1" min="30" max="150" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="weightNotes">Notes (optional)</label>
                    <input type="text" id="weightNotes" class="form-input" placeholder="Any notes about this measurement...">
                </div>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save"></i>
                    Save Weight Entry
                </button>
            </form>
        </div>

        <!-- Weight History -->
        <div class="weight-history">
            <h3 style="margin-bottom: 1.5rem; color: var(--dark);">
                <i class="fas fa-history"></i>
                Recent Weight Entries
            </h3>
            <div id="weightHistoryList">
                <div class="history-item">
                    <div>
                        <div class="history-date">March 15, 2024</div>
                        <div style="font-size: 0.9rem; color: var(--gray);">Week 28</div>
                    </div>
                    <div class="history-weight">65.5 kg</div>
                </div>
                <div class="history-item">
                    <div>
                        <div class="history-date">March 8, 2024</div>
                        <div style="font-size: 0.9rem; color: var(--gray);">Week 27</div>
                    </div>
                    <div class="history-weight">65.0 kg</div>
                </div>
                <div class="history-item">
                    <div>
                        <div class="history-date">March 1, 2024</div>
                        <div style="font-size: 0.9rem; color: var(--gray);">Week 26</div>
                    </div>
                    <div class="history-weight">64.3 kg</div>
                </div>
            </div>
        </div>
    </main>

    <!-- Include API Client -->
    <script src="../../js/api-client.js"></script>

    <script>
        // Initialize weight chart
        const ctx = document.getElementById('weightChart').getContext('2d');
        const weightChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Week 20', 'Week 22', 'Week 24', 'Week 26', 'Week 27', 'Week 28'],
                datasets: [{
                    label: 'Your Weight',
                    data: [60.5, 61.2, 62.8, 64.3, 65.0, 65.5],
                    borderColor: '#e91e63',
                    backgroundColor: 'rgba(233, 30, 99, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Recommended Range (Upper)',
                    data: [62, 63, 64.5, 66, 67, 68],
                    borderColor: '#4caf50',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false
                }, {
                    label: 'Recommended Range (Lower)',
                    data: [58, 59, 60.5, 62, 63, 64],
                    borderColor: '#4caf50',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 55,
                        max: 75
                    }
                }
            }
        });

        // Handle form submission
        document.getElementById('weightForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const date = document.getElementById('weightDate').value;
            const weight = parseFloat(document.getElementById('weightValue').value);
            const notes = document.getElementById('weightNotes').value;

            if (date && weight) {
                try {
                    // Save to API
                    await apiClient.addWeightEntry({
                        weight: weight,
                        pregnancy_week: 28, // You can make this dynamic
                        notes: notes,
                        recorded_date: date
                    });

                    // Add to history list
                    const historyList = document.getElementById('weightHistoryList');
                    const newEntry = document.createElement('div');
                    newEntry.className = 'history-item';
                    newEntry.innerHTML = `
                        <div>
                            <div class="history-date">${new Date(date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</div>
                            <div style="font-size: 0.9rem; color: var(--gray);">${notes || 'No notes'}</div>
                        </div>
                        <div class="history-weight">${weight} kg</div>
                    `;
                    historyList.insertBefore(newEntry, historyList.firstChild);

                    // Update current weight
                    document.getElementById('currentWeight').textContent = weight;

                    // Clear form
                    this.reset();
                    document.getElementById('weightDate').valueAsDate = new Date();

                    // Show success message
                    showMessage('Weight entry saved successfully!', 'success');

                } catch (error) {
                    console.error('Error saving weight:', error);
                    showMessage('Failed to save weight entry. Please try again.', 'error');
                }
            }
        });

        // Show message function
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                ${type === 'success' ? 'background: #4caf50;' : 'background: #f44336;'}
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // Set today's date as default
        document.getElementById('weightDate').valueAsDate = new Date();
    </script>
</body>
</html>
