/**
 * Configuration file for the Maternal-Child Health Care System
 * This file contains all the configuration settings for the frontend application
 */

// API Configuration
const API_CONFIG = {
    // Backend API base URL - Using relative URL since frontend and backend are on same port
    BASE_URL: '/api',
    
    // Request timeout in milliseconds
    TIMEOUT: 30000,
    
    // Retry configuration
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
    
    // Authentication
    TOKEN_KEY: 'auth_token',
    USER_KEY: 'user_data',
    
    // API endpoints
    ENDPOINTS: {
        // Authentication
        LOGIN: '/login',
        REGISTER: '/register',
        
        // Pregnancy
        PREGNANCY_PROFILE: '/pregnancy/profile',
        WEIGHT_TRACK: '/weight/track',
        
        // Baby
        BABY_PROFILE: '/baby/profile',
        BABY_SLEEP: '/baby/sleep',
        
        // Health
        VACCINATIONS: '/vaccinations',
        APPOINTMENTS: '/appointments',
        NUTRITION: '/nutrition',
        MEDITATION: '/meditation',
        
        // Chat
        CHAT: '/chat',
        
        // Admin
        ADMIN_STATS: '/admin/stats',
        ADMIN_USERS: '/admin/users',
        ADMIN_ANALYTICS: '/admin/analytics',
        ADMIN_DOCTORS: '/admin/doctors',
        ADMIN_NUTRITION_PLANS: '/admin/nutrition_plans',
        ADMIN_VACCINATIONS: '/admin/vaccinations',
        ADMIN_SLEEP_SCHEDULES: '/admin/sleep_schedules',
        ADMIN_SCHEMES: '/admin/schemes',
        
        // Doctor
        DOCTOR_PATIENTS: '/doctor/patients',
        DOCTOR_APPOINTMENTS: '/doctor/appointments',
        DOCTOR_PATIENT_DETAILS: '/doctor/patient-details',
        DOCTOR_MEDICAL_RECORDS: '/doctor/patient-medical-records',
        DOCTOR_GENERATE_REPORT: '/doctor/generate-patient-report',
        
        // Health check
        HEALTH: '/health'
    }
};

// Application Configuration
const APP_CONFIG = {
    // Application name
    NAME: 'Maternal-Child Health Care System',
    VERSION: '2.0.0',
    
    // Default settings
    DEFAULT_LANGUAGE: 'en',
    DEFAULT_THEME: 'light',
    
    // Pagination
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    
    // File upload
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
    
    // Chart configuration
    CHART_COLORS: {
        PRIMARY: '#4299e1',
        SECONDARY: '#48bb78',
        WARNING: '#ed8936',
        DANGER: '#e53e3e',
        INFO: '#38b2ac',
        SUCCESS: '#48bb78'
    },
    
    // Date formats
    DATE_FORMAT: 'YYYY-MM-DD',
    DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
    DISPLAY_DATE_FORMAT: 'MMM DD, YYYY',
    DISPLAY_DATETIME_FORMAT: 'MMM DD, YYYY HH:mm'
};

// Feature flags
const FEATURE_FLAGS = {
    VOICE_ASSISTANT: true,
    PDF_REPORTS: true,
    OFFLINE_MODE: true,
    PUSH_NOTIFICATIONS: false,
    ANALYTICS: true,
    CHAT_BOT: true,
    MULTI_LANGUAGE: false,
    DARK_MODE: true
};

// Environment-specific configuration
const ENV_CONFIG = {
    DEVELOPMENT: {
        DEBUG: true,
        LOG_LEVEL: 'debug',
        API_BASE_URL: '/api'
    },
    PRODUCTION: {
        DEBUG: false,
        LOG_LEVEL: 'error',
        API_BASE_URL: '/api'
    }
};

// Get current environment
const getCurrentEnvironment = () => {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        return 'DEVELOPMENT';
    }
    return 'PRODUCTION';
};

// Get configuration for current environment
const getConfig = () => {
    const env = getCurrentEnvironment();
    return {
        ...API_CONFIG,
        ...APP_CONFIG,
        ...FEATURE_FLAGS,
        ...ENV_CONFIG[env],
        ENVIRONMENT: env
    };
};

// Export configuration
window.CONFIG = getConfig();

// Update API client base URL
if (window.apiClient) {
    window.apiClient.baseURL = window.CONFIG.API_BASE_URL || window.CONFIG.BASE_URL;
}

// Console log for debugging
if (window.CONFIG.DEBUG) {
    console.log('🔧 Application Configuration:', window.CONFIG);
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        API_CONFIG,
        APP_CONFIG,
        FEATURE_FLAGS,
        ENV_CONFIG,
        getConfig
    };
}
