<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Admin Dashboard</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .settings-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .settings-nav {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #eee;
        }

        .nav-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .nav-tab:hover {
            color: var(--primary-color);
        }

        .settings-section {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .settings-section.active {
            display: block;
        }

        .section-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .settings-grid {
            display: grid;
            gap: 2rem;
        }

        .setting-group {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .setting-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .setting-title {
            font-weight: 600;
            color: #333;
        }

        .setting-description {
            color: #666;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        .backup-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .logs-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.875rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 2px;
        }

        .log-info {
            color: #007bff;
        }

        .log-warning {
            color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }

        .log-error {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        @media (max-width: 768px) {
            .settings-nav {
                flex-wrap: wrap;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1>⚙️ System Settings</h1>
                <p>Configure system preferences and parameters</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="saveAllSettings()">
                    💾 Save All Changes
                </button>
                <a href="users.html" class="btn btn-secondary">
                    ← Back to Users
                </a>
            </div>
        </div>

        <!-- Settings Navigation -->
        <div class="settings-nav">
            <button class="nav-tab active" onclick="showSection('general')">🏠 General</button>
            <button class="nav-tab" onclick="showSection('security')">🔒 Security</button>
            <button class="nav-tab" onclick="showSection('notifications')">🔔 Notifications</button>
            <button class="nav-tab" onclick="showSection('backup')">💾 Backup</button>
            <button class="nav-tab" onclick="showSection('system')">🖥️ System</button>
        </div>

        <!-- General Settings -->
        <div id="general-section" class="settings-section active">
            <div class="section-header">
                <h2>🏠 General Settings</h2>
                <p>Basic system configuration and preferences</p>
            </div>

            <div class="settings-grid">
                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">System Information</div>
                    </div>
                    <div class="setting-description">
                        Configure basic system information and branding
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="system-name">System Name</label>
                            <input type="text" id="system-name" value="Maternal-Child Health System">
                        </div>
                        <div class="form-group">
                            <label for="system-version">Version</label>
                            <input type="text" id="system-version" value="1.0.0" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="organization-name">Organization Name</label>
                            <input type="text" id="organization-name" value="Healthcare Center">
                        </div>
                        <div class="form-group">
                            <label for="contact-email">Contact Email</label>
                            <input type="email" id="contact-email" value="<EMAIL>">
                        </div>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Regional Settings</div>
                    </div>
                    <div class="setting-description">
                        Configure timezone, language, and regional preferences
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="timezone">Timezone</label>
                            <select id="timezone">
                                <option value="UTC">UTC</option>
                                <option value="America/New_York">Eastern Time</option>
                                <option value="America/Chicago">Central Time</option>
                                <option value="America/Denver">Mountain Time</option>
                                <option value="America/Los_Angeles" selected>Pacific Time</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="language">Default Language</label>
                            <select id="language">
                                <option value="en" selected>English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="date-format">Date Format</label>
                            <select id="date-format">
                                <option value="MM/DD/YYYY" selected>MM/DD/YYYY</option>
                                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="time-format">Time Format</label>
                            <select id="time-format">
                                <option value="12" selected>12 Hour</option>
                                <option value="24">24 Hour</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div id="security-section" class="settings-section">
            <div class="section-header">
                <h2>🔒 Security Settings</h2>
                <p>Configure authentication and security policies</p>
            </div>

            <div class="settings-grid">
                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Password Policy</div>
                    </div>
                    <div class="setting-description">
                        Set requirements for user passwords
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="min-password-length">Minimum Password Length</label>
                            <input type="number" id="min-password-length" value="8" min="6" max="20">
                        </div>
                        <div class="form-group">
                            <label for="password-expiry">Password Expiry (days)</label>
                            <input type="number" id="password-expiry" value="90" min="30" max="365">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="require-uppercase" checked>
                            Require uppercase letters
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="require-numbers" checked>
                            Require numbers
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="require-special" checked>
                            Require special characters
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Session Management</div>
                    </div>
                    <div class="setting-description">
                        Configure user session settings
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="session-timeout">Session Timeout (minutes)</label>
                            <input type="number" id="session-timeout" value="30" min="5" max="480">
                        </div>
                        <div class="form-group">
                            <label for="max-login-attempts">Max Login Attempts</label>
                            <input type="number" id="max-login-attempts" value="5" min="3" max="10">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enable-2fa">
                            Enable Two-Factor Authentication
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div id="notifications-section" class="settings-section">
            <div class="section-header">
                <h2>🔔 Notification Settings</h2>
                <p>Configure system notifications and alerts</p>
            </div>

            <div class="settings-grid">
                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Email Notifications</div>
                    </div>
                    <div class="setting-description">
                        Configure email notification preferences
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp-server">SMTP Server</label>
                            <input type="text" id="smtp-server" value="smtp.gmail.com">
                        </div>
                        <div class="form-group">
                            <label for="smtp-port">SMTP Port</label>
                            <input type="number" id="smtp-port" value="587">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="smtp-username">SMTP Username</label>
                            <input type="email" id="smtp-username" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="smtp-password">SMTP Password</label>
                            <input type="password" id="smtp-password" placeholder="••••••••">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enable-email-notifications" checked>
                            Enable email notifications
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">System Alerts</div>
                    </div>
                    <div class="setting-description">
                        Configure when to send system alerts
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="alert-new-users" checked>
                            Alert on new user registrations
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="alert-failed-logins" checked>
                            Alert on failed login attempts
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="alert-system-errors" checked>
                            Alert on system errors
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="alert-appointments" checked>
                            Alert on appointment changes
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Settings -->
        <div id="backup-section" class="settings-section">
            <div class="section-header">
                <h2>💾 Backup & Recovery</h2>
                <p>Configure data backup and recovery options</p>
            </div>

            <div class="settings-grid">
                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Backup Status</div>
                    </div>
                    <div class="backup-status status-success">
                        <span>✅</span>
                        <span>Last backup: January 25, 2024 at 2:00 AM</span>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="backup-frequency">Backup Frequency</label>
                            <select id="backup-frequency">
                                <option value="daily" selected>Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="backup-time">Backup Time</label>
                            <input type="time" id="backup-time" value="02:00">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="backup-retention">Retention Period (days)</label>
                        <input type="number" id="backup-retention" value="30" min="7" max="365">
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="createBackup()">Create Backup Now</button>
                        <button class="btn btn-secondary" onclick="restoreBackup()">Restore from Backup</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div id="system-section" class="settings-section">
            <div class="section-header">
                <h2>🖥️ System Settings</h2>
                <p>Advanced system configuration and maintenance</p>
            </div>

            <div class="settings-grid">
                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">Performance Settings</div>
                    </div>
                    <div class="setting-description">
                        Configure system performance parameters
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="max-upload-size">Max Upload Size (MB)</label>
                            <input type="number" id="max-upload-size" value="10" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label for="cache-duration">Cache Duration (hours)</label>
                            <input type="number" id="cache-duration" value="24" min="1" max="168">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enable-compression" checked>
                            Enable data compression
                        </label>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-header">
                        <div class="setting-title">System Logs</div>
                    </div>
                    <div class="setting-description">
                        View recent system activity and errors
                    </div>
                    <div class="logs-container" id="system-logs">
                        <div class="log-entry log-info">[2024-01-25 14:30:15] INFO: System backup completed successfully</div>
                        <div class="log-entry log-info">[2024-01-25 14:25:10] INFO: User 'admin' logged in</div>
                        <div class="log-entry log-warning">[2024-01-25 14:20:05] WARN: High memory usage detected (85%)</div>
                        <div class="log-entry log-info">[2024-01-25 14:15:00] INFO: Scheduled maintenance completed</div>
                        <div class="log-entry log-error">[2024-01-25 14:10:30] ERROR: Failed to send email notification</div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-secondary" onclick="refreshLogs()">Refresh Logs</button>
                        <button class="btn btn-secondary" onclick="downloadLogs()">Download Logs</button>
                        <button class="btn btn-danger" onclick="clearLogs()">Clear Logs</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Global Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-danger" onclick="resetToDefaults()">Reset to Defaults</button>
            <button class="btn btn-secondary" onclick="exportSettings()">Export Settings</button>
            <button class="btn btn-secondary" onclick="importSettings()">Import Settings</button>
            <button class="btn btn-primary" onclick="saveAllSettings()">Save All Changes</button>
        </div>
    </div>

    <!-- Application Scripts -->
    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        // Settings Management System
        let currentSettings = {};
        let hasUnsavedChanges = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            setupChangeTracking();
        });

        // Load current settings
        async function loadSettings() {
            try {
                // In a real application, load from API/database
                currentSettings = {
                    general: {
                        systemName: 'Maternal-Child Health System',
                        systemVersion: '1.0.0',
                        organizationName: 'Healthcare Center',
                        contactEmail: '<EMAIL>',
                        timezone: 'America/Los_Angeles',
                        language: 'en',
                        dateFormat: 'MM/DD/YYYY',
                        timeFormat: '12'
                    },
                    security: {
                        minPasswordLength: 8,
                        passwordExpiry: 90,
                        requireUppercase: true,
                        requireNumbers: true,
                        requireSpecial: true,
                        sessionTimeout: 30,
                        maxLoginAttempts: 5,
                        enable2FA: false
                    },
                    notifications: {
                        smtpServer: 'smtp.gmail.com',
                        smtpPort: 587,
                        smtpUsername: '<EMAIL>',
                        enableEmailNotifications: true,
                        alertNewUsers: true,
                        alertFailedLogins: true,
                        alertSystemErrors: true,
                        alertAppointments: true
                    },
                    backup: {
                        frequency: 'daily',
                        time: '02:00',
                        retention: 30
                    },
                    system: {
                        maxUploadSize: 10,
                        cacheDuration: 24,
                        enableCompression: true
                    }
                };

                populateSettings();
            } catch (error) {
                console.error('Error loading settings:', error);
                showNotification('Error loading settings', 'error');
            }
        }

        // Populate form fields with current settings
        function populateSettings() {
            // General settings
            document.getElementById('system-name').value = currentSettings.general.systemName;
            document.getElementById('system-version').value = currentSettings.general.systemVersion;
            document.getElementById('organization-name').value = currentSettings.general.organizationName;
            document.getElementById('contact-email').value = currentSettings.general.contactEmail;
            document.getElementById('timezone').value = currentSettings.general.timezone;
            document.getElementById('language').value = currentSettings.general.language;
            document.getElementById('date-format').value = currentSettings.general.dateFormat;
            document.getElementById('time-format').value = currentSettings.general.timeFormat;

            // Security settings
            document.getElementById('min-password-length').value = currentSettings.security.minPasswordLength;
            document.getElementById('password-expiry').value = currentSettings.security.passwordExpiry;
            document.getElementById('require-uppercase').checked = currentSettings.security.requireUppercase;
            document.getElementById('require-numbers').checked = currentSettings.security.requireNumbers;
            document.getElementById('require-special').checked = currentSettings.security.requireSpecial;
            document.getElementById('session-timeout').value = currentSettings.security.sessionTimeout;
            document.getElementById('max-login-attempts').value = currentSettings.security.maxLoginAttempts;
            document.getElementById('enable-2fa').checked = currentSettings.security.enable2FA;

            // Notification settings
            document.getElementById('smtp-server').value = currentSettings.notifications.smtpServer;
            document.getElementById('smtp-port').value = currentSettings.notifications.smtpPort;
            document.getElementById('smtp-username').value = currentSettings.notifications.smtpUsername;
            document.getElementById('enable-email-notifications').checked = currentSettings.notifications.enableEmailNotifications;
            document.getElementById('alert-new-users').checked = currentSettings.notifications.alertNewUsers;
            document.getElementById('alert-failed-logins').checked = currentSettings.notifications.alertFailedLogins;
            document.getElementById('alert-system-errors').checked = currentSettings.notifications.alertSystemErrors;
            document.getElementById('alert-appointments').checked = currentSettings.notifications.alertAppointments;

            // Backup settings
            document.getElementById('backup-frequency').value = currentSettings.backup.frequency;
            document.getElementById('backup-time').value = currentSettings.backup.time;
            document.getElementById('backup-retention').value = currentSettings.backup.retention;

            // System settings
            document.getElementById('max-upload-size').value = currentSettings.system.maxUploadSize;
            document.getElementById('cache-duration').value = currentSettings.system.cacheDuration;
            document.getElementById('enable-compression').checked = currentSettings.system.enableCompression;
        }

        // Setup change tracking
        function setupChangeTracking() {
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('change', () => {
                    hasUnsavedChanges = true;
                });
            });
        }

        // Show section
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(`${sectionName}-section`).classList.add('active');

            // Add active class to selected tab
            event.target.classList.add('active');
        }

        // Save all settings
        async function saveAllSettings() {
            try {
                // Collect all settings from form
                const updatedSettings = {
                    general: {
                        systemName: document.getElementById('system-name').value,
                        organizationName: document.getElementById('organization-name').value,
                        contactEmail: document.getElementById('contact-email').value,
                        timezone: document.getElementById('timezone').value,
                        language: document.getElementById('language').value,
                        dateFormat: document.getElementById('date-format').value,
                        timeFormat: document.getElementById('time-format').value
                    },
                    security: {
                        minPasswordLength: parseInt(document.getElementById('min-password-length').value),
                        passwordExpiry: parseInt(document.getElementById('password-expiry').value),
                        requireUppercase: document.getElementById('require-uppercase').checked,
                        requireNumbers: document.getElementById('require-numbers').checked,
                        requireSpecial: document.getElementById('require-special').checked,
                        sessionTimeout: parseInt(document.getElementById('session-timeout').value),
                        maxLoginAttempts: parseInt(document.getElementById('max-login-attempts').value),
                        enable2FA: document.getElementById('enable-2fa').checked
                    },
                    notifications: {
                        smtpServer: document.getElementById('smtp-server').value,
                        smtpPort: parseInt(document.getElementById('smtp-port').value),
                        smtpUsername: document.getElementById('smtp-username').value,
                        enableEmailNotifications: document.getElementById('enable-email-notifications').checked,
                        alertNewUsers: document.getElementById('alert-new-users').checked,
                        alertFailedLogins: document.getElementById('alert-failed-logins').checked,
                        alertSystemErrors: document.getElementById('alert-system-errors').checked,
                        alertAppointments: document.getElementById('alert-appointments').checked
                    },
                    backup: {
                        frequency: document.getElementById('backup-frequency').value,
                        time: document.getElementById('backup-time').value,
                        retention: parseInt(document.getElementById('backup-retention').value)
                    },
                    system: {
                        maxUploadSize: parseInt(document.getElementById('max-upload-size').value),
                        cacheDuration: parseInt(document.getElementById('cache-duration').value),
                        enableCompression: document.getElementById('enable-compression').checked
                    }
                };

                // In a real application, save to API/database
                currentSettings = updatedSettings;
                hasUnsavedChanges = false;

                showNotification('Settings saved successfully!', 'success');
            } catch (error) {
                console.error('Error saving settings:', error);
                showNotification('Error saving settings', 'error');
            }
        }

        // Backup functions
        function createBackup() {
            showNotification('Creating backup...', 'info');
            setTimeout(() => {
                showNotification('Backup created successfully!', 'success');
            }, 2000);
        }

        function restoreBackup() {
            if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {
                showNotification('Restoring from backup...', 'info');
                setTimeout(() => {
                    showNotification('Backup restored successfully!', 'success');
                }, 3000);
            }
        }

        // System functions
        function refreshLogs() {
            showNotification('Refreshing system logs...', 'info');
            // In a real application, fetch latest logs
        }

        function downloadLogs() {
            showNotification('Downloading system logs...', 'info');
            // In a real application, generate and download log file
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all system logs?')) {
                document.getElementById('system-logs').innerHTML = '<div class="log-entry log-info">[' + new Date().toISOString() + '] INFO: System logs cleared</div>';
                showNotification('System logs cleared!', 'success');
            }
        }

        // Other functions
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
                loadSettings();
                showNotification('Settings reset to defaults!', 'success');
            }
        }

        function exportSettings() {
            const dataStr = JSON.stringify(currentSettings, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'mchs-settings.json';
            link.click();
            showNotification('Settings exported successfully!', 'success');
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const importedSettings = JSON.parse(e.target.result);
                            currentSettings = importedSettings;
                            populateSettings();
                            showNotification('Settings imported successfully!', 'success');
                        } catch (error) {
                            showNotification('Error importing settings file', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 2rem;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                border-radius: 4px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Warn about unsaved changes
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
