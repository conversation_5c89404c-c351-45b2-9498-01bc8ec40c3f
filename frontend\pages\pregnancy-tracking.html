<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregnancy Tracking - Preg and Baby Care</title>
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .pregnancy-progress {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
        }
        
        .week-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        
        .week-number {
            font-size: 2rem;
            font-weight: 700;
        }
        
        .progress-ring {
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }
        
        .progress-ring .background {
            stroke: rgba(255, 255, 255, 0.2);
        }
        
        .progress-ring .progress {
            stroke: #fff;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            transition: stroke-dasharray 0.3s ease;
        }
        
        .trimester-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .milestone-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            margin-bottom: 1rem;
        }
        
        .milestone-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }
        
        .milestone-completed {
            background: var(--success-color);
            color: white;
        }
        
        .milestone-upcoming {
            background: var(--warning-color);
            color: white;
        }
        
        .milestone-future {
            background: var(--border-color);
            color: var(--text-secondary);
        }
        
        .health-metric {
            text-align: center;
            padding: 1.5rem;
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .baby-size-visual {
            text-align: center;
            padding: 2rem;
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
        }
        
        .baby-emoji {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .size-comparison {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .size-details {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="mother/profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">🤱 Pregnancy Tracking</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="AuthManager.logout()" class="btn btn-outline btn-sm">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main style="padding: 2rem;">
        <div class="container">
            <!-- Pregnancy Progress Section -->
            <div class="pregnancy-progress">
                <div class="row items-center">
                    <div class="col-4">
                        <div class="week-circle">
                            <div class="week-number" id="current-week">24</div>
                            <div style="font-size: 0.875rem;">weeks</div>
                        </div>
                        <div class="text-center">
                            <div class="trimester-badge" id="trimester">Second Trimester</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h2 style="margin-bottom: 1rem;">Your Pregnancy Journey</h2>
                            <div class="progress-ring">
                                <svg width="200" height="200">
                                    <circle class="background" cx="100" cy="100" r="90"></circle>
                                    <circle class="progress" cx="100" cy="100" r="90" 
                                            stroke-dasharray="0 565.48" id="progress-circle"></circle>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <h3 style="margin-bottom: 1rem;">Due Date</h3>
                            <div style="font-size: 1.5rem; font-weight: 600;" id="due-date">March 15, 2025</div>
                            <div style="opacity: 0.8; margin-top: 0.5rem;" id="days-remaining">112 days remaining</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Week Info -->
            <div class="row mb-4">
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Baby Development - Week <span id="week-title">24</span></h3>
                        </div>
                        <div class="card-body">
                            <div class="baby-size-visual">
                                <div class="baby-emoji">🌽</div>
                                <div class="size-comparison">About the size of a corn</div>
                                <div class="size-details">
                                    Length: ~12 inches<br>
                                    Weight: ~1.3 pounds
                                </div>
                            </div>
                            
                            <div style="margin-top: 1.5rem;">
                                <h4>This Week's Developments:</h4>
                                <ul style="padding-left: 1.5rem; margin-top: 1rem;">
                                    <li>Brain tissue and neurons are developing rapidly</li>
                                    <li>Lungs are beginning to produce surfactant</li>
                                    <li>Hearing is becoming more acute</li>
                                    <li>Taste buds are forming</li>
                                    <li>Baby can respond to sounds and light</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Health Metrics</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="health-metric">
                                        <div class="metric-value" id="current-weight">65</div>
                                        <div class="metric-label">Current Weight (kg)</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="health-metric">
                                        <div class="metric-value" id="weight-gain">+8</div>
                                        <div class="metric-label">Weight Gain (kg)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" style="margin-top: 1rem;">
                                <div class="col-6">
                                    <div class="health-metric">
                                        <div class="metric-value" id="blood-pressure">120/80</div>
                                        <div class="metric-label">Blood Pressure</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="health-metric">
                                        <div class="metric-value" id="heart-rate">72</div>
                                        <div class="metric-label">Heart Rate (bpm)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;" 
                                    onclick="openModal('health-record-modal')">
                                Update Health Record
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pregnancy Milestones -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">Pregnancy Milestones</h3>
                </div>
                <div class="card-body">
                    <div id="milestones-list">
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-completed">✓</div>
                            <div>
                                <h4>First Trimester Complete</h4>
                                <p class="text-secondary">Week 12 - All major organs formed</p>
                            </div>
                        </div>
                        
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-completed">✓</div>
                            <div>
                                <h4>Anatomy Scan</h4>
                                <p class="text-secondary">Week 20 - Detailed ultrasound completed</p>
                            </div>
                        </div>
                        
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-upcoming">!</div>
                            <div>
                                <h4>Glucose Screening</h4>
                                <p class="text-secondary">Week 24-28 - Test for gestational diabetes</p>
                            </div>
                        </div>
                        
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-future">○</div>
                            <div>
                                <h4>Third Trimester</h4>
                                <p class="text-secondary">Week 28 - Final stage begins</p>
                            </div>
                        </div>
                        
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-future">○</div>
                            <div>
                                <h4>Full Term</h4>
                                <p class="text-secondary">Week 37 - Baby considered full term</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">📅</div>
                            <h4>Book Appointment</h4>
                            <p class="text-secondary">Schedule your next checkup</p>
                            <button class="btn btn-primary">Book Now</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🥗</div>
                            <h4>Nutrition Guide</h4>
                            <p class="text-secondary">Track your daily nutrition</p>
                            <button class="btn btn-primary">View Guide</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">💬</div>
                            <h4>Ask Doctor</h4>
                            <p class="text-secondary">Get expert advice</p>
                            <button class="btn btn-primary">Chat Now</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Health Record Modal -->
    <div id="health-record-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Update Health Record</h3>
                <button onclick="closeModal('health-record-modal')" class="btn-close">×</button>
            </div>
            <div class="modal-body">
                <form id="health-record-form">
                    <div class="form-group">
                        <label class="form-label">Record Date</label>
                        <input type="date" name="record_date" class="form-control" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Weight (kg)</label>
                                <input type="number" name="weight" class="form-control" step="0.1">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Blood Pressure</label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" name="blood_pressure_systolic" class="form-control" placeholder="Systolic">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" name="blood_pressure_diastolic" class="form-control" placeholder="Diastolic">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Heart Rate (bpm)</label>
                        <input type="number" name="heart_rate" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Notes</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">Save Record</button>
                        <button type="button" onclick="closeModal('health-record-modal')" class="btn btn-outline">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/common-header.js"></script>
    <script>
        // Modal functionality
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Update progress circle
        function updateProgressCircle(week) {
            const circle = document.getElementById('progress-circle');
            const circumference = 2 * Math.PI * 90; // radius = 90
            const progress = (week / 40) * circumference;
            circle.style.strokeDasharray = `${progress} ${circumference}`;
        }
        
        // Load pregnancy data
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const user = AuthManager.getUser();
                if (user) {
                    document.getElementById('user-name').textContent = user.full_name;
                }
                
                // Load pregnancy timeline
                await loadPregnancyData();
            } catch (error) {
                console.error('Error loading pregnancy data:', error);
                UIUtils.showAlert('Error loading pregnancy data', 'error');
            }
        });
        
        async function loadPregnancyData() {
            try {
                const response = await APIClient.get('/mother/pregnancy-timeline');
                if (response.success) {
                    updatePregnancyDisplay(response.data);
                }
            } catch (error) {
                console.error('Error loading pregnancy timeline:', error);
            }
        }
        
        function updatePregnancyDisplay(data) {
            // Update week display
            const currentWeek = data.current_week || 24;
            document.getElementById('current-week').textContent = currentWeek;
            document.getElementById('week-title').textContent = currentWeek;
            
            // Update progress circle
            updateProgressCircle(currentWeek);
            
            // Update trimester
            let trimester = 'First Trimester';
            if (currentWeek >= 14 && currentWeek < 28) {
                trimester = 'Second Trimester';
            } else if (currentWeek >= 28) {
                trimester = 'Third Trimester';
            }
            document.getElementById('trimester').textContent = trimester;
            
            // Update due date
            if (data.due_date) {
                document.getElementById('due-date').textContent = UIUtils.formatDate(data.due_date);
                
                const dueDate = new Date(data.due_date);
                const today = new Date();
                const daysRemaining = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
                document.getElementById('days-remaining').textContent = `${daysRemaining} days remaining`;
            }
        }
        
        // Health record form handler
        FormHandler.handleSubmit('health-record-form', async (data) => {
            const submitBtn = document.querySelector('#health-record-form button[type="submit"]');
            const hideLoading = UIUtils.showLoading(submitBtn);
            
            try {
                data.record_type = 'routine_checkup';
                const response = await APIClient.post('/mother/health-records', data);
                
                if (response.success) {
                    UIUtils.showAlert('Health record saved successfully', 'success');
                    closeModal('health-record-modal');
                    // Refresh data
                    await loadPregnancyData();
                } else {
                    UIUtils.showAlert(response.message || 'Failed to save health record', 'error');
                }
            } catch (error) {
                UIUtils.showAlert(error.message || 'Failed to save health record', 'error');
            } finally {
                hideLoading();
            }
        });
        
        // Modal styles
        const modalStyles = `
            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            
            .modal-content {
                background: white;
                border-radius: 1rem;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1.5rem;
                border-bottom: 1px solid var(--border-color);
            }
            
            .modal-body {
                padding: 1.5rem;
            }
            
            .btn-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: var(--text-secondary);
            }
        `;
        
        // Add modal styles to head
        const styleSheet = document.createElement('style');
        styleSheet.textContent = modalStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
