<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat with Doctor - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">💬 MCHS - Chat with Doctor</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Chat Sidebar -->
                <div class="col-md-4">
                    <div class="chat-sidebar">
                        <!-- Doctor Selection -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h3>Your Healthcare Team</h3>
                            </div>
                            <div class="card-body">
                                <div id="doctors-list">
                                    <!-- Doctors will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Recent Conversations -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Recent Conversations</h3>
                            </div>
                            <div class="card-body">
                                <div id="recent-chats">
                                    <!-- Recent chats will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-body">
                                <button onclick="requestUrgentConsultation()" class="btn btn-danger btn-block mb-2">
                                    🚨 Urgent Consultation
                                </button>
                                <button onclick="scheduleAppointment()" class="btn btn-primary btn-block mb-2">
                                    📅 Schedule Appointment
                                </button>
                                <button onclick="shareHealthData()" class="btn btn-outline btn-block">
                                    📊 Share Health Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Area -->
                <div class="col-md-8">
                    <div class="chat-container">
                        <!-- Chat Header -->
                        <div id="chat-header" class="chat-header" style="display: none;">
                            <div class="doctor-info">
                                <div class="doctor-avatar">
                                    <img id="doctor-avatar" src="" alt="Doctor" class="avatar">
                                    <div id="doctor-status" class="status-indicator"></div>
                                </div>
                                <div class="doctor-details">
                                    <h4 id="doctor-name">Select a doctor to start chatting</h4>
                                    <p id="doctor-specialty" class="text-secondary"></p>
                                    <span id="doctor-online-status" class="status-text"></span>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <button onclick="startVideoCall()" class="btn btn-outline btn-sm">
                                    📹 Video Call
                                </button>
                                <button onclick="startVoiceCall()" class="btn btn-outline btn-sm">
                                    📞 Voice Call
                                </button>
                            </div>
                        </div>

                        <!-- Chat Messages -->
                        <div id="chat-messages" class="chat-messages">
                            <div class="welcome-message">
                                <div class="welcome-icon">💬</div>
                                <h3>Welcome to Doctor Chat</h3>
                                <p>Select a doctor from your healthcare team to start a conversation. You can ask questions, share concerns, or request consultations.</p>
                                <div class="chat-features">
                                    <div class="feature-item">
                                        <span class="feature-icon">🔒</span>
                                        <span>Secure & Private</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-icon">⚡</span>
                                        <span>Real-time Messaging</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-icon">📎</span>
                                        <span>File Sharing</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Input -->
                        <div id="chat-input-container" class="chat-input-container" style="display: none;">
                            <div class="chat-input">
                                <div class="input-actions">
                                    <button onclick="attachFile()" class="btn btn-outline btn-sm">
                                        📎
                                    </button>
                                    <button onclick="addEmoji()" class="btn btn-outline btn-sm">
                                        😊
                                    </button>
                                </div>
                                <div class="message-input">
                                    <textarea id="message-input" placeholder="Type your message..." rows="1" 
                                              onkeypress="handleKeyPress(event)" oninput="autoResize(this)"></textarea>
                                </div>
                                <div class="send-actions">
                                    <button onclick="sendMessage()" class="btn btn-primary">
                                        Send
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Typing Indicator -->
                            <div id="typing-indicator" class="typing-indicator" style="display: none;">
                                <span id="typing-user">Dr. Smith</span> is typing...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- File Upload Modal -->
    <div id="file-upload-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Share File</h3>
                <span class="close" onclick="closeFileModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="file-upload-area" onclick="document.getElementById('file-input').click()">
                    <div class="upload-icon">📎</div>
                    <p>Click to select a file or drag and drop</p>
                    <small>Supported: Images, PDFs, Documents (Max 10MB)</small>
                    <input type="file" id="file-input" style="display: none;" 
                           accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" onchange="handleFileSelect(event)">
                </div>
                <div id="file-preview" class="file-preview" style="display: none;">
                    <div class="file-info">
                        <span id="file-name"></span>
                        <span id="file-size"></span>
                    </div>
                    <button onclick="removeFile()" class="btn btn-sm btn-outline">Remove</button>
                </div>
                <div class="form-group mt-3">
                    <label for="file-description">Description (optional)</label>
                    <textarea id="file-description" class="form-control" rows="2" 
                              placeholder="Add a description for this file..."></textarea>
                </div>
                <div class="form-actions">
                    <button onclick="uploadFile()" class="btn btn-primary">Share File</button>
                    <button onclick="closeFileModal()" class="btn btn-secondary">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Urgent Consultation Modal -->
    <div id="urgent-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🚨 Request Urgent Consultation</h3>
                <span class="close" onclick="closeUrgentModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <strong>Important:</strong> Use this only for urgent medical concerns that need immediate attention.
                    For emergencies, please call emergency services.
                </div>
                
                <form id="urgent-form">
                    <div class="form-group">
                        <label for="urgency-level">Urgency Level</label>
                        <select id="urgency-level" class="form-control" required>
                            <option value="">Select urgency level...</option>
                            <option value="high">High - Need response within 1 hour</option>
                            <option value="medium">Medium - Need response within 4 hours</option>
                            <option value="low">Low - Need response within 24 hours</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="symptoms">Symptoms/Concerns</label>
                        <textarea id="symptoms" class="form-control" rows="4" required
                                  placeholder="Please describe your symptoms or concerns in detail..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="duration">How long have you been experiencing this?</label>
                        <select id="duration" class="form-control" required>
                            <option value="">Select duration...</option>
                            <option value="less_than_hour">Less than 1 hour</option>
                            <option value="few_hours">A few hours</option>
                            <option value="today">Since today</option>
                            <option value="yesterday">Since yesterday</option>
                            <option value="few_days">A few days</option>
                            <option value="week_or_more">A week or more</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="preferred-doctor">Preferred Doctor (optional)</label>
                        <select id="preferred-doctor" class="form-control">
                            <option value="">Any available doctor</option>
                            <!-- Doctors will be loaded here -->
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger">Send Urgent Request</button>
                        <button type="button" onclick="closeUrgentModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentDoctor = null;
        let currentChat = null;
        let messagePolling = null;
        let selectedFile = null;

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDoctors();
            loadRecentChats();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Auto-resize textarea
            document.getElementById('message-input').addEventListener('input', function() {
                autoResize(this);
            });

            // File drag and drop
            const uploadArea = document.querySelector('.file-upload-area');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleFileDrop);
        }

        async function loadDoctors() {
            try {
                const response = await apiCall('/mother/doctors', 'GET');
                if (response.success) {
                    displayDoctors(response.data);
                }
            } catch (error) {
                console.error('Error loading doctors:', error);
                showNotification('Error loading doctors: ' + error.message, 'error');
            }
        }

        function displayDoctors(doctors) {
            const container = document.getElementById('doctors-list');
            const preferredSelect = document.getElementById('preferred-doctor');
            
            if (doctors.length === 0) {
                container.innerHTML = '<div class="text-center text-secondary">No doctors assigned yet.</div>';
                return;
            }

            container.innerHTML = doctors.map(doctor => `
                <div class="doctor-card" onclick="selectDoctor('${doctor.id}')">
                    <div class="doctor-avatar">
                        <img src="${doctor.avatar || '/images/default-doctor.png'}" alt="${doctor.name}" class="avatar">
                        <div class="status-indicator ${doctor.online_status}"></div>
                    </div>
                    <div class="doctor-info">
                        <h5>${doctor.name}</h5>
                        <p class="specialty">${doctor.specialty}</p>
                        <span class="status-text ${doctor.online_status}">
                            ${doctor.online_status === 'online' ? 'Online' : 
                              doctor.online_status === 'busy' ? 'Busy' : 'Offline'}
                        </span>
                    </div>
                    ${doctor.unread_count > 0 ? `<div class="unread-badge">${doctor.unread_count}</div>` : ''}
                </div>
            `).join('');

            // Populate preferred doctor select
            preferredSelect.innerHTML = '<option value="">Any available doctor</option>' +
                doctors.map(doctor => `<option value="${doctor.id}">${doctor.name}</option>`).join('');
        }

        async function selectDoctor(doctorId) {
            try {
                const response = await apiCall(`/mother/doctors/${doctorId}`, 'GET');
                if (response.success) {
                    currentDoctor = response.data;
                    displayDoctorHeader();
                    loadChatHistory(doctorId);
                    startMessagePolling();
                }
            } catch (error) {
                console.error('Error selecting doctor:', error);
                showNotification('Error loading doctor details: ' + error.message, 'error');
            }
        }

        function displayDoctorHeader() {
            document.getElementById('doctor-name').textContent = currentDoctor.name;
            document.getElementById('doctor-specialty').textContent = currentDoctor.specialty;
            document.getElementById('doctor-avatar').src = currentDoctor.avatar || '/images/default-doctor.png';
            document.getElementById('doctor-status').className = `status-indicator ${currentDoctor.online_status}`;
            document.getElementById('doctor-online-status').textContent = 
                currentDoctor.online_status === 'online' ? 'Online' : 
                currentDoctor.online_status === 'busy' ? 'Busy' : 'Offline';
            document.getElementById('doctor-online-status').className = `status-text ${currentDoctor.online_status}`;

            document.getElementById('chat-header').style.display = 'flex';
            document.getElementById('chat-input-container').style.display = 'block';
        }

        async function loadChatHistory(doctorId) {
            try {
                const response = await apiCall(`/mother/chat/${doctorId}/messages`, 'GET');
                if (response.success) {
                    displayMessages(response.data);
                }
            } catch (error) {
                console.error('Error loading chat history:', error);
                showNotification('Error loading chat history: ' + error.message, 'error');
            }
        }

        function displayMessages(messages) {
            const container = document.getElementById('chat-messages');
            
            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="no-messages">
                        <div class="no-messages-icon">💬</div>
                        <h4>Start a conversation</h4>
                        <p>Send a message to ${currentDoctor.name} to begin your consultation.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = messages.map(message => `
                <div class="message ${message.sender_type === 'mother' ? 'sent' : 'received'}">
                    <div class="message-content">
                        ${message.message_type === 'text' ? 
                            `<p>${message.content}</p>` :
                            message.message_type === 'file' ?
                            `<div class="file-message">
                                <div class="file-icon">📎</div>
                                <div class="file-details">
                                    <span class="file-name">${message.file_name}</span>
                                    <span class="file-size">${formatFileSize(message.file_size)}</span>
                                </div>
                                <button onclick="downloadFile('${message.file_url}')" class="btn btn-sm btn-outline">Download</button>
                            </div>` : message.content
                        }
                        ${message.description ? `<p class="file-description">${message.description}</p>` : ''}
                    </div>
                    <div class="message-meta">
                        <span class="message-time">${formatMessageTime(message.created_at)}</span>
                        ${message.sender_type === 'mother' ? 
                            `<span class="message-status ${message.read_status}">${getMessageStatusIcon(message.read_status)}</span>` : ''
                        }
                    </div>
                </div>
            `).join('');

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        async function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const content = messageInput.value.trim();
            
            if (!content && !selectedFile) return;
            if (!currentDoctor) {
                showNotification('Please select a doctor first', 'error');
                return;
            }

            try {
                const messageData = {
                    doctor_id: currentDoctor.id,
                    message_type: selectedFile ? 'file' : 'text',
                    content: content
                };

                if (selectedFile) {
                    // Upload file first
                    const fileData = new FormData();
                    fileData.append('file', selectedFile);
                    fileData.append('description', content);
                    
                    const fileResponse = await apiCall('/mother/chat/upload-file', 'POST', fileData);
                    if (fileResponse.success) {
                        messageData.file_url = fileResponse.data.file_url;
                        messageData.file_name = selectedFile.name;
                        messageData.file_size = selectedFile.size;
                    }
                }

                const response = await apiCall('/mother/chat/send-message', 'POST', messageData);
                if (response.success) {
                    messageInput.value = '';
                    selectedFile = null;
                    autoResize(messageInput);
                    loadChatHistory(currentDoctor.id);
                }
            } catch (error) {
                console.error('Error sending message:', error);
                showNotification('Error sending message: ' + error.message, 'error');
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        function startMessagePolling() {
            if (messagePolling) clearInterval(messagePolling);
            
            messagePolling = setInterval(() => {
                if (currentDoctor) {
                    loadChatHistory(currentDoctor.id);
                }
            }, 5000); // Poll every 5 seconds
        }

        async function loadRecentChats() {
            try {
                const response = await apiCall('/mother/chat/recent', 'GET');
                if (response.success) {
                    displayRecentChats(response.data);
                }
            } catch (error) {
                console.error('Error loading recent chats:', error);
            }
        }

        function displayRecentChats(chats) {
            const container = document.getElementById('recent-chats');
            
            if (chats.length === 0) {
                container.innerHTML = '<div class="text-center text-secondary">No recent conversations</div>';
                return;
            }

            container.innerHTML = chats.map(chat => `
                <div class="recent-chat-item" onclick="selectDoctor('${chat.doctor_id}')">
                    <div class="chat-avatar">
                        <img src="${chat.doctor_avatar || '/images/default-doctor.png'}" alt="${chat.doctor_name}" class="avatar">
                    </div>
                    <div class="chat-info">
                        <h6>${chat.doctor_name}</h6>
                        <p class="last-message">${chat.last_message}</p>
                        <span class="chat-time">${formatMessageTime(chat.last_message_time)}</span>
                    </div>
                    ${chat.unread_count > 0 ? `<div class="unread-badge">${chat.unread_count}</div>` : ''}
                </div>
            `).join('');
        }

        // File handling functions
        function attachFile() {
            document.getElementById('file-upload-modal').style.display = 'block';
        }

        function closeFileModal() {
            document.getElementById('file-upload-modal').style.display = 'none';
            selectedFile = null;
            document.getElementById('file-preview').style.display = 'none';
            document.getElementById('file-description').value = '';
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    showNotification('File size must be less than 10MB', 'error');
                    return;
                }
                
                selectedFile = file;
                document.getElementById('file-name').textContent = file.name;
                document.getElementById('file-size').textContent = formatFileSize(file.size);
                document.getElementById('file-preview').style.display = 'block';
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect({ target: { files: files } });
            }
        }

        function removeFile() {
            selectedFile = null;
            document.getElementById('file-preview').style.display = 'none';
            document.getElementById('file-input').value = '';
        }

        async function uploadFile() {
            if (!selectedFile) return;
            
            const description = document.getElementById('file-description').value;
            
            // Add file to message input as context
            const messageInput = document.getElementById('message-input');
            messageInput.value = description || `Sharing file: ${selectedFile.name}`;
            
            closeFileModal();
            sendMessage();
        }

        // Quick action functions
        function requestUrgentConsultation() {
            document.getElementById('urgent-modal').style.display = 'block';
        }

        function closeUrgentModal() {
            document.getElementById('urgent-modal').style.display = 'none';
            document.getElementById('urgent-form').reset();
        }

        document.getElementById('urgent-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/urgent-consultation', 'POST', data);
                if (response.success) {
                    showNotification('Urgent consultation request sent successfully!', 'success');
                    closeUrgentModal();
                }
            } catch (error) {
                showNotification('Error sending urgent request: ' + error.message, 'error');
            }
        });

        function scheduleAppointment() {
            window.location.href = 'appointments.html';
        }

        function shareHealthData() {
            showNotification('Health data sharing feature coming soon!', 'info');
        }

        function startVideoCall() {
            showNotification('Video call feature coming soon!', 'info');
        }

        function startVoiceCall() {
            showNotification('Voice call feature coming soon!', 'info');
        }

        function addEmoji() {
            const messageInput = document.getElementById('message-input');
            const emojis = ['😊', '😢', '😰', '🤔', '👍', '❤️', '🙏', '😴', '🤱', '👶'];
            const emoji = emojis[Math.floor(Math.random() * emojis.length)];
            messageInput.value += emoji;
            messageInput.focus();
        }

        // Utility functions
        function formatMessageTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
                return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            } else if (diffDays < 7) {
                return date.toLocaleDateString('en-US', { weekday: 'short' });
            } else {
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getMessageStatusIcon(status) {
            const icons = {
                'sent': '✓',
                'delivered': '✓✓',
                'read': '✓✓'
            };
            return icons[status] || '';
        }

        function downloadFile(url) {
            window.open(url, '_blank');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (messagePolling) {
                clearInterval(messagePolling);
            }
        });
    </script>
</body>
</html>
