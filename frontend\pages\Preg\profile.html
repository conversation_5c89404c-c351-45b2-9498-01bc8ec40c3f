<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mother Profile - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="../../home.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Home</a>
                    <div class="navbar-brand">🤱 MCHS - Mother Profile</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="row">
                <!-- Profile Information -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h2>Personal Information</h2>
                            <button onclick="editProfile()" class="btn btn-primary">Edit Profile</button>
                        </div>
                        <div class="card-body">
                            <form id="profile-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="full_name">Full Name</label>
                                            <input type="text" id="full_name" name="full_name" class="form-control" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email">Email</label>
                                            <input type="email" id="email" name="email" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone">Phone Number</label>
                                            <input type="tel" id="phone" name="phone" class="form-control" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="date_of_birth">Date of Birth</label>
                                            <input type="date" id="date_of_birth" name="date_of_birth" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="blood_group">Blood Group</label>
                                            <select id="blood_group" name="blood_group" class="form-control" disabled>
                                                <option value="">Select Blood Group</option>
                                                <option value="A+">A+</option>
                                                <option value="A-">A-</option>
                                                <option value="B+">B+</option>
                                                <option value="B-">B-</option>
                                                <option value="AB+">AB+</option>
                                                <option value="AB-">AB-</option>
                                                <option value="O+">O+</option>
                                                <option value="O-">O-</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="height">Height (cm)</label>
                                            <input type="number" id="height" name="height" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="weight">Current Weight (kg)</label>
                                            <input type="number" id="weight" name="weight" class="form-control" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="pregnancy_stage">Pregnancy Stage</label>
                                            <select id="pregnancy_stage" name="pregnancy_stage" class="form-control" disabled>
                                                <option value="">Select Stage</option>
                                                <option value="planning">Planning</option>
                                                <option value="first_trimester">First Trimester</option>
                                                <option value="second_trimester">Second Trimester</option>
                                                <option value="third_trimester">Third Trimester</option>
                                                <option value="postpartum">Postpartum</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address">Address</label>
                                    <textarea id="address" name="address" class="form-control" rows="3" readonly></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="medical_history">Medical History</label>
                                    <textarea id="medical_history" name="medical_history" class="form-control" rows="3" readonly></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="allergies">Allergies</label>
                                    <textarea id="allergies" name="allergies" class="form-control" rows="2" readonly></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="current_medications">Current Medications</label>
                                    <textarea id="current_medications" name="current_medications" class="form-control" rows="2" readonly></textarea>
                                </div>

                                <div class="form-actions" id="form-actions" style="display: none;">
                                    <button type="submit" class="btn btn-primary">Save Changes</button>
                                    <button type="button" onclick="cancelEdit()" class="btn btn-secondary">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contacts -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3>Emergency Contacts</h3>
                            <button onclick="editEmergencyContact()" class="btn btn-sm btn-outline">Edit</button>
                        </div>
                        <div class="card-body">
                            <div class="emergency-contact">
                                <div class="contact-info">
                                    <strong id="emergency_contact_name">-</strong>
                                    <p id="emergency_contact_relation" class="text-secondary">-</p>
                                    <p id="emergency_contact_phone" class="text-primary">-</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Quick Stats</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">BMI</span>
                                <span class="stat-value" id="bmi">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Pregnancy Week</span>
                                <span class="stat-value" id="pregnancy_week">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Days to Due Date</span>
                                <span class="stat-value" id="days_to_due">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Picture -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Profile Picture</h3>
                        </div>
                        <div class="card-body text-center">
                            <div class="profile-picture-container">
                                <img id="profile-picture" src="../../assets/default-avatar.png" alt="Profile Picture" class="profile-picture">
                                <button onclick="changeProfilePicture()" class="btn btn-sm btn-primary mt-2">Change Picture</button>
                                <input type="file" id="profile-picture-input" accept="image/*" style="display: none;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Emergency Contact Modal -->
    <div id="emergency-contact-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Emergency Contact</h3>
                <span class="close" onclick="closeEmergencyContactModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="emergency-contact-form">
                    <div class="form-group">
                        <label for="modal_emergency_name">Contact Name</label>
                        <input type="text" id="modal_emergency_name" name="emergency_contact_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="modal_emergency_relation">Relationship</label>
                        <input type="text" id="modal_emergency_relation" name="emergency_contact_relation" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="modal_emergency_phone">Phone Number</label>
                        <input type="tel" id="modal_emergency_phone" name="emergency_contact_phone" class="form-control" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save Contact</button>
                        <button type="button" onclick="closeEmergencyContactModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let isEditing = false;

        // Load profile data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProfile();
            calculateStats();
        });

        async function loadProfile() {
            try {
                const response = await apiCall('/mother/profile', 'GET');
                if (response.success) {
                    const profile = response.data;
                    populateForm(profile);
                    updateEmergencyContact(profile);
                }
            } catch (error) {
                showNotification('Error loading profile: ' + error.message, 'error');
            }
        }

        function populateForm(profile) {
            Object.keys(profile).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = profile[key] || '';
                }
            });
        }

        function updateEmergencyContact(profile) {
            document.getElementById('emergency_contact_name').textContent = profile.emergency_contact_name || '-';
            document.getElementById('emergency_contact_relation').textContent = profile.emergency_contact_relation || '-';
            document.getElementById('emergency_contact_phone').textContent = profile.emergency_contact_phone || '-';
        }

        function editProfile() {
            isEditing = true;
            const inputs = document.querySelectorAll('#profile-form input, #profile-form select, #profile-form textarea');
            inputs.forEach(input => {
                if (input.id !== 'email') { // Email should remain readonly
                    input.removeAttribute('readonly');
                    input.removeAttribute('disabled');
                }
            });
            document.getElementById('form-actions').style.display = 'block';
        }

        function cancelEdit() {
            isEditing = false;
            const inputs = document.querySelectorAll('#profile-form input, #profile-form select, #profile-form textarea');
            inputs.forEach(input => {
                input.setAttribute('readonly', 'readonly');
                if (input.tagName === 'SELECT') {
                    input.setAttribute('disabled', 'disabled');
                }
            });
            document.getElementById('form-actions').style.display = 'none';
            loadProfile(); // Reload original data
        }

        function calculateStats() {
            const height = parseFloat(document.getElementById('height').value);
            const weight = parseFloat(document.getElementById('weight').value);
            
            if (height && weight) {
                const bmi = (weight / ((height / 100) ** 2)).toFixed(1);
                document.getElementById('bmi').textContent = bmi;
            }
        }

        function editEmergencyContact() {
            document.getElementById('emergency-contact-modal').style.display = 'block';
            // Pre-fill current values
            document.getElementById('modal_emergency_name').value = document.getElementById('emergency_contact_name').textContent;
            document.getElementById('modal_emergency_relation').value = document.getElementById('emergency_contact_relation').textContent;
            document.getElementById('modal_emergency_phone').value = document.getElementById('emergency_contact_phone').textContent;
        }

        function closeEmergencyContactModal() {
            document.getElementById('emergency-contact-modal').style.display = 'none';
        }

        function changeProfilePicture() {
            document.getElementById('profile-picture-input').click();
        }

        // Form submission handlers
        document.getElementById('profile-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            if (!isEditing) return;

            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/profile', 'PUT', data);
                if (response.success) {
                    showNotification('Profile updated successfully!', 'success');
                    cancelEdit();
                    calculateStats();
                }
            } catch (error) {
                showNotification('Error updating profile: ' + error.message, 'error');
            }
        });

        document.getElementById('emergency-contact-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/emergency-contact', 'PUT', data);
                if (response.success) {
                    showNotification('Emergency contact updated successfully!', 'success');
                    updateEmergencyContact(data);
                    closeEmergencyContactModal();
                }
            } catch (error) {
                showNotification('Error updating emergency contact: ' + error.message, 'error');
            }
        });

        document.getElementById('profile-picture-input').addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (file) {
                try {
                    const formData = new FormData();
                    formData.append('profile_picture', file);
                    
                    const response = await apiCall('/mother/profile-picture', 'POST', formData);
                    if (response.success) {
                        document.getElementById('profile-picture').src = response.data.url;
                        showNotification('Profile picture updated successfully!', 'success');
                    }
                } catch (error) {
                    showNotification('Error uploading profile picture: ' + error.message, 'error');
                }
            }
        });
    </script>
</body>
</html>
