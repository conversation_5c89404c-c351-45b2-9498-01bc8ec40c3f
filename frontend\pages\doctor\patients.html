<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maternal-Child Health System</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #ff66b2;
            --secondary: #ff99cc;
            --accent: #ff3385;
            --light: #fff0f5;
            --dark: #4d0026;
            --text: #333333;
            --white: #ffffff;
            --success: #4CAF50;
            --warning: #FFC107;
            --danger: #F44336;
            --gray: #f5f5f5;
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
        body {
            background-color: var(--light);
            color: var(--text);
            line-height: 1.6;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            color: var(--white);
            padding: 1rem 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 3px solid rgba(255, 255, 255, 0.2);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .logo img {
            height: 50px;
            margin-right: 12px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .logo-text {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(to right, var(--white), #FFE6F2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.6rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        .btn-primary {
            background: var(--accent);
            color: var(--white);
        }
        
        .btn-primary:hover {
            background: #e02a75;
            transform: translateY(-2px);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--accent);
            color: var(--accent);
        }
        
        .btn-outline:hover {
            background: rgba(255, 51, 133, 0.1);
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: var(--danger);
            color: white;
            border: none;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }
        
        .btn-sm {
            padding: 0.4rem 1rem;
            font-size: 0.9rem;
        }
        
        /* Main Content */
        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1.5rem;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid rgba(255, 102, 178, 0.2);
        }
        
        .page-title {
            color: var(--dark);
            font-size: 2.2rem;
            font-weight: 700;
            position: relative;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 0;
            width: 60px;
            height: 4px;
            background: var(--accent);
            border-radius: 2px;
        }
        
        .page-actions {
            display: flex;
            gap: 1rem;
        }
        
        /* Filters and Search */
        .filters-card {
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 0 2px rgba(255, 51, 133, 0.2);
        }
        
        /* Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 102, 178, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 1.5rem;
            color: var(--accent);
        }
        
        .stat-content {
            flex-grow: 1;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dark);
            line-height: 1.2;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: var(--text);
        }
        
        /* Patient Views */
        .view-toggle {
            background: var(--white);
            border-radius: 8px;
            padding: 0.5rem;
            display: inline-flex;
            margin-bottom: 1.5rem;
        }
        
        .view-btn {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .view-btn.active {
            background: var(--accent);
            color: white;
        }
        
        /* Table View */
        .table-container {
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: rgba(255, 102, 178, 0.1);
            font-weight: 600;
            color: var(--dark);
        }
        
        tr:hover {
            background-color: rgba(255, 102, 178, 0.05);
        }
        
        .badge {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-pregnant {
            background: rgba(255, 102, 178, 0.2);
            color: var(--accent);
        }
        
        .badge-postpartum {
            background: rgba(76, 175, 80, 0.2);
            color: var(--success);
        }
        
        .badge-low {
            background: rgba(76, 175, 80, 0.2);
            color: var(--success);
        }
        
        .badge-medium {
            background: rgba(255, 193, 7, 0.2);
            color: var(--warning);
        }
        
        .badge-high {
            background: rgba(244, 67, 54, 0.2);
            color: var(--danger);
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        /* Card View */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .patient-card {
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .patient-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        
        .patient-card-header {
            padding: 1.5rem;
            background: linear-gradient(to right, rgba(255, 102, 178, 0.1), rgba(255, 51, 133, 0.1));
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .patient-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
        }
        
        .patient-card-body {
            padding: 1.5rem;
        }
        
        .patient-detail {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .detail-label {
            width: 120px;
            font-weight: 500;
            color: var(--dark);
        }
        
        .detail-value {
            flex-grow: 1;
        }
        
        .patient-card-footer {
            padding: 1rem 1.5rem;
            background: var(--gray);
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 1.5rem;
        }
        
        .page-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover, .page-btn.active {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }
        
        /* Modals */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 5px 25px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            padding: 20px;
            background: linear-gradient(to right, var(--primary), var(--accent));
            color: white;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .close {
            font-size: 1.8rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .close:hover {
            transform: rotate(90deg);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            background: var(--gray);
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .form-row {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .form-col {
            flex: 1;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .header {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .page-actions {
                align-self: flex-end;
            }
        }
        
        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                flex-direction: column;
                gap: 1rem;
            }
        }
        
        @media (max-width: 576px) {
            .page-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .btn {
                width: 100%;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <img src="https://i.ibb.co/6n0hLML/pregnancy-logo.png" alt="MotherCare Logo">
                <div class="logo-text">MotherCare</div>
            </div>
            
            <div class="auth-buttons">
                <span class="text-white">Dr. Susan Chen</span>
                <button class="btn btn-outline"><i class="fas fa-user"></i> My Account</button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">Patient Management</h1>
            <div class="page-actions">
                <button onclick="addNewPatient()" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Patient</button>
                <button class="btn btn-outline"><i class="fas fa-download"></i> Export List</button>
            </div>
        </div>
        
        <!-- Filters and Search -->
        <div class="filters-card">
            <div class="filters-grid">
                <div class="form-group">
                    <label for="search-patients">Search Patients</label>
                    <input type="text" id="search-patients" class="form-control" placeholder="Search by name, ID, or phone...">
                </div>
                
                <div class="form-group">
                    <label for="filter-status">Status</label>
                    <select id="filter-status" class="form-control">
                        <option value="">All Patients</option>
                        <option value="pregnant">Pregnant</option>
                        <option value="postpartum">Postpartum</option>
                        <option value="planning">Family Planning</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="filter-priority">Risk Level</label>
                    <select id="filter-priority" class="form-control">
                        <option value="">All Risk Levels</option>
                        <option value="low">Low Risk</option>
                        <option value="medium">Medium Risk</option>
                        <option value="high">High Risk</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="sort-by">Sort By</label>
                    <select id="sort-by" class="form-control">
                        <option value="name">Name</option>
                        <option value="last_visit">Last Visit</option>
                        <option value="next_appointment">Next Appointment</option>
                        <option value="due_date">Due Date</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <div class="stat-value">142</div>
                    <div class="stat-label">Total Patients</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🤰</div>
                <div class="stat-content">
                    <div class="stat-value">68</div>
                    <div class="stat-label">Pregnant Patients</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">⚠️</div>
                <div class="stat-content">
                    <div class="stat-value">19</div>
                    <div class="stat-label">High Risk Patients</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-content">
                    <div class="stat-value">12</div>
                    <div class="stat-label">Appointments Today</div>
                </div>
            </div>
        </div>
        
        <!-- View Toggle -->
        <div class="view-toggle">
            <button class="view-btn active" onclick="toggleView('table')"><i class="fas fa-table"></i> Table View</button>
            <button class="view-btn" onclick="toggleView('cards')"><i class="fas fa-th-large"></i> Card View</button>
        </div>
        
        <!-- Table View -->
        <div id="table-view">
            <div class="table-container">
                <div class="table-header">
                    <h3>Patients List</h3>
                </div>
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Patient</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Risk Level</th>
                                <th>Last Visit</th>
                                <th>Next Appointment</th>
                                <th>Due Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="patient-info">
                                        <strong>Sarah Johnson</strong>
                                        <div class="text-secondary">ID: MC-1425</div>
                                    </div>
                                </td>
                                <td>
                                    <div>(*************</div>
                                    <div class="text-secondary"><EMAIL></div>
                                </td>
                                <td><span class="badge badge-pregnant">Pregnant</span></td>
                                <td><span class="badge badge-medium">Medium Risk</span></td>
                                <td>May 12, 2024</td>
                                <td>May 25, 2024</td>
                                <td>Aug 30, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <div class="patient-info">
                                        <strong>Emily Davis</strong>
                                        <div class="text-secondary">ID: MC-1872</div>
                                    </div>
                                </td>
                                <td>
                                    <div>(*************</div>
                                    <div class="text-secondary"><EMAIL></div>
                                </td>
                                <td><span class="badge badge-pregnant">Pregnant</span></td>
                                <td><span class="badge badge-low">Low Risk</span></td>
                                <td>May 10, 2024</td>
                                <td>May 28, 2024</td>
                                <td>Sep 15, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <div class="patient-info">
                                        <strong>Maria Rodriguez</strong>
                                        <div class="text-secondary">ID: MC-1567</div>
                                    </div>
                                </td>
                                <td>
                                    <div>(*************</div>
                                    <div class="text-secondary"><EMAIL></div>
                                </td>
                                <td><span class="badge badge-pregnant">Pregnant</span></td>
                                <td><span class="badge badge-high">High Risk</span></td>
                                <td>May 8, 2024</td>
                                <td>May 18, 2024</td>
                                <td>Jul 22, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <div class="patient-info">
                                        <strong>Jennifer Wilson</strong>
                                        <div class="text-secondary">ID: MC-1983</div>
                                    </div>
                                </td>
                                <td>
                                    <div>(555) 456-7890</div>
                                    <div class="text-secondary"><EMAIL></div>
                                </td>
                                <td><span class="badge badge-postpartum">Postpartum</span></td>
                                <td><span class="badge badge-low">Low Risk</span></td>
                                <td>Apr 28, 2024</td>
                                <td>Jun 5, 2024</td>
                                <td>-</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td>
                                    <div class="patient-info">
                                        <strong>Lisa Anderson</strong>
                                        <div class="text-secondary">ID: MC-1765</div>
                                    </div>
                                </td>
                                <td>
                                    <div>(555) 567-8901</div>
                                    <div class="text-secondary"><EMAIL></div>
                                </td>
                                <td><span class="badge badge-pregnant">Pregnant</span></td>
                                <td><span class="badge badge-medium">Medium Risk</span></td>
                                <td>May 5, 2024</td>
                                <td>May 22, 2024</td>
                                <td>Oct 10, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Card View -->
        <div id="cards-view" style="display: none;">
            <div class="cards-grid">
                <!-- Patient Card 1 -->
                <div class="patient-card">
                    <div class="patient-card-header">
                        <div class="patient-name">Sarah Johnson</div>
                        <span class="badge badge-pregnant">Pregnant</span>
                    </div>
                    <div class="patient-card-body">
                        <div class="patient-detail">
                            <div class="detail-label">ID:</div>
                            <div class="detail-value">MC-1425</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Phone:</div>
                            <div class="detail-value">(*************</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Risk Level:</div>
                            <div class="detail-value"><span class="badge badge-medium">Medium Risk</span></div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Last Visit:</div>
                            <div class="detail-value">May 12, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Next Appt:</div>
                            <div class="detail-value">May 25, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Due Date:</div>
                            <div class="detail-value">Aug 30, 2024</div>
                        </div>
                    </div>
                    <div class="patient-card-footer">
                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                    </div>
                </div>
                
                <!-- Patient Card 2 -->
                <div class="patient-card">
                    <div class="patient-card-header">
                        <div class="patient-name">Emily Davis</div>
                        <span class="badge badge-pregnant">Pregnant</span>
                    </div>
                    <div class="patient-card-body">
                        <div class="patient-detail">
                            <div class="detail-label">ID:</div>
                            <div class="detail-value">MC-1872</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Phone:</div>
                            <div class="detail-value">(*************</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Risk Level:</div>
                            <div class="detail-value"><span class="badge badge-low">Low Risk</span></div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Last Visit:</div>
                            <div class="detail-value">May 10, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Next Appt:</div>
                            <div class="detail-value">May 28, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Due Date:</div>
                            <div class="detail-value">Sep 15, 2024</div>
                        </div>
                    </div>
                    <div class="patient-card-footer">
                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                    </div>
                </div>
                
                <!-- Patient Card 3 -->
                <div class="patient-card">
                    <div class="patient-card-header">
                        <div class="patient-name">Maria Rodriguez</div>
                        <span class="badge badge-pregnant">Pregnant</span>
                    </div>
                    <div class="patient-card-body">
                        <div class="patient-detail">
                            <div class="detail-label">ID:</div>
                            <div class="detail-value">MC-1567</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Phone:</div>
                            <div class="detail-value">(*************</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Risk Level:</div>
                            <div class="detail-value"><span class="badge badge-high">High Risk</span></div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Last Visit:</div>
                            <div class="detail-value">May 8, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Next Appt:</div>
                            <div class="detail-value">May 18, 2024</div>
                        </div>
                        <div class="patient-detail">
                            <div class="detail-label">Due Date:</div>
                            <div class="detail-value">Jul 22, 2024</div>
                        </div>
                    </div>
                    <div class="patient-card-footer">
                        <button class="btn btn-sm btn-primary"><i class="fas fa-eye"></i></button>
                        <button class="btn btn-sm btn-outline"><i class="fas fa-calendar"></i></button>
                        <button class="btn btn-sm btn-secondary"><i class="fas fa-edit"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pagination -->
        <div class="pagination">
            <div class="page-btn"><i class="fas fa-chevron-left"></i></div>
            <div class="page-btn active">1</div>
            <div class="page-btn">2</div>
            <div class="page-btn">3</div>
            <div class="page-btn"><i class="fas fa-chevron-right"></i></div>
        </div>
    </div>
    
    <!-- Add Patient Modal -->
    <div id="add-patient-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Add New Patient</h3>
                <span class="close" onclick="closeModal('add-patient-modal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-patient-form">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="patient_name">Full Name</label>
                                <input type="text" id="patient_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="patient_email">Email</label>
                                <input type="email" id="patient_email" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="patient_phone">Phone Number</label>
                                <input type="tel" id="patient_phone" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="date_of_birth">Date of Birth</label>
                                <input type="date" id="date_of_birth" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="patient_status">Patient Status</label>
                                <select id="patient_status" class="form-control" required>
                                    <option value="">Select Status</option>
                                    <option value="pregnant">Pregnant</option>
                                    <option value="postpartum">Postpartum</option>
                                    <option value="planning">Family Planning</option>
                                    <option value="general">General Care</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="risk_level">Risk Level</label>
                                <select id="risk_level" class="form-control" required>
                                    <option value="">Select Risk Level</option>
                                    <option value="low">Low Risk</option>
                                    <option value="medium">Medium Risk</option>
                                    <option value="high">High Risk</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row" id="pregnancy-details">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="due_date">Expected Due Date</label>
                                <input type="date" id="due_date" class="form-control">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="gestational_age">Gestational Age (weeks)</label>
                                <input type="number" id="gestational_age" class="form-control" min="0" max="42">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="medical_history">Medical History</label>
                        <textarea id="medical_history" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="emergency_contact">Emergency Contact</label>
                        <input type="text" id="emergency_contact" class="form-control" placeholder="Name and phone number">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('add-patient-modal')">Cancel</button>
                <button type="button" class="btn btn-primary">Add Patient</button>
            </div>
        </div>
    </div>

    <script>
        // Toggle between table and card view
        function toggleView(view) {
            const tableView = document.getElementById('table-view');
            const cardsView = document.getElementById('cards-view');
            const viewButtons = document.querySelectorAll('.view-btn');
            
            if (view === 'table') {
                tableView.style.display = 'block';
                cardsView.style.display = 'none';
                viewButtons[0].classList.add('active');
                viewButtons[1].classList.remove('active');
            } else {
                tableView.style.display = 'none';
                cardsView.style.display = 'block';
                viewButtons[0].classList.remove('active');
                viewButtons[1].classList.add('active');
            }
        }
        
        // Show modal for adding new patient
        function addNewPatient() {
            document.getElementById('add-patient-modal').style.display = 'flex';
        }
        
        // Close modal
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Show/hide pregnancy details based on status
        document.getElementById('patient_status').addEventListener('change', function() {
            const pregnancyDetails = document.getElementById('pregnancy-details');
            if (this.value === 'pregnant') {
                pregnancyDetails.style.display = 'flex';
            } else {
                pregnancyDetails.style.display = 'none';
            }
        });
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default for date of birth
            const today = new Date();
            const formattedDate = today.toISOString().split('T')[0];
            document.getElementById('date_of_birth').value = formattedDate;
        });
    </script>
</body>
</html>