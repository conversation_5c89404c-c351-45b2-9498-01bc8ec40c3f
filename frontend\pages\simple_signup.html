<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Maternal-Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .signup-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .signup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .signup-header i {
            font-size: 3rem;
            color: #e91e63;
            margin-bottom: 10px;
        }
        
        .signup-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .signup-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group label i {
            margin-right: 8px;
            color: #e91e63;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #e91e63;
        }
        
        .signup-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #c2185b);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-top: 10px;
        }
        
        .signup-btn:hover {
            transform: translateY(-2px);
        }
        
        .signup-btn i {
            margin-right: 8px;
        }
        
        .signup-footer {
            text-align: center;
            margin-top: 25px;
            font-size: 14px;
        }
        
        .signup-footer p {
            margin-bottom: 10px;
        }
        
        .signup-footer a {
            color: #e91e63;
            text-decoration: none;
        }
        
        .signup-footer a:hover {
            text-decoration: underline;
        }

        .message {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="signup-header">
            <i class="fas fa-user-plus"></i>
            <h1>Create Account</h1>
            <p>Join our Maternal-Child Health Care community</p>
        </div>
        
        <!-- Simple form that submits directly to backend -->
        <form action="/signup" method="POST">
            <div class="form-group">
                <label for="fullName">
                    <i class="fas fa-user"></i>
                    Full Name
                </label>
                <input type="text" id="fullName" name="fullName" required 
                       placeholder="Enter your full name">
            </div>
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input type="email" id="email" name="email" required 
                       placeholder="Enter your email address">
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" id="password" name="password" required 
                       placeholder="Enter your password" minlength="6">
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">
                    <i class="fas fa-lock"></i>
                    Confirm Password
                </label>
                <input type="password" id="confirmPassword" name="confirmPassword" required 
                       placeholder="Confirm your password" minlength="6">
            </div>
            
            <button type="submit" class="signup-btn">
                <i class="fas fa-user-plus"></i>
                Create Account
            </button>
        </form>
        
        <div class="signup-footer">
            <p>Already have an account? <a href="/login">Sign in here</a></p>
            <p><a href="/">← Back to Home</a></p>
        </div>
    </div>
</body>
</html>
