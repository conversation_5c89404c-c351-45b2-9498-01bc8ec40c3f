<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Dashboard - Maternal-Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo-icon {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem;
            border-radius: 10px;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.2);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .welcome-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #718096;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-content h3 {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }

        .stat-content p {
            color: #718096;
            font-size: 0.9rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-scheduled {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .status-completed {
            background: #c6f6d5;
            color: #2f855a;
        }

        .status-cancelled {
            background: #fed7d7;
            color: #c53030;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #718096;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .main-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <span>Doctor Dashboard</span>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="patients.html"><i class="fas fa-users"></i> Patients</a></li>
                    <li><a href="appointments.html"><i class="fas fa-calendar"></i> Appointments</a></li>
                    <li><a href="/"><i class="fas fa-home"></i> Home</a></li>
                </ul>
            </nav>
            
            <div class="user-profile">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <span id="doctor-name">Dr. Smith</span>
                <a href="/logout" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </header>

    <main class="main-container">
        <div class="welcome-section">
            <h1 class="welcome-title">Welcome back, <span id="welcome-doctor-name">Doctor</span>!</h1>
            <p class="welcome-subtitle">Here's what's happening with your patients today.</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3 id="total-patients">0</h3>
                    <p>Total Patients</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <h3 id="today-appointments">0</h3>
                    <p>Today's Appointments</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-content">
                    <h3 id="week-appointments">0</h3>
                    <p>This Week</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                    <i class="fas fa-baby"></i>
                </div>
                <div class="stat-content">
                    <h3 id="active-pregnancies">0</h3>
                    <p>Active Pregnancies</p>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Recent Appointments</h2>
                    <button class="btn" onclick="viewAllAppointments()">
                        <i class="fas fa-eye"></i> View All
                    </button>
                </div>
                <div id="appointments-content">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading appointments...
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Quick Actions</h2>
                </div>
                <div style="display: flex; flex-direction: column; gap: 1rem;">
                    <button class="btn" onclick="viewPatients()">
                        <i class="fas fa-users"></i> View All Patients
                    </button>
                    <button class="btn" onclick="manageAppointments()">
                        <i class="fas fa-calendar-plus"></i> Manage Appointments
                    </button>
                    <button class="btn" onclick="generateReports()">
                        <i class="fas fa-chart-bar"></i> Generate Reports
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Patient Overview</h2>
                <button class="btn" onclick="refreshPatients()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
            <div id="patients-content">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading patients...
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let patients = [];
        let appointments = [];
        let stats = {};

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
        });

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadStats(),
                    loadAppointments(),
                    loadPatients()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/doctor/stats');
                if (response.ok) {
                    stats = await response.json();
                    updateStatsDisplay();
                } else {
                    throw new Error('Failed to load stats');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
                showError('Failed to load statistics');
            }
        }

        // Update stats display
        function updateStatsDisplay() {
            document.getElementById('total-patients').textContent = stats.total_patients || 0;
            document.getElementById('today-appointments').textContent = stats.today_appointments || 0;
            document.getElementById('week-appointments').textContent = stats.week_appointments || 0;
            document.getElementById('active-pregnancies').textContent = stats.active_pregnancies || 0;
        }

        // Load appointments
        async function loadAppointments() {
            try {
                const response = await fetch('/api/doctor/appointments');
                if (response.ok) {
                    appointments = await response.json();
                    displayAppointments();
                } else {
                    throw new Error('Failed to load appointments');
                }
            } catch (error) {
                console.error('Error loading appointments:', error);
                document.getElementById('appointments-content').innerHTML =
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load appointments</p></div>';
            }
        }

        // Display appointments
        function displayAppointments() {
            const container = document.getElementById('appointments-content');

            if (appointments.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-calendar"></i><p>No appointments found</p></div>';
                return;
            }

            const recentAppointments = appointments.slice(0, 5);
            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentAppointments.map(apt => `
                            <tr>
                                <td>${apt.patient_name}</td>
                                <td>${new Date(apt.appointment_date).toLocaleDateString()}</td>
                                <td>${new Date(apt.appointment_date).toLocaleTimeString()}</td>
                                <td><span class="status-badge status-${apt.status}">${apt.status}</span></td>
                                <td>
                                    <button class="btn btn-secondary" onclick="updateAppointmentStatus(${apt.id}, 'completed')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Load patients
        async function loadPatients() {
            try {
                const response = await fetch('/api/doctor/patients');
                if (response.ok) {
                    patients = await response.json();
                    displayPatients();
                } else {
                    throw new Error('Failed to load patients');
                }
            } catch (error) {
                console.error('Error loading patients:', error);
                document.getElementById('patients-content').innerHTML =
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load patients</p></div>';
            }
        }

        // Display patients
        function displayPatients() {
            const container = document.getElementById('patients-content');

            if (patients.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-users"></i><p>No patients found</p></div>';
                return;
            }

            const recentPatients = patients.slice(0, 10);
            const tableHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Pregnancy Status</th>
                            <th>Last Appointment</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentPatients.map(patient => `
                            <tr>
                                <td>${patient.full_name}</td>
                                <td>${patient.email}</td>
                                <td>${patient.phone || 'N/A'}</td>
                                <td>${patient.pregnancy_status || 'N/A'}</td>
                                <td>${patient.last_appointment ? new Date(patient.last_appointment).toLocaleDateString() : 'None'}</td>
                                <td>
                                    <button class="btn btn-secondary" onclick="viewPatientDetails(${patient.id})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Update appointment status
        async function updateAppointmentStatus(appointmentId, status) {
            try {
                const response = await fetch(`/api/doctor/appointments/${appointmentId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status })
                });

                if (response.ok) {
                    showSuccess('Appointment status updated successfully');
                    loadAppointments(); // Reload appointments
                    loadStats(); // Reload stats
                } else {
                    throw new Error('Failed to update appointment status');
                }
            } catch (error) {
                console.error('Error updating appointment status:', error);
                showError('Failed to update appointment status');
            }
        }

        // Navigation functions
        function viewAllAppointments() {
            window.location.href = 'appointments.html';
        }

        function viewPatients() {
            window.location.href = 'patients.html';
        }

        function manageAppointments() {
            window.location.href = 'appointments.html';
        }

        function generateReports() {
            alert('Reports feature coming soon!');
        }

        function refreshPatients() {
            loadPatients();
        }

        function viewPatientDetails(patientId) {
            window.location.href = `patients.html?patient=${patientId}`;
        }

        // Utility functions
        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Success: ' + message);
        }
    </script>
</body>
</html>
