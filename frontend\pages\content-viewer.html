<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Content - Maternal-Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo-icon {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem;
            border-radius: 10px;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-menu a:hover {
            background: rgba(255,255,255,0.2);
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #718096;
            font-size: 1.2rem;
        }

        .categories-nav {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .categories-title {
            font-size: 1.5rem;
            color: #2d3748;
            margin-bottom: 1rem;
            text-align: center;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .category-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            transition: transform 0.2s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .category-btn:hover {
            transform: translateY(-2px);
        }

        .category-btn.active {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .category-btn i {
            font-size: 1.5rem;
        }

        .content-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .content-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .content-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
        }

        .content-title {
            font-size: 2rem;
            color: #2d3748;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .content-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .content-card:hover {
            transform: translateY(-2px);
        }

        .content-card-title {
            font-size: 1.25rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .content-card-description {
            color: #718096;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .content-card-text {
            color: #4a5568;
            line-height: 1.7;
        }

        .content-card-meta {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
            font-size: 0.85rem;
            color: #718096;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <span>Health Content</span>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li><a href="/"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-container">
        <div class="page-header">
            <h1 class="page-title">Health & Wellness Content</h1>
            <p class="page-subtitle">Expert guidance for maternal and child health care</p>
        </div>

        <div class="categories-nav">
            <h2 class="categories-title">Browse by Category</h2>
            <div id="categories-container" class="categories-grid">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading categories...
                </div>
            </div>
        </div>

        <div class="content-section">
            <div id="content-header" class="content-header" style="display: none;">
                <div id="content-icon" class="content-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h2 id="content-title" class="content-title">Select a Category</h2>
            </div>
            
            <div id="content-container">
                <div class="empty-state">
                    <i class="fas fa-mouse-pointer"></i>
                    <h3>Select a Category</h3>
                    <p>Choose a category above to view related health content</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let categories = [];
        let currentCategory = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
        });

        // Load content categories
        async function loadCategories() {
            try {
                const response = await fetch('/api/content/categories');
                if (response.ok) {
                    categories = await response.json();
                    displayCategories();
                } else {
                    throw new Error('Failed to load categories');
                }
            } catch (error) {
                console.error('Error loading categories:', error);
                document.getElementById('categories-container').innerHTML = 
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load categories</p></div>';
            }
        }

        // Display categories
        function displayCategories() {
            const container = document.getElementById('categories-container');
            
            if (categories.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>No categories available</p></div>';
                return;
            }

            const categoriesHTML = categories.map(category => `
                <button class="category-btn" onclick="loadCategoryContent('${category.name}', '${category.display_name}', '${category.icon}')">
                    <i class="${category.icon}"></i>
                    <span>${category.display_name}</span>
                </button>
            `).join('');
            
            container.innerHTML = categoriesHTML;
        }

        // Load content for a specific category
        async function loadCategoryContent(categoryName, displayName, icon) {
            // Update active category button
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            event.target.closest('.category-btn').classList.add('active');
            
            // Update content header
            document.getElementById('content-header').style.display = 'flex';
            document.getElementById('content-icon').innerHTML = `<i class="${icon}"></i>`;
            document.getElementById('content-title').textContent = displayName;
            
            // Show loading state
            document.getElementById('content-container').innerHTML = 
                '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading content...</div>';
            
            try {
                const response = await fetch(`/api/content/${categoryName}`);
                if (response.ok) {
                    const content = await response.json();
                    displayContent(content);
                } else {
                    throw new Error('Failed to load content');
                }
            } catch (error) {
                console.error('Error loading content:', error);
                document.getElementById('content-container').innerHTML = 
                    '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Failed to load content</p></div>';
            }
        }

        // Display content
        function displayContent(content) {
            const container = document.getElementById('content-container');
            
            if (content.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-file-alt"></i><p>No content available for this category</p></div>';
                return;
            }

            const contentHTML = `
                <div class="content-grid">
                    ${content.map(item => `
                        <div class="content-card">
                            <h3 class="content-card-title">${item.title}</h3>
                            ${item.description ? `<p class="content-card-description">${item.description}</p>` : ''}
                            <div class="content-card-text">${item.content}</div>
                            <div class="content-card-meta">
                                Published: ${new Date(item.created_at).toLocaleDateString()}
                                ${item.tags ? ` | Tags: ${item.tags}` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            container.innerHTML = contentHTML;
        }
    </script>
</body>
</html>
