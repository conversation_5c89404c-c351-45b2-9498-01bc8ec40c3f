<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Care Center - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            min-height: 100vh;
            margin: 0;
        }
        
        /* Hero Section Styles */
        .hero-section {
            background: linear-gradient(135deg, rgba(233, 30, 99, 0.8), rgba(255, 107, 157, 0.8)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23a8d8ea" width="1200" height="600"/><circle fill="%23aa96da" cx="300" cy="200" r="80"/><circle fill="%23fcbad3" cx="900" cy="150" r="60"/><circle fill="%23ffffd2" cx="600" cy="400" r="100"/><rect fill="%23d4a574" x="400" y="250" width="400" height="200" rx="20"/><circle fill="%23ffeaa7" cx="600" cy="350" r="50"/></svg>');
            background-size: cover;
            background-position: center;
            height: 65vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="20" r="1.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="90" r="1.2" fill="white" opacity="0.1"/></svg>');
            background-size: 50px 50px;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-content p {
            font-size: 1.4rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        /* Header Styles */
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: var(--primary);
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .logo-icon {
            background: var(--primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .back-btn {
            background: var(--light-gray);
            color: var(--dark);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }
        
        .back-btn:hover {
            background: var(--gray);
            color: white;
        }
        
        /* Quick Access Section */
        .quick-access-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 5rem 2rem;
            margin-top: -50px;
            position: relative;
            z-index: 10;
            text-align: center;
        }

        .quick-access-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23e91e63" opacity="0.1"/><circle cx="80" cy="30" r="1.5" fill="%234caf50" opacity="0.1"/><circle cx="60" cy="70" r="2.5" fill="%232196f3" opacity="0.1"/><circle cx="30" cy="80" r="1" fill="%23ff9800" opacity="0.1"/></svg>');
            background-size: 100px 100px;
            pointer-events: none;
        }

        .quick-access-container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .quick-access-title {
            font-size: 3rem;
            color: #2d3748;
            margin-bottom: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .quick-access-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .quick-access-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 2.5rem;
            max-width: 1200px;
            margin: 0 auto;
            align-items: start;
        }

        .quick-access-item {
            background: white;
            border-radius: 25px;
            padding: 2.5rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            text-align: center;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .quick-access-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), #ff6b9d);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .quick-access-item:hover::before {
            transform: scaleX(1);
        }

        .quick-access-item:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: rgba(233, 30, 99, 0.2);
        }

        .quick-access-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), #ff6b9d);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 25px rgba(233, 30, 99, 0.3);
            transition: all 0.3s ease;
        }

        .quick-access-item:hover .quick-access-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 35px rgba(233, 30, 99, 0.4);
        }

        .quick-access-item h3 {
            font-size: 1.2rem;
            color: #2d3748;
            margin: 0;
            font-weight: 600;
            line-height: 1.4;
        }

        /* Custom styling for specific buttons */
        .quick-access-item:nth-child(6) .quick-access-icon {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .quick-access-item:nth-child(6):hover .quick-access-icon {
            box-shadow: 0 15px 35px rgba(255, 152, 0, 0.4);
        }

        .quick-access-item:nth-child(7) .quick-access-icon {
            background: linear-gradient(135deg, #607d8b, #455a64);
        }

        .quick-access-item:nth-child(7):hover .quick-access-icon {
            box-shadow: 0 15px 35px rgba(96, 125, 139, 0.4);
        }

        /* Main Content */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .quick-access-grid {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                height: 55vh;
            }

            .hero-content h1 {
                font-size: 2.8rem;
            }

            .hero-content p {
                font-size: 1.2rem;
            }

            .quick-access-section {
                padding: 4rem 1rem;
            }

            .quick-access-title {
                font-size: 2.2rem;
            }

            .quick-access-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .quick-access-item {
                padding: 2rem 1rem;
            }

            .quick-access-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }

            .quick-access-item h3 {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .hero-section {
                height: 50vh;
            }

            .hero-content h1 {
                font-size: 2.2rem;
            }

            .hero-content p {
                font-size: 1.1rem;
            }

            .quick-access-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .quick-access-title {
                font-size: 2rem;
            }

            .quick-access-item {
                padding: 2rem 1.5rem;
            }
        }
        
        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary), var(--accent));
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1.5rem;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .feature-title {
            font-size: 1.5rem;
            color: var(--dark);
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .feature-description {
            color: var(--gray);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .feature-btn {
            background: var(--secondary);
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
            font-weight: 500;
        }
        
        .feature-btn:hover {
            background: var(--secondary-dark);
            transform: translateY(-2px);
        }
        
        /* Special Cards */
        .unique-id-card .feature-icon {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }
        
        .sleep-card .feature-icon {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
        }
        
        .vaccination-card .feature-icon {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }
        
        .chatbot-card .feature-icon {
            background: linear-gradient(135deg, #607d8b, #455a64);
        }
        
        /* Baby Info Section */
        .baby-info {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .baby-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
        }
        
        .baby-name {
            font-size: 1.8rem;
            color: var(--dark);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .baby-age {
            color: var(--gray);
            font-size: 1.1rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .main-container {
                padding: 1rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .feature-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="../../home.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <h1>Baby Care</h1>
            <p>Expert guidance for nurturing your baby's health and development</p>
        </div>
    </section>

    <!-- Quick Access Section -->
    <section class="quick-access-section">
        <div class="quick-access-container">
            <h2 class="quick-access-title">Quick Access</h2>
            <p class="quick-access-subtitle">Essential tools and resources for your baby's care and development</p>
            <div class="quick-access-grid">
                <div class="quick-access-item" onclick="window.location.href='vaccinations.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-syringe"></i>
                    </div>
                    <h3>Vaccinations</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='government-schemes.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <h3>Government</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='physical-activities.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <h3>Physical Activities</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='nutrition-tracking.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <h3>Baby Nutrition</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='sleep.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-moon"></i>
                    </div>
                    <h3>Sleep Tracker</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='unique-id.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3>Unique ID</h3>
                </div>

                <div class="quick-access-item" onclick="window.location.href='chatbot.html'">
                    <div class="quick-access-icon">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h3>Voice Assistant</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-container">


    </main>

    <script>
        // Add hover effects for quick access items
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.quick-access-item');

            items.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
