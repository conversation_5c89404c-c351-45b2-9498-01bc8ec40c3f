<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Nutrition Tracking - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .age-selector {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .age-tabs {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .age-tab {
            padding: 0.75rem 1.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            background: white;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .age-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nutrition-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .nutrition-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .nutrition-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
        }

        .food-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8fafc;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .food-item:hover {
            background: #e2e8f0;
        }

        .food-name {
            font-weight: 500;
            color: #2d3748;
        }

        .food-amount {
            color: #64748b;
            font-size: 0.9rem;
        }

        .add-food-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .add-food-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }

        .tracking-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }

        .feeding-log {
            display: grid;
            gap: 1rem;
        }

        .log-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .log-time {
            font-weight: 600;
            color: #667eea;
        }

        .log-details {
            flex: 1;
            margin-left: 1rem;
        }

        .log-food {
            font-weight: 500;
            color: #2d3748;
        }

        .log-amount {
            color: #64748b;
            font-size: 0.9rem;
        }

        .quick-add {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quick-add-btn {
            padding: 1rem;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-add-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quick-add-btn.selected {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        .quick-add-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .quick-add-name {
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2d3748;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nutrition-grid {
                grid-template-columns: 1fr;
            }

            .age-tabs {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-utensils"></i> Baby Nutrition Tracking</h1>
            <p>Monitor your baby's feeding schedule and nutritional intake</p>
        </div>

        <!-- Age Selector -->
        <div class="age-selector">
            <div class="age-tabs">
                <div class="age-tab active" onclick="filterByAge('0-6')" data-age="0-6">0-6 Months</div>
                <div class="age-tab" onclick="filterByAge('6-12')" data-age="6-12">6-12 Months</div>
                <div class="age-tab" onclick="filterByAge('12-24')" data-age="12-24">12-24 Months</div>
                <div class="age-tab" onclick="filterByAge('24+')" data-age="24+">24+ Months</div>
            </div>
        </div>

        <!-- Quick Add Foods -->
        <div class="tracking-section">
            <h2 class="section-title">
                <i class="fas fa-plus-circle"></i>
                Quick Add Foods
            </h2>
            <div class="quick-add" id="quick-add-foods">
                <!-- Quick add buttons will be loaded here -->
            </div>
        </div>

        <!-- Nutrition Cards -->
        <div class="nutrition-grid" id="nutrition-cards">
            <!-- Nutrition cards will be loaded here -->
        </div>

        <!-- Feeding Chart -->
        <div class="tracking-section">
            <h2 class="section-title">
                <i class="fas fa-chart-line"></i>
                Feeding Trends
            </h2>
            <div class="chart-container">
                <canvas id="feeding-chart"></canvas>
            </div>
        </div>

        <!-- Recent Feeding Log -->
        <div class="tracking-section">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                Recent Feedings
            </h2>
            <div class="feeding-log" id="feeding-log">
                <!-- Feeding log entries will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Add Food Modal -->
    <div class="modal" id="add-food-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Add Food Entry</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="add-food-form">
                <div class="form-group">
                    <label class="form-label">Food Item</label>
                    <input type="text" class="form-input" id="food-name" placeholder="Enter food name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Amount</label>
                    <input type="text" class="form-input" id="food-amount" placeholder="e.g., 120ml, 2 tbsp" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Time</label>
                    <input type="time" class="form-input" id="food-time" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Notes (Optional)</label>
                    <input type="text" class="form-input" id="food-notes" placeholder="Any additional notes">
                </div>
                <button type="submit" class="btn-primary">Add Food Entry</button>
            </form>
        </div>
    </div>

    <script>
        let currentAge = '0-6';
        let feedingChart = null;

        // Age-specific food data
        const ageSpecificFoods = {
            '0-6': {
                quickAdd: [
                    { name: 'Breast Milk', icon: '🤱', amount: '120ml' },
                    { name: 'Formula', icon: '🍼', amount: '120ml' },
                    { name: 'Water', icon: '💧', amount: '30ml' }
                ],
                categories: [
                    {
                        title: 'Milk Feeds',
                        icon: '🍼',
                        color: 'linear-gradient(135deg, #4299e1, #3182ce)',
                        foods: [
                            { name: 'Breast Milk', amount: '120-180ml per feed' },
                            { name: 'Formula Milk', amount: '120-180ml per feed' },
                            { name: 'Expressed Milk', amount: '120ml per feed' }
                        ]
                    }
                ]
            },
            '6-12': {
                quickAdd: [
                    { name: 'Breast Milk', icon: '🤱', amount: '120ml' },
                    { name: 'Baby Cereal', icon: '🥣', amount: '2-3 tbsp' },
                    { name: 'Fruit Puree', icon: '🍎', amount: '2-3 tbsp' },
                    { name: 'Vegetable Puree', icon: '🥕', amount: '2-3 tbsp' }
                ],
                categories: [
                    {
                        title: 'Milk Feeds',
                        icon: '🍼',
                        color: 'linear-gradient(135deg, #4299e1, #3182ce)',
                        foods: [
                            { name: 'Breast Milk', amount: '120-180ml per feed' },
                            { name: 'Formula Milk', amount: '120-180ml per feed' }
                        ]
                    },
                    {
                        title: 'First Foods',
                        icon: '🥄',
                        color: 'linear-gradient(135deg, #48bb78, #38a169)',
                        foods: [
                            { name: 'Rice Cereal', amount: '2-3 tablespoons' },
                            { name: 'Banana Puree', amount: '2-3 tablespoons' },
                            { name: 'Sweet Potato Puree', amount: '2-3 tablespoons' },
                            { name: 'Avocado Mash', amount: '2-3 tablespoons' }
                        ]
                    }
                ]
            },
            '12-24': {
                quickAdd: [
                    { name: 'Whole Milk', icon: '🥛', amount: '150ml' },
                    { name: 'Finger Foods', icon: '🍌', amount: '1 piece' },
                    { name: 'Soft Solids', icon: '🍽️', amount: '1/4 cup' },
                    { name: 'Water', icon: '💧', amount: '100ml' }
                ],
                categories: [
                    {
                        title: 'Dairy & Milk',
                        icon: '🥛',
                        color: 'linear-gradient(135deg, #4299e1, #3182ce)',
                        foods: [
                            { name: 'Whole Milk', amount: '150-200ml per serving' },
                            { name: 'Yogurt', amount: '1/4 cup' },
                            { name: 'Cheese Cubes', amount: '2-3 pieces' }
                        ]
                    },
                    {
                        title: 'Finger Foods',
                        icon: '🍌',
                        color: 'linear-gradient(135deg, #48bb78, #38a169)',
                        foods: [
                            { name: 'Banana Slices', amount: '1/2 banana' },
                            { name: 'Soft Cooked Vegetables', amount: '1/4 cup' },
                            { name: 'Small Pasta', amount: '1/4 cup' },
                            { name: 'Soft Fruits', amount: '1/4 cup' }
                        ]
                    }
                ]
            },
            '24+': {
                quickAdd: [
                    { name: 'Whole Milk', icon: '🥛', amount: '200ml' },
                    { name: 'Solid Meals', icon: '🍽️', amount: '1/2 cup' },
                    { name: 'Snacks', icon: '🍪', amount: '1 piece' },
                    { name: 'Water', icon: '💧', amount: '150ml' }
                ],
                categories: [
                    {
                        title: 'Regular Meals',
                        icon: '🍽️',
                        color: 'linear-gradient(135deg, #ed8936, #dd6b20)',
                        foods: [
                            { name: 'Rice & Dal', amount: '1/2 cup' },
                            { name: 'Vegetable Curry', amount: '1/4 cup' },
                            { name: 'Chapati Pieces', amount: '1/2 chapati' },
                            { name: 'Cooked Vegetables', amount: '1/4 cup' }
                        ]
                    },
                    {
                        title: 'Snacks & Fruits',
                        icon: '🍎',
                        color: 'linear-gradient(135deg, #9f7aea, #805ad5)',
                        foods: [
                            { name: 'Fresh Fruits', amount: '1/2 cup' },
                            { name: 'Biscuits', amount: '2-3 pieces' },
                            { name: 'Dry Fruits', amount: '5-6 pieces' },
                            { name: 'Homemade Snacks', amount: '1 piece' }
                        ]
                    }
                ]
            }
        };

        // Sample feeding log data
        let feedingLog = [
            { time: '08:00', food: 'Breast Milk', amount: '120ml', notes: 'Good feeding' },
            { time: '11:30', food: 'Baby Cereal', amount: '3 tbsp', notes: 'Loved it!' },
            { time: '14:00', food: 'Fruit Puree', amount: '2 tbsp', notes: 'Apple puree' },
            { time: '17:30', food: 'Formula Milk', amount: '150ml', notes: 'Before nap' }
        ];

        // Filter by age
        function filterByAge(ageGroup) {
            currentAge = ageGroup;

            // Update active tab
            document.querySelectorAll('.age-tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.age === ageGroup) {
                    tab.classList.add('active');
                }
            });

            loadQuickAddFoods();
            loadNutritionCards();
            updateChart();
        }

        // Load quick add foods
        function loadQuickAddFoods() {
            const container = document.getElementById('quick-add-foods');
            const foods = ageSpecificFoods[currentAge].quickAdd;

            container.innerHTML = foods.map(food => `
                <div class="quick-add-btn" onclick="quickAddFood('${food.name}', '${food.amount}')">
                    <div class="quick-add-icon">${food.icon}</div>
                    <div class="quick-add-name">${food.name}</div>
                </div>
            `).join('');
        }

        // Load nutrition cards
        function loadNutritionCards() {
            const container = document.getElementById('nutrition-cards');
            const categories = ageSpecificFoods[currentAge].categories;

            container.innerHTML = categories.map(category => `
                <div class="nutrition-card">
                    <div class="card-header">
                        <div class="card-icon" style="background: ${category.color};">
                            ${category.icon}
                        </div>
                        <h3 class="card-title">${category.title}</h3>
                    </div>
                    ${category.foods.map(food => `
                        <div class="food-item">
                            <span class="food-name">${food.name}</span>
                            <span class="food-amount">${food.amount}</span>
                        </div>
                    `).join('')}
                    <button class="add-food-btn" onclick="openModal()">
                        <i class="fas fa-plus"></i> Add Custom Food
                    </button>
                </div>
            `).join('');
        }

        // Quick add food
        function quickAddFood(foodName, amount) {
            const now = new Date();
            const time = now.toTimeString().slice(0, 5);

            const newEntry = {
                time: time,
                food: foodName,
                amount: amount,
                notes: 'Quick add'
            };

            feedingLog.unshift(newEntry);
            loadFeedingLog();
            updateChart();
            showNotification(`${foodName} added successfully!`, 'success');
        }

        // Load feeding log
        function loadFeedingLog() {
            const container = document.getElementById('feeding-log');
            container.innerHTML = feedingLog.slice(0, 10).map(entry => `
                <div class="log-entry">
                    <div class="log-time">${entry.time}</div>
                    <div class="log-details">
                        <div class="log-food">${entry.food}</div>
                        <div class="log-amount">${entry.amount} ${entry.notes ? '• ' + entry.notes : ''}</div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize chart
        function initializeChart() {
            const ctx = document.getElementById('feeding-chart').getContext('2d');
            feedingChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['6 AM', '9 AM', '12 PM', '3 PM', '6 PM', '9 PM'],
                    datasets: [{
                        label: 'Milk Feeds (ml)',
                        data: [120, 150, 120, 140, 130, 120],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66, 153, 225, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Solid Foods (servings)',
                        data: [0, 1, 2, 1, 2, 0],
                        borderColor: '#48bb78',
                        backgroundColor: 'rgba(72, 187, 120, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Update chart based on age
        function updateChart() {
            if (!feedingChart) return;

            // Update chart data based on current age
            // This would typically fetch real data from an API
            feedingChart.update();
        }

        // Modal functions
        function openModal() {
            document.getElementById('add-food-modal').classList.add('active');
        }

        function closeModal() {
            document.getElementById('add-food-modal').classList.remove('active');
            document.getElementById('add-food-form').reset();
        }

        // Handle form submission
        document.getElementById('add-food-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const foodName = document.getElementById('food-name').value;
            const foodAmount = document.getElementById('food-amount').value;
            const foodTime = document.getElementById('food-time').value;
            const foodNotes = document.getElementById('food-notes').value;

            const newEntry = {
                time: foodTime,
                food: foodName,
                amount: foodAmount,
                notes: foodNotes
            };

            feedingLog.unshift(newEntry);
            loadFeedingLog();
            updateChart();
            closeModal();
            showNotification(`${foodName} added successfully!`, 'success');
        });

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
                background: ${type === 'success' ? '#48bb78' : '#f56565'};
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadQuickAddFoods();
            loadNutritionCards();
            loadFeedingLog();
            initializeChart();
        });

        // Close modal when clicking outside
        document.getElementById('add-food-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
