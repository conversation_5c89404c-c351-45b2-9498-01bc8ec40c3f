/**
 * Enhanced Authentication Service
 * Handles user authentication, role-based access control, and security measures
 */

class AuthService {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.userData = this.getUserData();
        this.refreshTimer = null;
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
        this.lastActivity = Date.now();
        
        this.initializeSessionManagement();
        this.initializeSecurityHeaders();
    }

    // Initialize session management
    initializeSessionManagement() {
        // Track user activity
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
            }, { passive: true });
        });

        // Check session validity periodically
        setInterval(() => {
            this.checkSessionValidity();
        }, 60000); // Check every minute

        // Set up token refresh
        if (this.token) {
            this.scheduleTokenRefresh();
        }
    }

    // Initialize security headers
    initializeSecurityHeaders() {
        // Add CSRF protection
        const csrfToken = this.generateCSRFToken();
        localStorage.setItem('csrf_token', csrfToken);
        
        // Set security headers for all API requests
        if (window.apiClient) {
            const originalMakeRequest = window.apiClient.makeRequest.bind(window.apiClient);
            window.apiClient.makeRequest = async (endpoint, options = {}) => {
                options.headers = {
                    ...options.headers,
                    'X-CSRF-Token': localStorage.getItem('csrf_token'),
                    'X-Requested-With': 'XMLHttpRequest'
                };
                return await originalMakeRequest(endpoint, options);
            };
        }
    }

    // Generate CSRF token
    generateCSRFToken() {
        return Array.from(crypto.getRandomValues(new Uint8Array(32)))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }

    // Update last activity timestamp
    updateLastActivity() {
        this.lastActivity = Date.now();
    }

    // Check session validity
    checkSessionValidity() {
        if (!this.token) return;

        const timeSinceLastActivity = Date.now() - this.lastActivity;
        
        if (timeSinceLastActivity > this.sessionTimeout) {
            this.logout('Session expired due to inactivity');
            return;
        }

        // Check if token is expired
        if (this.isTokenExpired()) {
            this.refreshToken();
        }
    }

    // Check if token is expired
    isTokenExpired() {
        if (!this.token) return true;

        try {
            const payload = JSON.parse(atob(this.token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            return payload.exp < currentTime;
        } catch (error) {
            console.error('Error checking token expiration:', error);
            return true;
        }
    }

    // Schedule token refresh
    scheduleTokenRefresh() {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }

        try {
            const payload = JSON.parse(atob(this.token.split('.')[1]));
            const expirationTime = payload.exp * 1000;
            const refreshTime = expirationTime - (5 * 60 * 1000); // Refresh 5 minutes before expiration
            const timeUntilRefresh = refreshTime - Date.now();

            if (timeUntilRefresh > 0) {
                this.refreshTimer = setTimeout(() => {
                    this.refreshToken();
                }, timeUntilRefresh);
            }
        } catch (error) {
            console.error('Error scheduling token refresh:', error);
        }
    }

    // Refresh authentication token
    async refreshToken() {
        try {
            const response = await fetch(`${window.apiClient.baseURL}/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.setToken(data.access_token);
                this.scheduleTokenRefresh();
            } else {
                this.logout('Token refresh failed');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            this.logout('Token refresh failed');
        }
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        localStorage.setItem('access_token', token);
        if (window.apiClient) {
            window.apiClient.setToken(token);
        }
    }

    // Get user data from localStorage
    getUserData() {
        try {
            const userData = localStorage.getItem('user_data');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error parsing user data:', error);
            return null;
        }
    }

    // Set user data
    setUserData(userData) {
        this.userData = userData;
        localStorage.setItem('user_data', JSON.stringify(userData));
    }

    // Login with enhanced security
    async login(email, password, rememberMe = false) {
        try {
            // Validate input
            if (!this.validateEmail(email)) {
                throw new Error('Invalid email format');
            }

            if (!this.validatePassword(password)) {
                throw new Error('Password must be at least 8 characters long');
            }

            // Hash password on client side for additional security
            const hashedPassword = await this.hashPassword(password);

            const response = await window.apiClient.login(email, hashedPassword);

            if (response.success) {
                this.setToken(response.data.access_token);
                this.setUserData(response.data.user);
                this.scheduleTokenRefresh();

                // Set session persistence
                if (rememberMe) {
                    localStorage.setItem('remember_me', 'true');
                } else {
                    sessionStorage.setItem('session_active', 'true');
                }

                // Log successful login
                this.logSecurityEvent('login_success', { email });

                return { success: true, user: response.data.user };
            } else {
                this.logSecurityEvent('login_failed', { email, reason: response.error });
                throw new Error(response.error);
            }
        } catch (error) {
            this.logSecurityEvent('login_error', { email, error: error.message });
            throw error;
        }
    }

    // Register with enhanced security
    async register(userData) {
        try {
            // Validate input
            if (!this.validateEmail(userData.email)) {
                throw new Error('Invalid email format');
            }

            if (!this.validatePassword(userData.password)) {
                throw new Error('Password must be at least 8 characters with uppercase, lowercase, number, and special character');
            }

            if (userData.password !== userData.confirmPassword) {
                throw new Error('Passwords do not match');
            }

            // Hash password
            userData.password = await this.hashPassword(userData.password);
            delete userData.confirmPassword;

            const response = await window.apiClient.register(userData);

            if (response.success) {
                this.setToken(response.data.access_token);
                this.setUserData(response.data.user);
                this.scheduleTokenRefresh();

                this.logSecurityEvent('registration_success', { email: userData.email });

                return { success: true, user: response.data.user };
            } else {
                this.logSecurityEvent('registration_failed', { email: userData.email, reason: response.error });
                throw new Error(response.error);
            }
        } catch (error) {
            this.logSecurityEvent('registration_error', { email: userData.email, error: error.message });
            throw error;
        }
    }

    // Logout with cleanup
    logout(reason = 'User logout') {
        // Clear tokens and data
        this.token = null;
        this.userData = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('remember_me');
        sessionStorage.removeItem('session_active');

        // Clear refresh timer
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }

        // Clear cached data
        if (window.dataSyncService) {
            window.dataSyncService.clearCache();
        }

        // Log logout
        this.logSecurityEvent('logout', { reason });

        // Redirect to login page
        if (window.location.pathname !== '/login.html') {
            window.location.href = '/login.html';
        }
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!(this.token && this.userData && !this.isTokenExpired());
    }

    // Check user role
    hasRole(role) {
        return this.userData && this.userData.role === role;
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.userData) return false;

        const rolePermissions = {
            admin: ['read', 'write', 'delete', 'manage_users', 'view_analytics', 'system_settings'],
            doctor: ['read', 'write', 'view_patients', 'manage_appointments', 'medical_records'],
            user: ['read', 'write_own', 'view_own']
        };

        const userPermissions = rolePermissions[this.userData.role] || [];
        return userPermissions.includes(permission);
    }

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Validate password strength
    validatePassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }

    // Hash password using Web Crypto API
    async hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hash = await crypto.subtle.digest('SHA-256', data);
        return Array.from(new Uint8Array(hash))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    }

    // Log security events
    logSecurityEvent(event, details = {}) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            event,
            details,
            userAgent: navigator.userAgent,
            ip: 'client-side' // In production, get from server
        };

        console.log('Security Event:', logEntry);

        // Store in local storage for admin review
        const securityLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
        securityLogs.push(logEntry);
        
        // Keep only last 100 entries
        if (securityLogs.length > 100) {
            securityLogs.splice(0, securityLogs.length - 100);
        }
        
        localStorage.setItem('security_logs', JSON.stringify(securityLogs));
    }

    // Get security logs
    getSecurityLogs() {
        return JSON.parse(localStorage.getItem('security_logs') || '[]');
    }

    // Clear security logs
    clearSecurityLogs() {
        localStorage.removeItem('security_logs');
    }
}

// Create global instance
window.authService = new AuthService();

// Global helper functions
window.isAuthenticated = () => window.authService.isAuthenticated();
window.hasRole = (role) => window.authService.hasRole(role);
window.hasPermission = (permission) => window.authService.hasPermission(permission);
window.getCurrentUser = () => window.authService.userData;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthService;
}
