<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physical Activities for Babies - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: #e91e63;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .logo-icon {
            background: #e91e63;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn {
            background: #f5f5f5;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e91e63;
            color: white;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .age-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .age-tab {
            padding: 0.75rem 1.5rem;
            background: white;
            border: 2px solid #e91e63;
            border-radius: 25px;
            color: #e91e63;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .age-tab.active,
        .age-tab:hover {
            background: #e91e63;
            color: white;
        }

        .activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .activity-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(233, 30, 99, 0.1);
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .activity-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .activity-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .activity-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .activity-age {
            font-size: 0.9rem;
            color: #718096;
            background: #f7fafc;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            display: inline-block;
        }

        .activity-description {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .activity-benefits {
            margin-bottom: 1.5rem;
        }

        .benefits-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .benefits-list {
            list-style: none;
        }

        .benefits-list li {
            padding: 0.25rem 0;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .benefits-list li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
        }

        .activity-steps {
            margin-bottom: 1.5rem;
        }

        .steps-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }

        .steps-list li {
            padding: 0.5rem 0;
            color: #4a5568;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            counter-increment: step-counter;
        }

        .steps-list li::before {
            content: counter(step-counter);
            background: #e91e63;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            flex-shrink: 0;
            margin-top: 0.1rem;
        }

        .activity-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #ad1457, #880e4f);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #e91e63;
            border: 2px solid #e91e63;
        }

        .btn-outline:hover {
            background: #e91e63;
            color: white;
        }

        .safety-tips {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .safety-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .safety-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .safety-item {
            padding: 1.5rem;
            background: #fff5f5;
            border-radius: 15px;
            border-left: 4px solid #f56565;
        }

        .safety-item-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .safety-item-desc {
            color: #4a5568;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .activities-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .activity-card {
                padding: 1.5rem;
            }

            .activity-actions {
                flex-direction: column;
            }

            .btn {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="baby-care.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Baby Care
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-running"></i>
                Physical Activities for Babies
            </h1>
            <p class="page-subtitle">
                Age-appropriate exercises and activities to support your baby's physical development and motor skills
            </p>
        </div>

        <!-- Age Selector -->
        <div class="age-selector">
            <div class="age-tab active" onclick="filterByAge('0-3')">0-3 Months</div>
            <div class="age-tab" onclick="filterByAge('3-6')">3-6 Months</div>
            <div class="age-tab" onclick="filterByAge('6-12')">6-12 Months</div>
            <div class="age-tab" onclick="filterByAge('12-24')">12-24 Months</div>
        </div>

        <!-- Activities Grid -->
        <div class="activities-grid" id="activities-container">
            <!-- Tummy Time (0-3 months) -->
            <div class="activity-card" data-age="0-3">
                <div class="activity-header">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #4caf50, #2e7d32);">
                        <i class="fas fa-baby"></i>
                    </div>
                    <div>
                        <h3 class="activity-title">Tummy Time</h3>
                        <span class="activity-age">0-3 Months</span>
                    </div>
                </div>
                <p class="activity-description">
                    Essential activity for strengthening neck, shoulder, and core muscles while preventing flat head syndrome.
                </p>
                <div class="activity-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Strengthens neck and shoulder muscles</li>
                        <li>Prevents flat head syndrome</li>
                        <li>Improves motor skills</li>
                        <li>Encourages visual development</li>
                    </ul>
                </div>
                <div class="activity-steps">
                    <h4 class="steps-title">How to do it:</h4>
                    <ol class="steps-list">
                        <li>Place baby on their tummy on a firm surface</li>
                        <li>Start with 2-3 minutes, several times a day</li>
                        <li>Stay close and interact with your baby</li>
                        <li>Use toys or mirrors to encourage lifting head</li>
                    </ol>
                </div>
                <div class="activity-actions">
                    <button class="btn btn-primary" onclick="startTimer(180)">Start 3-min Timer</button>
                    <button class="btn btn-outline" onclick="logActivity('Tummy Time')">Log Activity</button>
                </div>
            </div>

            <!-- Gentle Massage (0-3 months) -->
            <div class="activity-card" data-age="0-3">
                <div class="activity-header">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #2196f3, #1565c0);">
                        <i class="fas fa-hands"></i>
                    </div>
                    <div>
                        <h3 class="activity-title">Gentle Baby Massage</h3>
                        <span class="activity-age">0-3 Months</span>
                    </div>
                </div>
                <p class="activity-description">
                    Soothing massage techniques to promote relaxation, bonding, and healthy circulation.
                </p>
                <div class="activity-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Promotes relaxation and better sleep</li>
                        <li>Improves circulation</li>
                        <li>Strengthens parent-baby bond</li>
                        <li>May help with colic and gas</li>
                    </ul>
                </div>
                <div class="activity-steps">
                    <h4 class="steps-title">How to do it:</h4>
                    <ol class="steps-list">
                        <li>Use baby-safe oil or lotion</li>
                        <li>Start with gentle strokes on arms and legs</li>
                        <li>Use circular motions on tummy</li>
                        <li>Keep sessions short (5-10 minutes)</li>
                    </ol>
                </div>
                <div class="activity-actions">
                    <button class="btn btn-primary" onclick="startTimer(600)">Start 10-min Timer</button>
                    <button class="btn btn-outline" onclick="logActivity('Baby Massage')">Log Activity</button>
                </div>
            </div>

            <!-- Supported Sitting (3-6 months) -->
            <div class="activity-card" data-age="3-6">
                <div class="activity-header">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #ff9800, #f57c00);">
                        <i class="fas fa-chair"></i>
                    </div>
                    <div>
                        <h3 class="activity-title">Supported Sitting</h3>
                        <span class="activity-age">3-6 Months</span>
                    </div>
                </div>
                <p class="activity-description">
                    Help your baby practice sitting with support to develop core strength and balance.
                </p>
                <div class="activity-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Develops core strength</li>
                        <li>Improves balance and coordination</li>
                        <li>New perspective for exploration</li>
                        <li>Prepares for independent sitting</li>
                    </ul>
                </div>
                <div class="activity-steps">
                    <h4 class="steps-title">How to do it:</h4>
                    <ol class="steps-list">
                        <li>Sit baby between your legs for support</li>
                        <li>Use pillows around baby for safety</li>
                        <li>Place interesting toys within reach</li>
                        <li>Gradually reduce support as baby gets stronger</li>
                    </ol>
                </div>
                <div class="activity-actions">
                    <button class="btn btn-primary" onclick="startTimer(300)">Start 5-min Timer</button>
                    <button class="btn btn-outline" onclick="logActivity('Supported Sitting')">Log Activity</button>
                </div>
            </div>

            <!-- Crawling Preparation (6-12 months) -->
            <div class="activity-card" data-age="6-12">
                <div class="activity-header">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #9c27b0, #6a1b9a);">
                        <i class="fas fa-paw"></i>
                    </div>
                    <div>
                        <h3 class="activity-title">Crawling Preparation</h3>
                        <span class="activity-age">6-12 Months</span>
                    </div>
                </div>
                <p class="activity-description">
                    Activities to encourage crawling and strengthen the muscles needed for mobility.
                </p>
                <div class="activity-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Strengthens arms and legs</li>
                        <li>Develops coordination</li>
                        <li>Encourages exploration</li>
                        <li>Builds confidence in movement</li>
                    </ul>
                </div>
                <div class="activity-steps">
                    <h4 class="steps-title">How to do it:</h4>
                    <ol class="steps-list">
                        <li>Place toys just out of reach</li>
                        <li>Get on hands and knees to demonstrate</li>
                        <li>Use tunnels or obstacles to crawl through</li>
                        <li>Encourage with praise and excitement</li>
                    </ol>
                </div>
                <div class="activity-actions">
                    <button class="btn btn-primary" onclick="startTimer(900)">Start 15-min Timer</button>
                    <button class="btn btn-outline" onclick="logActivity('Crawling Practice')">Log Activity</button>
                </div>
            </div>

            <!-- Walking Practice (12-24 months) -->
            <div class="activity-card" data-age="12-24">
                <div class="activity-header">
                    <div class="activity-icon" style="background: linear-gradient(135deg, #f44336, #c62828);">
                        <i class="fas fa-walking"></i>
                    </div>
                    <div>
                        <h3 class="activity-title">Walking Practice</h3>
                        <span class="activity-age">12-24 Months</span>
                    </div>
                </div>
                <p class="activity-description">
                    Fun activities to encourage walking and improve balance and coordination.
                </p>
                <div class="activity-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Develops leg strength</li>
                        <li>Improves balance and coordination</li>
                        <li>Builds confidence</li>
                        <li>Encourages independence</li>
                    </ul>
                </div>
                <div class="activity-steps">
                    <h4 class="steps-title">How to do it:</h4>
                    <ol class="steps-list">
                        <li>Hold baby's hands while they walk</li>
                        <li>Use push toys for support</li>
                        <li>Create safe walking paths</li>
                        <li>Practice on different surfaces</li>
                    </ol>
                </div>
                <div class="activity-actions">
                    <button class="btn btn-primary" onclick="startTimer(1200)">Start 20-min Timer</button>
                    <button class="btn btn-outline" onclick="logActivity('Walking Practice')">Log Activity</button>
                </div>
            </div>
        </div>

        <!-- Safety Tips -->
        <div class="safety-tips">
            <h2 class="safety-title">
                <i class="fas fa-shield-alt"></i>
                Safety Tips
            </h2>
            <div class="safety-grid">
                <div class="safety-item">
                    <h4 class="safety-item-title">Always Supervise</h4>
                    <p class="safety-item-desc">Never leave your baby unattended during physical activities</p>
                </div>
                <div class="safety-item">
                    <h4 class="safety-item-title">Safe Environment</h4>
                    <p class="safety-item-desc">Ensure the area is clean, soft, and free from hazards</p>
                </div>
                <div class="safety-item">
                    <h4 class="safety-item-title">Follow Baby's Cues</h4>
                    <p class="safety-item-desc">Stop if baby seems tired, fussy, or uncomfortable</p>
                </div>
                <div class="safety-item">
                    <h4 class="safety-item-title">Age-Appropriate</h4>
                    <p class="safety-item-desc">Only do activities suitable for your baby's developmental stage</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentTimer = null;
        let timerDisplay = null;

        // Age filtering functionality
        function filterByAge(ageGroup) {
            const cards = document.querySelectorAll('.activity-card');
            const tabs = document.querySelectorAll('.age-tab');

            // Update active tab
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // Filter cards
            cards.forEach(card => {
                if (card.dataset.age === ageGroup) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Timer functionality
        function startTimer(seconds) {
            if (currentTimer) {
                clearInterval(currentTimer);
            }

            let timeLeft = seconds;
            showNotification(`Timer started for ${Math.floor(seconds/60)} minutes`, 'info');

            // Create timer display
            if (!timerDisplay) {
                timerDisplay = document.createElement('div');
                timerDisplay.style.cssText = `
                    position: fixed;
                    top: 100px;
                    right: 20px;
                    background: #e91e63;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    font-size: 1.2rem;
                    font-weight: bold;
                    z-index: 10000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
                document.body.appendChild(timerDisplay);
            }

            currentTimer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(currentTimer);
                    showNotification('Timer finished! Great job!', 'success');
                    if (timerDisplay) {
                        timerDisplay.remove();
                        timerDisplay = null;
                    }
                    currentTimer = null;
                }
                timeLeft--;
            }, 1000);
        }

        // Activity logging
        function logActivity(activityName) {
            const timestamp = new Date().toLocaleString();
            showNotification(`${activityName} logged successfully!`, 'success');

            // In a real app, this would save to a database
            console.log(`Activity logged: ${activityName} at ${timestamp}`);
        }

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            switch(type) {
                case 'success':
                    notification.style.backgroundColor = '#4caf50';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#f44336';
                    break;
                case 'info':
                    notification.style.backgroundColor = '#2196f3';
                    break;
                default:
                    notification.style.backgroundColor = '#333';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show 0-3 months activities by default
            filterByAge('0-3');

            // Add hover effects
            const cards = document.querySelectorAll('.activity-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
