<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Schemes - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: #e91e63;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .logo-icon {
            background: #e91e63;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn {
            background: #f5f5f5;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e91e63;
            color: white;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .schemes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .scheme-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(233, 30, 99, 0.1);
        }

        .scheme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .scheme-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .scheme-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .scheme-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .scheme-category {
            font-size: 0.9rem;
            color: #718096;
            background: #f7fafc;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            display: inline-block;
        }

        .scheme-description {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .scheme-benefits {
            margin-bottom: 1.5rem;
        }

        .benefits-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .benefits-list {
            list-style: none;
        }

        .benefits-list li {
            padding: 0.25rem 0;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .benefits-list li::before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
        }

        .scheme-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #ad1457, #880e4f);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #e91e63;
            border: 2px solid #e91e63;
        }

        .btn-outline:hover {
            background: #e91e63;
            color: white;
        }

        .eligibility-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .eligibility-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .eligibility-item {
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #e91e63;
        }

        .eligibility-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .eligibility-desc {
            color: #4a5568;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .schemes-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .scheme-card {
                padding: 1.5rem;
            }

            .scheme-actions {
                flex-direction: column;
            }

            .btn {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="pregcare.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Pregnancy Care
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-hands-helping"></i>
                Government Schemes
            </h1>
            <p class="page-subtitle">
                Comprehensive guide to government benefits and support programs for pregnant mothers and families
            </p>
        </div>

        <!-- Government Schemes Grid -->
        <div class="schemes-grid">
            <!-- Pradhan Mantri Matru Vandana Yojana -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #4caf50, #2e7d32);">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Pradhan Mantri Matru Vandana Yojana</h3>
                        <span class="scheme-category">Maternity Benefit</span>
                    </div>
                </div>
                <p class="scheme-description">
                    A maternity benefit program providing financial assistance to pregnant and lactating mothers for the first living child.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>₹5,000 cash incentive in three installments</li>
                        <li>Compensation for wage loss during pregnancy</li>
                        <li>Better nutrition and healthcare access</li>
                        <li>Institutional delivery promotion</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('PMMVY')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('PMMVY')">View Details</a>
                </div>
            </div>

            <!-- Janani Suraksha Yojana -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #2196f3, #1565c0);">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Janani Suraksha Yojana</h3>
                        <span class="scheme-category">Safe Delivery</span>
                    </div>
                </div>
                <p class="scheme-description">
                    A safe motherhood intervention program to reduce maternal and neonatal mortality by promoting institutional delivery.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Cash assistance for institutional delivery</li>
                        <li>Free delivery and postnatal care</li>
                        <li>Transportation support</li>
                        <li>ASHA worker assistance</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('JSY')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('JSY')">View Details</a>
                </div>
            </div>

            <!-- Integrated Child Development Services -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #ff9800, #f57c00);">
                        <i class="fas fa-child"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Integrated Child Development Services</h3>
                        <span class="scheme-category">Child Development</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Comprehensive program for early childhood care and development, providing nutrition and health services.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Supplementary nutrition for children and mothers</li>
                        <li>Immunization and health check-ups</li>
                        <li>Pre-school education</li>
                        <li>Nutrition and health education</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('ICDS')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('ICDS')">View Details</a>
                </div>
            </div>

            <!-- Pradhan Mantri Surakshit Matritva Abhiyan -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #9c27b0, #6a1b9a);">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Pradhan Mantri Surakshit Matritva Abhiyan</h3>
                        <span class="scheme-category">Healthcare</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Ensures comprehensive and quality antenatal care to all pregnant women on the 9th of every month.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Free antenatal check-ups</li>
                        <li>High-risk pregnancy identification</li>
                        <li>Specialist doctor consultations</li>
                        <li>Essential diagnostic tests</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('PMSMA')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('PMSMA')">View Details</a>
                </div>
            </div>

            <!-- Rashtriya Bal Swasthya Karyakram -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #f44336, #c62828);">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Rashtriya Bal Swasthya Karyakram</h3>
                        <span class="scheme-category">Child Health</span>
                    </div>
                </div>
                <p class="scheme-description">
                    Child health screening and early intervention services for defects at birth, diseases, and developmental delays.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>Free health screening for children</li>
                        <li>Early detection of health issues</li>
                        <li>Treatment and management support</li>
                        <li>Referral services for specialized care</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('RBSK')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('RBSK')">View Details</a>
                </div>
            </div>

            <!-- Pradhan Mantri Jan Arogya Yojana -->
            <div class="scheme-card">
                <div class="scheme-header">
                    <div class="scheme-icon" style="background: linear-gradient(135deg, #00bcd4, #0097a7);">
                        <i class="fas fa-hospital"></i>
                    </div>
                    <div>
                        <h3 class="scheme-title">Pradhan Mantri Jan Arogya Yojana</h3>
                        <span class="scheme-category">Health Insurance</span>
                    </div>
                </div>
                <p class="scheme-description">
                    World's largest health insurance scheme providing coverage up to ₹5 lakh per family per year.
                </p>
                <div class="scheme-benefits">
                    <h4 class="benefits-title">Benefits:</h4>
                    <ul class="benefits-list">
                        <li>₹5 lakh annual health coverage</li>
                        <li>Cashless treatment at empaneled hospitals</li>
                        <li>Pre and post-hospitalization coverage</li>
                        <li>Maternity and newborn care coverage</li>
                    </ul>
                </div>
                <div class="scheme-actions">
                    <a href="#" class="btn btn-primary" onclick="applyForScheme('PMJAY')">Apply Now</a>
                    <a href="#" class="btn btn-outline" onclick="viewDetails('PMJAY')">View Details</a>
                </div>
            </div>
        </div>

        <!-- Eligibility Section -->
        <div class="eligibility-section">
            <h2 class="section-title">
                <i class="fas fa-check-circle"></i>
                General Eligibility Criteria
            </h2>
            <div class="eligibility-grid">
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Age Requirement</h4>
                    <p class="eligibility-desc">Pregnant women aged 19 years and above are eligible for most schemes</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Income Criteria</h4>
                    <p class="eligibility-desc">BPL families and those with annual income below specified limits</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Documentation</h4>
                    <p class="eligibility-desc">Aadhaar card, bank account, and pregnancy registration required</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Institutional Delivery</h4>
                    <p class="eligibility-desc">Many schemes require delivery at government or accredited private facilities</p>
                </div>
            </div>
        </div>

        <!-- How to Apply Section -->
        <div class="eligibility-section">
            <h2 class="section-title">
                <i class="fas fa-clipboard-list"></i>
                How to Apply
            </h2>
            <div class="eligibility-grid">
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Step 1: Registration</h4>
                    <p class="eligibility-desc">Register pregnancy at nearest Anganwadi Center or PHC</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Step 2: Documentation</h4>
                    <p class="eligibility-desc">Submit required documents including Aadhaar, bank details, and medical records</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Step 3: Verification</h4>
                    <p class="eligibility-desc">Officials will verify your eligibility and process the application</p>
                </div>
                <div class="eligibility-item">
                    <h4 class="eligibility-title">Step 4: Benefits</h4>
                    <p class="eligibility-desc">Receive benefits directly in your bank account or through service delivery</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Scheme application functionality
        function applyForScheme(schemeCode) {
            showNotification(`Redirecting to ${schemeCode} application portal...`, 'info');

            // In a real application, this would redirect to the actual government portal
            setTimeout(() => {
                showNotification(`Application for ${schemeCode} initiated. Please complete the process on the official portal.`, 'success');
            }, 2000);
        }

        function viewDetails(schemeCode) {
            showNotification(`Loading detailed information for ${schemeCode}...`, 'info');

            // In a real application, this would show a modal or redirect to detailed page
            setTimeout(() => {
                showModal(schemeCode);
            }, 1000);
        }

        function showModal(schemeCode) {
            const schemeDetails = {
                'PMMVY': {
                    title: 'Pradhan Mantri Matru Vandana Yojana',
                    details: 'This scheme provides ₹5,000 in three installments: ₹1,000 after early registration, ₹2,000 after 6 months of pregnancy, and ₹2,000 after birth registration.',
                    eligibility: 'Pregnant and lactating mothers for their first living child, aged 19 years and above.',
                    documents: 'Aadhaar card, bank account details, pregnancy registration certificate, institutional delivery certificate.'
                },
                'JSY': {
                    title: 'Janani Suraksha Yojana',
                    details: 'Provides cash assistance for institutional delivery. Rural areas: ₹1,400, Urban areas: ₹1,000.',
                    eligibility: 'All pregnant women belonging to BPL families, and all pregnant women irrespective of age and number of children in low performing states.',
                    documents: 'BPL card, Aadhaar card, bank account details, delivery certificate from accredited institution.'
                },
                'ICDS': {
                    title: 'Integrated Child Development Services',
                    details: 'Provides supplementary nutrition, immunization, health check-ups, and pre-school education.',
                    eligibility: 'Children below 6 years, pregnant and lactating mothers.',
                    documents: 'Aadhaar card, birth certificate, immunization card, income certificate.'
                },
                'PMSMA': {
                    title: 'Pradhan Mantri Surakshit Matritva Abhiyan',
                    details: 'Free antenatal care on 9th of every month by qualified doctors.',
                    eligibility: 'All pregnant women in their second and third trimester.',
                    documents: 'Pregnancy registration, Aadhaar card, previous medical records.'
                },
                'RBSK': {
                    title: 'Rashtriya Bal Swasthya Karyakram',
                    details: 'Free health screening and treatment for children from birth to 18 years.',
                    eligibility: 'All children from birth to 18 years.',
                    documents: 'Birth certificate, Aadhaar card, school enrollment certificate (if applicable).'
                },
                'PMJAY': {
                    title: 'Pradhan Mantri Jan Arogya Yojana',
                    details: 'Health insurance coverage up to ₹5 lakh per family per year.',
                    eligibility: 'Families identified in SECC-2011 database and meeting deprivation criteria.',
                    documents: 'Aadhaar card, ration card, SECC verification, family income certificate.'
                }
            };

            const scheme = schemeDetails[schemeCode];
            if (scheme) {
                alert(`${scheme.title}\n\nDetails: ${scheme.details}\n\nEligibility: ${scheme.eligibility}\n\nRequired Documents: ${scheme.documents}`);
            }
        }

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            switch(type) {
                case 'success':
                    notification.style.backgroundColor = '#4caf50';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#f44336';
                    break;
                case 'info':
                    notification.style.backgroundColor = '#2196f3';
                    break;
                default:
                    notification.style.backgroundColor = '#333';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.scheme-card');

            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
