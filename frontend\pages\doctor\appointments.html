<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MotherCare - Doctor Appointments</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #FF66B2;
            --secondary: #FF99CC;
            --accent: #FF3385;
            --light: #FFF0F5;
            --dark: #4D0026;
            --text: #333333;
            --white: #FFFFFF;
            --success: #4CAF50;
            --warning: #FFC107;
            --gray: #f5f5f5;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
        body {
            background-color: var(--light);
            color: var(--text);
            line-height: 1.6;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            color: var(--white);
            padding: 1rem 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 3px solid rgba(255, 255, 255, 0.2);
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .logo img {
            height: 50px;
            margin-right: 12px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .logo-text {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(to right, var(--white), #FFE6F2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1.5rem;
        }
        
        .nav-menu li {
            position: relative;
        }
        
        .nav-menu a {
            color: var(--white);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Main Content Styles */
        .appointment-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1.5rem;
        }
        
        .page-title {
            color: var(--dark);
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 2.2rem;
            position: relative;
            padding-bottom: 15px;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--accent);
            border-radius: 2px;
        }
        
        .page-subtitle {
            text-align: center;
            color: var(--text);
            margin-bottom: 2.5rem;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .appointment-layout {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        @media (min-width: 992px) {
            .appointment-layout {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .section-card {
            background: var(--white);
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 1.8rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .section-card:hover {
            transform: translateY(-5px);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--dark);
            margin-bottom: 1.5rem;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--light);
        }
        
        .section-title i {
            color: var(--accent);
            font-size: 1.4rem;
        }
        
        /* Form Styles */
        .form-group {
            margin-bottom: 1.2rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--accent);
            box-shadow: 0 0 0 2px rgba(255, 51, 133, 0.2);
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: var(--accent);
            color: var(--white);
        }
        
        .btn-primary:hover {
            background: #e02a75;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--accent);
            color: var(--accent);
        }
        
        .btn-outline:hover {
            background: rgba(255, 51, 133, 0.1);
        }
        
        /* Doctor Cards */
        .doctor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .doctor-card {
            background: var(--white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-5px);
        }
        
        .doctor-header {
            padding: 20px;
            text-align: center;
            background: linear-gradient(to right, var(--primary), var(--accent));
            color: white;
        }
        
        .doctor-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            margin: 0 auto 15px;
            overflow: hidden;
        }
        
        .doctor-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .doctor-name {
            font-size: 1.3rem;
            margin-bottom: 5px;
        }
        
        .doctor-specialty {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .doctor-details {
            padding: 20px;
        }
        
        .doctor-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }
        
        .doctor-info i {
            width: 25px;
            color: var(--accent);
        }
        
        .doctor-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        /* Appointment List */
        .appointment-list {
            list-style: none;
        }
        
        .appointment-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        
        .appointment-item:last-child {
            border-bottom: none;
        }
        
        .appointment-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 102, 178, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .appointment-icon i {
            color: var(--accent);
            font-size: 1.2rem;
        }
        
        .appointment-details {
            flex-grow: 1;
        }
        
        .appointment-details h4 {
            margin-bottom: 5px;
            color: var(--dark);
        }
        
        .appointment-time {
            font-size: 0.9rem;
            color: var(--text);
            display: flex;
            gap: 15px;
            margin-top: 5px;
        }
        
        .appointment-time span {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .appointment-actions {
            display: flex;
            gap: 10px;
        }
        
        .status-badge {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-upcoming {
            background: rgba(76, 175, 80, 0.1);
            color: var(--success);
        }
        
        .status-completed {
            background: rgba(158, 158, 158, 0.1);
            color: #9E9E9E;
        }
        
        /* Footer Styles */
        .footer {
            background: linear-gradient(to right, var(--dark), #660033);
            color: var(--white);
            padding: 3rem 0;
            margin-top: 3rem;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .footer-section h3 {
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 2px;
            background: var(--accent);
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 0.8rem;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .footer-links a:hover {
            color: var(--white);
            transform: translateX(5px);
        }
        
        .copyright {
            text-align: center;
            padding: 1.5rem 0;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 2rem;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .nav-menu {
                display: none;
            }
            
            .appointment-layout {
                grid-template-columns: 1fr;
            }
            
            .doctor-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo">
                <img src="https://i.ibb.co/6n0hLML/pregnancy-logo.png" alt="MotherCare Logo">
                <div class="logo-text">MotherCare</div>
            </div>
            
            <ul class="nav-menu">
                <li><a href="#"><i class="fas fa-home"></i> Home</a></li>
                <li><a href="#"><i class="fas fa-baby-carriage"></i> Pregnancy</a></li>
                <li><a href="#"><i class="fas fa-calendar-check"></i> Appointments</a></li>
                <li><a href="#"><i class="fas fa-user-md"></i> Doctors</a></li>
            </ul>
            
            <div class="auth-buttons">
                <button class="btn btn-outline"><i class="fas fa-user"></i> My Account</button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="appointment-container">
        <h1 class="page-title">Doctor Appointments</h1>
        <p class="page-subtitle">Book and manage your prenatal appointments with our experienced obstetricians and gynecologists. Your health and your baby's development are our top priority.</p>
        
        <div class="appointment-layout">
            <!-- Left Column: Booking and Upcoming Appointments -->
            <div>
                <!-- Appointment Booking Form -->
                <div class="section-card">
                    <h2 class="section-title"><i class="fas fa-calendar-plus"></i> Book New Appointment</h2>
                    <form id="appointmentForm">
                        <div class="form-group">
                            <label for="doctorSelect">Select Doctor</label>
                            <select class="form-control" id="doctorSelect">
                                <option value="">Choose a doctor</option>
                                <option value="dr-susan">Dr. Susan Chen - Obstetrician</option>
                                <option value="dr-michael">Dr. Michael Johnson - Gynecologist</option>
                                <option value="dr-emily">Dr. Emily Rodriguez - Maternal-Fetal Medicine</option>
                                <option value="dr-robert">Dr. Robert Kim - Perinatologist</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="appointmentDate">Appointment Date</label>
                            <input type="date" class="form-control" id="appointmentDate" min="2023-08-01">
                        </div>
                        
                        <div class="form-group">
                            <label for="appointmentTime">Preferred Time</label>
                            <select class="form-control" id="appointmentTime">
                                <option value="">Select time slot</option>
                                <option value="9:00">9:00 AM</option>
                                <option value="10:30">10:30 AM</option>
                                <option value="12:00">12:00 PM</option>
                                <option value="2:00">2:00 PM</option>
                                <option value="3:30">3:30 PM</option>
                                <option value="5:00">5:00 PM</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="appointmentReason">Reason for Appointment</label>
                            <textarea class="form-control" id="appointmentReason" rows="3" placeholder="Please describe the reason for your visit"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Book Appointment</button>
                    </form>
                </div>
                
                <!-- Upcoming Appointments -->
                <div class="section-card">
                    <h2 class="section-title"><i class="fas fa-calendar-alt"></i> Upcoming Appointments</h2>
                    <ul class="appointment-list">
                        <li class="appointment-item">
                            <div class="appointment-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="appointment-details">
                                <h4>Dr. Susan Chen</h4>
                                <p>Routine Checkup - 28 Weeks</p>
                                <div class="appointment-time">
                                    <span><i class="far fa-calendar"></i> August 15, 2023</span>
                                    <span><i class="far fa-clock"></i> 10:30 AM</span>
                                </div>
                            </div>
                            <div class="appointment-actions">
                                <span class="status-badge status-upcoming">Confirmed</span>
                                <button class="btn btn-outline">Reschedule</button>
                            </div>
                        </li>
                        
                        <li class="appointment-item">
                            <div class="appointment-icon">
                                <i class="fas fa-stethoscope"></i>
                            </div>
                            <div class="appointment-details">
                                <h4>Dr. Michael Johnson</h4>
                                <p>Ultrasound Scan</p>
                                <div class="appointment-time">
                                    <span><i class="far fa-calendar"></i> August 22, 2023</span>
                                    <span><i class="far fa-clock"></i> 2:00 PM</span>
                                </div>
                            </div>
                            <div class="appointment-actions">
                                <span class="status-badge status-upcoming">Confirmed</span>
                                <button class="btn btn-outline">Reschedule</button>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Right Column: Doctor Search -->
            <div>
                <!-- Doctor Search -->
                <div class="section-card">
                    <h2 class="section-title"><i class="fas fa-search"></i> Find a Doctor</h2>
                    <div class="form-group">
                        <input type="text" class="form-control" placeholder="Search by name, specialty or location">
                    </div>
                    
                    <div class="form-group">
                        <label>Specialty</label>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn btn-outline">Obstetrics</button>
                            <button class="btn btn-outline">Gynecology</button>
                            <button class="btn btn-outline">Maternal-Fetal</button>
                            <button class="btn btn-outline">Ultrasound</button>
                        </div>
                    </div>
                </div>
                
                <!-- Available Doctors -->
                <div class="section-card">
                    <h2 class="section-title"><i class="fas fa-user-md"></i> Recommended Doctors</h2>
                    <div class="doctor-grid">
                        <!-- Doctor 1 -->
                        <div class="doctor-card">
                            <div class="doctor-header">
                                <div class="doctor-avatar">
                                    <img src="https://i.ibb.co/L0J3s1t/doctor1.jpg" alt="Dr. Susan Chen">
                                </div>
                                <h3 class="doctor-name">Dr. Susan Chen</h3>
                                <p class="doctor-specialty">Obstetrician & Gynecologist</p>
                            </div>
                            <div class="doctor-details">
                                <div class="doctor-info">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>15 years experience</span>
                                </div>
                                <div class="doctor-info">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (245 reviews)</span>
                                </div>
                                <div class="doctor-info">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>MotherCare Center, Downtown</span>
                                </div>
                                <div class="doctor-actions">
                                    <button class="btn btn-primary">View Profile</button>
                                    <button class="btn btn-outline">Book Now</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Doctor 2 -->
                        <div class="doctor-card">
                            <div class="doctor-header">
                                <div class="doctor-avatar">
                                    <img src="https://i.ibb.co/1G0GxSX/doctor2.jpg" alt="Dr. Michael Johnson">
                                </div>
                                <h3 class="doctor-name">Dr. Michael Johnson</h3>
                                <p class="doctor-specialty">Maternal-Fetal Medicine</p>
                            </div>
                            <div class="doctor-details">
                                <div class="doctor-info">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>12 years experience</span>
                                </div>
                                <div class="doctor-info">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (198 reviews)</span>
                                </div>
                                <div class="doctor-info">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>MotherCare Center, Westside</span>
                                </div>
                                <div class="doctor-actions">
                                    <button class="btn btn-primary">View Profile</button>
                                    <button class="btn btn-outline">Book Now</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Appointment Tips -->
                <div class="section-card">
                    <h2 class="section-title"><i class="fas fa-lightbulb"></i> Appointment Tips</h2>
                    <ul style="list-style: none; padding-left: 20px;">
                        <li style="margin-bottom: 15px; display: flex; gap: 10px;">
                            <i class="fas fa-check-circle" style="color: var(--accent); margin-top: 4px;"></i>
                            <span>Arrive 15 minutes early for paperwork</span>
                        </li>
                        <li style="margin-bottom: 15px; display: flex; gap: 10px;">
                            <i class="fas fa-check-circle" style="color: var(--accent); margin-top: 4px;"></i>
                            <span>Bring your pregnancy journal and test results</span>
                        </li>
                        <li style="margin-bottom: 15px; display: flex; gap: 10px;">
                            <i class="fas fa-check-circle" style="color: var(--accent); margin-top: 4px;"></i>
                            <span>Prepare a list of questions for your doctor</span>
                        </li>
                        <li style="display: flex; gap: 10px;">
                            <i class="fas fa-check-circle" style="color: var(--accent); margin-top: 4px;"></i>
                            <span>Wear comfortable clothing for examinations</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-section">
                <h3>MotherCare</h3>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1.5rem;">Your trusted partner for pregnancy care and maternal health services.</p>
                <div style="display: flex; gap: 15px;">
                    <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-facebook"></i></a>
                    <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-instagram"></i></a>
                    <a href="#" style="color: white; font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
                </div>
            </div>
            
            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="#"><i class="fas fa-chevron-right"></i> Home</a></li>
                    <li><a href="#"><i class="fas fa-chevron-right"></i> Pregnancy Tracker</a></li>
                    <li><a href="#"><i class="fas fa-chevron-right"></i> Doctor Directory</a></li>
                    <li><a href="#"><i class="fas fa-chevron-right"></i> Nutrition Guide</a></li>
                    <li><a href="#"><i class="fas fa-chevron-right"></i> Community Support</a></li>
                </ul>
            </div>
            
            <div class="footer-section">
                <h3>Contact Us</h3>
                <ul class="footer-links">
                    <li><a href="#"><i class="fas fa-map-marker-alt"></i> 123 Care Street, Motherville</a></li>
                    <li><a href="tel:+11234567890"><i class="fas fa-phone"></i> (*************</a></li>
                    <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a></li>
                    <li><a href="#"><i class="fas fa-calendar-check"></i> Book Appointment</a></li>
                    <li><a href="#"><i class="fas fa-question-circle"></i> Support Center</a></li>
                </ul>
            </div>
        </div>
        
        <div class="copyright">
            &copy; 2023 MotherCare. All rights reserved. Providing compassionate care for mothers-to-be.
        </div>
    </footer>

    <script>
        // Set minimum date for appointment to today
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        document.getElementById('appointmentDate').min = formattedDate;
        
        // Form submission handler
        document.getElementById('appointmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const doctor = document.getElementById('doctorSelect').value;
            const date = document.getElementById('appointmentDate').value;
            const time = document.getElementById('appointmentTime').value;
            const reason = document.getElementById('appointmentReason').value;
            
            // Simple validation
            if (!doctor || !date || !time) {
                alert('Please fill in all required fields');
                return;
            }
            
            // In a real app, this would send to a server
            alert('Appointment booked successfully! We will send you a confirmation shortly.');
            
            // Reset form
            this.reset();
        });
        
        // Simulate appointment booking
        document.querySelectorAll('.btn-outline').forEach(button => {
            if (button.textContent.includes('Book Now')) {
                button.addEventListener('click', function() {
                    const doctorName = this.closest('.doctor-card').querySelector('.doctor-name').textContent;
                    alert(`Starting booking process with ${doctorName}`);
                });
            }
        });
    </script>
</body>
</html>