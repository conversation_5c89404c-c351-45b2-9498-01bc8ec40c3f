<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Profile - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="../mother/profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">👶 MCHS - Baby Profile</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Baby Profile</h1>
                <div class="page-actions">
                    <button onclick="addBaby()" class="btn btn-primary">Add New Baby</button>
                    <button onclick="editProfile()" class="btn btn-outline" id="edit-btn">Edit Profile</button>
                </div>
            </div>

            <!-- Baby Selection -->
            <div class="card mb-4" id="baby-selector">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="selected-baby">Select Baby</label>
                                <select id="selected-baby" class="form-control" onchange="loadBabyProfile()">
                                    <option value="">Select a baby...</option>
                                    <!-- Babies will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6 text-right">
                            <button onclick="addBaby()" class="btn btn-outline">Add Another Baby</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Baby Profile Content -->
            <div id="baby-profile-content" style="display: none;">
                <div class="row">
                    <!-- Main Profile -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Basic Information</h3>
                            </div>
                            <div class="card-body">
                                <form id="baby-profile-form">
                                    <input type="hidden" id="baby_id" name="baby_id">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="baby_name">Baby's Name</label>
                                                <input type="text" id="baby_name" name="baby_name" class="form-control" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="gender">Gender</label>
                                                <select id="gender" name="gender" class="form-control" disabled>
                                                    <option value="">Select Gender</option>
                                                    <option value="male">Male</option>
                                                    <option value="female">Female</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="date_of_birth">Date of Birth</label>
                                                <input type="date" id="date_of_birth" name="date_of_birth" class="form-control" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="time_of_birth">Time of Birth</label>
                                                <input type="time" id="time_of_birth" name="time_of_birth" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="birth_weight">Birth Weight (kg)</label>
                                                <input type="number" id="birth_weight" name="birth_weight" class="form-control" step="0.01" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="birth_length">Birth Length (cm)</label>
                                                <input type="number" id="birth_length" name="birth_length" class="form-control" step="0.1" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="head_circumference">Head Circumference (cm)</label>
                                                <input type="number" id="head_circumference" name="head_circumference" class="form-control" step="0.1" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="blood_type">Blood Type</label>
                                                <select id="blood_type" name="blood_type" class="form-control" disabled>
                                                    <option value="">Unknown</option>
                                                    <option value="A+">A+</option>
                                                    <option value="A-">A-</option>
                                                    <option value="B+">B+</option>
                                                    <option value="B-">B-</option>
                                                    <option value="AB+">AB+</option>
                                                    <option value="AB-">AB-</option>
                                                    <option value="O+">O+</option>
                                                    <option value="O-">O-</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="birth_place">Place of Birth</label>
                                                <input type="text" id="birth_place" name="birth_place" class="form-control" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="pediatrician">Pediatrician</label>
                                        <input type="text" id="pediatrician" name="pediatrician" class="form-control" readonly>
                                    </div>

                                    <div class="form-group">
                                        <label for="allergies">Known Allergies</label>
                                        <textarea id="allergies" name="allergies" class="form-control" rows="2" readonly></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="medical_conditions">Medical Conditions</label>
                                        <textarea id="medical_conditions" name="medical_conditions" class="form-control" rows="2" readonly></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="notes">Additional Notes</label>
                                        <textarea id="notes" name="notes" class="form-control" rows="3" readonly></textarea>
                                    </div>

                                    <div class="form-actions" id="form-actions" style="display: none;">
                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                        <button type="button" onclick="cancelEdit()" class="btn btn-secondary">Cancel</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Identification Information -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Identification Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="unique_id">Unique Baby ID</label>
                                            <input type="text" id="unique_id" class="form-control" readonly>
                                            <small class="form-text text-muted">This ID is automatically generated and cannot be changed.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="birth_certificate">Birth Certificate Number</label>
                                            <input type="text" id="birth_certificate" name="birth_certificate" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="hospital_id">Hospital ID</label>
                                            <input type="text" id="hospital_id" name="hospital_id" class="form-control" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="insurance_id">Insurance ID</label>
                                            <input type="text" id="insurance_id" name="insurance_id" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-md-4">
                        <!-- Baby Photo -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Baby Photo</h3>
                            </div>
                            <div class="card-body text-center">
                                <div class="baby-photo-container">
                                    <img id="baby-photo" src="../../images/default-baby.png" alt="Baby Photo" class="baby-photo">
                                    <div class="photo-upload" id="photo-upload" style="display: none;">
                                        <input type="file" id="photo-input" accept="image/*" onchange="uploadBabyPhoto()">
                                        <label for="photo-input" class="btn btn-outline">Change Photo</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Quick Stats</h3>
                            </div>
                            <div class="card-body">
                                <div class="baby-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Age</span>
                                        <span class="stat-value" id="baby-age">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Current Weight</span>
                                        <span class="stat-value" id="current-weight">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Current Length</span>
                                        <span class="stat-value" id="current-length">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Next Checkup</span>
                                        <span class="stat-value" id="next-checkup">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Next Vaccination</span>
                                        <span class="stat-value" id="next-vaccination">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-body">
                                <div class="quick-actions">
                                    <a href="growth.html" class="btn btn-outline btn-block mb-2">View Growth Chart</a>
                                    <a href="vaccinations.html" class="btn btn-outline btn-block mb-2">Vaccination Schedule</a>
                                    <a href="milestones.html" class="btn btn-outline btn-block mb-2">Development Milestones</a>
                                    <a href="checkups.html" class="btn btn-outline btn-block">Health Checkups</a>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contacts -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Emergency Contacts</h3>
                            </div>
                            <div class="card-body">
                                <div class="emergency-contacts">
                                    <div class="contact-item">
                                        <strong>Pediatrician</strong>
                                        <p id="pediatrician-contact">-</p>
                                    </div>
                                    <div class="contact-item">
                                        <strong>Emergency Services</strong>
                                        <p>911</p>
                                    </div>
                                    <div class="contact-item">
                                        <strong>Poison Control</strong>
                                        <p>1-800-222-1222</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Baby Modal -->
    <div id="add-baby-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Baby</h3>
                <span class="close" onclick="closeAddBabyModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-baby-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_baby_name">Baby's Name</label>
                                <input type="text" id="new_baby_name" name="baby_name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_gender">Gender</label>
                                <select id="new_gender" name="gender" class="form-control" required>
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_date_of_birth">Date of Birth</label>
                                <input type="date" id="new_date_of_birth" name="date_of_birth" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_time_of_birth">Time of Birth</label>
                                <input type="time" id="new_time_of_birth" name="time_of_birth" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="new_birth_weight">Birth Weight (kg)</label>
                                <input type="number" id="new_birth_weight" name="birth_weight" class="form-control" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="new_birth_length">Birth Length (cm)</label>
                                <input type="number" id="new_birth_length" name="birth_length" class="form-control" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="new_head_circumference">Head Circumference (cm)</label>
                                <input type="number" id="new_head_circumference" name="head_circumference" class="form-control" step="0.1">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="new_birth_place">Place of Birth</label>
                        <input type="text" id="new_birth_place" name="birth_place" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="new_pediatrician">Pediatrician</label>
                        <input type="text" id="new_pediatrician" name="pediatrician" class="form-control">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Add Baby</button>
                        <button type="button" onclick="closeAddBabyModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentBaby = null;
        let isEditing = false;

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBabies();
        });

        async function loadBabies() {
            try {
                const response = await apiCall('/mother/babies', 'GET');
                if (response.success) {
                    const select = document.getElementById('selected-baby');
                    select.innerHTML = '<option value="">Select a baby...</option>';
                    
                    response.data.forEach(baby => {
                        const option = document.createElement('option');
                        option.value = baby.id;
                        option.textContent = baby.baby_name;
                        select.appendChild(option);
                    });

                    // Auto-select if only one baby
                    if (response.data.length === 1) {
                        select.value = response.data[0].id;
                        loadBabyProfile();
                    }
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        async function loadBabyProfile() {
            const babyId = document.getElementById('selected-baby').value;
            if (!babyId) {
                document.getElementById('baby-profile-content').style.display = 'none';
                return;
            }

            try {
                const response = await apiCall(`/mother/babies/${babyId}`, 'GET');
                if (response.success) {
                    currentBaby = response.data;
                    displayBabyProfile(currentBaby);
                    document.getElementById('baby-profile-content').style.display = 'block';
                }
            } catch (error) {
                console.error('Error loading baby profile:', error);
            }
        }

        function displayBabyProfile(baby) {
            // Fill form fields
            document.getElementById('baby_id').value = baby.id;
            document.getElementById('baby_name').value = baby.baby_name || '';
            document.getElementById('gender').value = baby.gender || '';
            document.getElementById('date_of_birth').value = baby.date_of_birth || '';
            document.getElementById('time_of_birth').value = baby.time_of_birth || '';
            document.getElementById('birth_weight').value = baby.birth_weight || '';
            document.getElementById('birth_length').value = baby.birth_length || '';
            document.getElementById('head_circumference').value = baby.head_circumference || '';
            document.getElementById('blood_type').value = baby.blood_type || '';
            document.getElementById('birth_place').value = baby.birth_place || '';
            document.getElementById('pediatrician').value = baby.pediatrician || '';
            document.getElementById('allergies').value = baby.allergies || '';
            document.getElementById('medical_conditions').value = baby.medical_conditions || '';
            document.getElementById('notes').value = baby.notes || '';
            document.getElementById('unique_id').value = baby.unique_id || '';
            document.getElementById('birth_certificate').value = baby.birth_certificate || '';
            document.getElementById('hospital_id').value = baby.hospital_id || '';
            document.getElementById('insurance_id').value = baby.insurance_id || '';

            // Update photo
            if (baby.photo_url) {
                document.getElementById('baby-photo').src = baby.photo_url;
            }

            // Update quick stats
            updateQuickStats(baby);
        }

        function updateQuickStats(baby) {
            // Calculate age
            if (baby.date_of_birth) {
                const age = calculateAge(baby.date_of_birth);
                document.getElementById('baby-age').textContent = age;
            }

            // Update other stats (these would come from growth tracking)
            document.getElementById('current-weight').textContent = baby.current_weight || 'Not recorded';
            document.getElementById('current-length').textContent = baby.current_length || 'Not recorded';
            document.getElementById('next-checkup').textContent = baby.next_checkup || 'Not scheduled';
            document.getElementById('next-vaccination').textContent = baby.next_vaccination || 'Up to date';
            document.getElementById('pediatrician-contact').textContent = baby.pediatrician || 'Not set';
        }

        function calculateAge(birthDate) {
            const birth = new Date(birthDate);
            const now = new Date();
            const diffTime = Math.abs(now - birth);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 30) {
                return `${diffDays} days`;
            } else if (diffDays < 365) {
                const months = Math.floor(diffDays / 30);
                const remainingDays = diffDays % 30;
                return `${months} months, ${remainingDays} days`;
            } else {
                const years = Math.floor(diffDays / 365);
                const remainingDays = diffDays % 365;
                const months = Math.floor(remainingDays / 30);
                return `${years} years, ${months} months`;
            }
        }

        function editProfile() {
            isEditing = true;
            const inputs = document.querySelectorAll('#baby-profile-form input, #baby-profile-form select, #baby-profile-form textarea');
            inputs.forEach(input => {
                if (input.id !== 'unique_id' && input.id !== 'baby_id') {
                    input.removeAttribute('readonly');
                    input.removeAttribute('disabled');
                }
            });
            
            document.getElementById('form-actions').style.display = 'block';
            document.getElementById('photo-upload').style.display = 'block';
            document.getElementById('edit-btn').style.display = 'none';
        }

        function cancelEdit() {
            isEditing = false;
            const inputs = document.querySelectorAll('#baby-profile-form input, #baby-profile-form select, #baby-profile-form textarea');
            inputs.forEach(input => {
                if (input.id !== 'unique_id' && input.id !== 'baby_id') {
                    input.setAttribute('readonly', 'readonly');
                    input.setAttribute('disabled', 'disabled');
                }
            });
            
            document.getElementById('form-actions').style.display = 'none';
            document.getElementById('photo-upload').style.display = 'none';
            document.getElementById('edit-btn').style.display = 'inline-block';
            
            // Restore original values
            if (currentBaby) {
                displayBabyProfile(currentBaby);
            }
        }

        function addBaby() {
            document.getElementById('add-baby-modal').style.display = 'block';
        }

        function closeAddBabyModal() {
            document.getElementById('add-baby-modal').style.display = 'none';
            document.getElementById('add-baby-form').reset();
        }

        async function uploadBabyPhoto() {
            const fileInput = document.getElementById('photo-input');
            const file = fileInput.files[0];
            
            if (file) {
                const formData = new FormData();
                formData.append('photo', file);
                formData.append('baby_id', currentBaby.id);
                
                try {
                    const response = await apiCall('/mother/babies/photo', 'POST', formData);
                    if (response.success) {
                        document.getElementById('baby-photo').src = response.data.photo_url;
                        showNotification('Photo updated successfully!', 'success');
                    }
                } catch (error) {
                    showNotification('Error uploading photo: ' + error.message, 'error');
                }
            }
        }

        // Form submission handlers
        document.getElementById('baby-profile-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                const babyId = data.baby_id;
                
                const response = await apiCall(`/mother/babies/${babyId}`, 'PUT', data);
                if (response.success) {
                    showNotification('Baby profile updated successfully!', 'success');
                    currentBaby = response.data;
                    cancelEdit();
                    loadBabies(); // Refresh the dropdown
                }
            } catch (error) {
                showNotification('Error updating profile: ' + error.message, 'error');
            }
        });

        document.getElementById('add-baby-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/babies', 'POST', data);
                if (response.success) {
                    showNotification('Baby added successfully!', 'success');
                    closeAddBabyModal();
                    loadBabies();
                    
                    // Auto-select the new baby
                    document.getElementById('selected-baby').value = response.data.id;
                    loadBabyProfile();
                }
            } catch (error) {
                showNotification('Error adding baby: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
