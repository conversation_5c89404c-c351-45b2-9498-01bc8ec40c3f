<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="users.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Users</a>
                    <div class="navbar-brand">📊 MCHS - Analytics Dashboard</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Analytics Dashboard</h1>
                <div class="page-actions">
                    <select id="date-range" class="form-control" onchange="updateDateRange()">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 3 months</option>
                        <option value="365">Last year</option>
                    </select>
                    <button onclick="exportReport()" class="btn btn-primary">Export Report</button>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-icon">👥</div>
                        <div class="metric-content">
                            <h3 id="total-users">0</h3>
                            <p>Total Users</p>
                            <span class="metric-change positive" id="users-change">+0%</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-icon">🤰</div>
                        <div class="metric-content">
                            <h3 id="active-pregnancies">0</h3>
                            <p>Active Pregnancies</p>
                            <span class="metric-change positive" id="pregnancies-change">+0%</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-icon">👶</div>
                        <div class="metric-content">
                            <h3 id="total-babies">0</h3>
                            <p>Registered Babies</p>
                            <span class="metric-change positive" id="babies-change">+0%</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-icon">👩‍⚕️</div>
                        <div class="metric-content">
                            <h3 id="active-doctors">0</h3>
                            <p>Active Doctors</p>
                            <span class="metric-change positive" id="doctors-change">+0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row 1 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>User Registration Trends</h3>
                            <div class="chart-controls">
                                <select id="registration-chart-type" class="form-control form-control-sm" onchange="updateRegistrationChart()">
                                    <option value="line">Line Chart</option>
                                    <option value="bar">Bar Chart</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="registration-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3>User Distribution</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="user-distribution-chart" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row 2 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>Appointment Statistics</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="appointments-chart" width="400" height="250"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>Health Metrics Overview</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="health-metrics-chart" width="400" height="250"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Usage -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>System Usage Analytics</h3>
                            <div class="usage-tabs">
                                <button class="tab-button active" onclick="showUsageTab('daily')">Daily Active Users</button>
                                <button class="tab-button" onclick="showUsageTab('features')">Feature Usage</button>
                                <button class="tab-button" onclick="showUsageTab('engagement')">User Engagement</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="daily-usage-tab" class="usage-tab-content active">
                                <canvas id="daily-usage-chart" width="800" height="300"></canvas>
                            </div>
                            <div id="features-usage-tab" class="usage-tab-content">
                                <canvas id="features-usage-chart" width="800" height="300"></canvas>
                            </div>
                            <div id="engagement-usage-tab" class="usage-tab-content">
                                <canvas id="engagement-chart" width="800" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Tables -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>Top Performing Doctors</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Doctor</th>
                                            <th>Patients</th>
                                            <th>Appointments</th>
                                            <th>Rating</th>
                                        </tr>
                                    </thead>
                                    <tbody id="top-doctors-table">
                                        <!-- Top doctors will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3>System Alerts</h3>
                        </div>
                        <div class="card-body">
                            <div id="system-alerts">
                                <!-- System alerts will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Geographic Distribution -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>Geographic Distribution</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div id="geographic-map">
                                        <!-- Map would be integrated here -->
                                        <div class="map-placeholder">
                                            <p>Geographic distribution map would be displayed here</p>
                                            <p>Integration with mapping service required</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="geographic-stats">
                                        <h5>Top Regions</h5>
                                        <div id="top-regions">
                                            <!-- Top regions will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let analyticsData = {};
        let charts = {};

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalyticsData();
        });

        async function loadAnalyticsData() {
            try {
                const dateRange = document.getElementById('date-range').value;
                const response = await apiCall(`/admin/analytics?days=${dateRange}`, 'GET');
                
                if (response.success) {
                    analyticsData = response.data;
                    updateMetrics();
                    createCharts();
                    updateTables();
                    updateAlerts();
                }
            } catch (error) {
                console.error('Error loading analytics data:', error);
                showNotification('Error loading analytics data: ' + error.message, 'error');
            }
        }

        function updateMetrics() {
            // Update key metrics
            document.getElementById('total-users').textContent = analyticsData.totalUsers || 0;
            document.getElementById('active-pregnancies').textContent = analyticsData.activePregnancies || 0;
            document.getElementById('total-babies').textContent = analyticsData.totalBabies || 0;
            document.getElementById('active-doctors').textContent = analyticsData.activeDoctors || 0;

            // Update change percentages
            document.getElementById('users-change').textContent = `+${analyticsData.usersChange || 0}%`;
            document.getElementById('pregnancies-change').textContent = `+${analyticsData.pregnanciesChange || 0}%`;
            document.getElementById('babies-change').textContent = `+${analyticsData.babiesChange || 0}%`;
            document.getElementById('doctors-change').textContent = `+${analyticsData.doctorsChange || 0}%`;
        }

        function createCharts() {
            createRegistrationChart();
            createUserDistributionChart();
            createAppointmentsChart();
            createHealthMetricsChart();
            createDailyUsageChart();
            createFeaturesUsageChart();
            createEngagementChart();
        }

        function createRegistrationChart() {
            const ctx = document.getElementById('registration-chart').getContext('2d');
            
            if (charts.registration) {
                charts.registration.destroy();
            }

            const chartType = document.getElementById('registration-chart-type').value;
            
            charts.registration = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: analyticsData.registrationTrends?.labels || [],
                    datasets: [{
                        label: 'New Registrations',
                        data: analyticsData.registrationTrends?.data || [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createUserDistributionChart() {
            const ctx = document.getElementById('user-distribution-chart').getContext('2d');
            
            if (charts.userDistribution) {
                charts.userDistribution.destroy();
            }

            charts.userDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Mothers', 'Doctors', 'Admins'],
                    datasets: [{
                        data: analyticsData.userDistribution || [0, 0, 0],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createAppointmentsChart() {
            const ctx = document.getElementById('appointments-chart').getContext('2d');
            
            if (charts.appointments) {
                charts.appointments.destroy();
            }

            charts.appointments = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Scheduled', 'Completed', 'Cancelled', 'No-show'],
                    datasets: [{
                        label: 'Appointments',
                        data: analyticsData.appointmentStats || [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 159, 64, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createHealthMetricsChart() {
            const ctx = document.getElementById('health-metrics-chart').getContext('2d');
            
            if (charts.healthMetrics) {
                charts.healthMetrics.destroy();
            }

            charts.healthMetrics = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Vaccinations', 'Checkups', 'Growth Tracking', 'Nutrition', 'Medications'],
                    datasets: [{
                        label: 'Completion Rate (%)',
                        data: analyticsData.healthMetrics || [0, 0, 0, 0, 0],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        pointBackgroundColor: 'rgb(255, 99, 132)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(255, 99, 132)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function createDailyUsageChart() {
            const ctx = document.getElementById('daily-usage-chart').getContext('2d');
            
            if (charts.dailyUsage) {
                charts.dailyUsage.destroy();
            }

            charts.dailyUsage = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: analyticsData.dailyUsage?.labels || [],
                    datasets: [{
                        label: 'Daily Active Users',
                        data: analyticsData.dailyUsage?.data || [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createFeaturesUsageChart() {
            const ctx = document.getElementById('features-usage-chart').getContext('2d');
            
            if (charts.featuresUsage) {
                charts.featuresUsage.destroy();
            }

            charts.featuresUsage = new Chart(ctx, {
                type: 'horizontalBar',
                data: {
                    labels: analyticsData.featureUsage?.labels || [],
                    datasets: [{
                        label: 'Usage Count',
                        data: analyticsData.featureUsage?.data || [],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createEngagementChart() {
            const ctx = document.getElementById('engagement-chart').getContext('2d');
            
            if (charts.engagement) {
                charts.engagement.destroy();
            }

            charts.engagement = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: analyticsData.engagement?.labels || [],
                    datasets: [
                        {
                            label: 'Session Duration (minutes)',
                            data: analyticsData.engagement?.sessionDuration || [],
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            yAxisID: 'y'
                        },
                        {
                            label: 'Page Views',
                            data: analyticsData.engagement?.pageViews || [],
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function updateTables() {
            // Update top doctors table
            const topDoctorsTable = document.getElementById('top-doctors-table');
            const topDoctors = analyticsData.topDoctors || [];
            
            if (topDoctors.length === 0) {
                topDoctorsTable.innerHTML = '<tr><td colspan="4" class="text-center text-secondary">No data available</td></tr>';
            } else {
                topDoctorsTable.innerHTML = topDoctors.map(doctor => `
                    <tr>
                        <td>${doctor.name}</td>
                        <td>${doctor.patients}</td>
                        <td>${doctor.appointments}</td>
                        <td>
                            <div class="rating">
                                ${'★'.repeat(Math.floor(doctor.rating))}${'☆'.repeat(5 - Math.floor(doctor.rating))}
                                <span class="rating-value">${doctor.rating}</span>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }

            // Update top regions
            const topRegions = document.getElementById('top-regions');
            const regions = analyticsData.topRegions || [];
            
            topRegions.innerHTML = regions.map(region => `
                <div class="region-item">
                    <div class="region-name">${region.name}</div>
                    <div class="region-stats">
                        <span class="region-users">${region.users} users</span>
                        <div class="region-bar">
                            <div class="region-fill" style="width: ${region.percentage}%"></div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateAlerts() {
            const alertsContainer = document.getElementById('system-alerts');
            const alerts = analyticsData.systemAlerts || [];
            
            if (alerts.length === 0) {
                alertsContainer.innerHTML = '<div class="alert alert-success">All systems operating normally</div>';
            } else {
                alertsContainer.innerHTML = alerts.map(alert => `
                    <div class="alert alert-${alert.type}">
                        <strong>${alert.title}</strong>
                        <p>${alert.message}</p>
                        <small class="text-muted">${formatDate(alert.timestamp)}</small>
                    </div>
                `).join('');
            }
        }

        function updateDateRange() {
            loadAnalyticsData();
        }

        function updateRegistrationChart() {
            createRegistrationChart();
        }

        function showUsageTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.usage-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab and activate button
            document.getElementById(`${tabName}-usage-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        async function exportReport() {
            try {
                const dateRange = document.getElementById('date-range').value;
                const response = await apiCall(`/admin/analytics/export?days=${dateRange}`, 'GET');
                
                if (response.success) {
                    // Create and download file
                    const blob = new Blob([response.data], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showNotification('Report exported successfully!', 'success');
                }
            } catch (error) {
                showNotification('Error exporting report: ' + error.message, 'error');
            }
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }
    </script>
</body>
</html>
