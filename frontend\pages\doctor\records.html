<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Records - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="patients.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Patients</a>
                    <div class="navbar-brand">📋 MCHS - Patient Records</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Patient Health Records</h1>
                <p class="page-description">Review and manage comprehensive patient health information</p>
                <div class="header-actions">
                    <button onclick="exportRecords()" class="btn btn-outline">
                        📊 Export Records
                    </button>
                    <button onclick="generateReport()" class="btn btn-outline">
                        📄 Generate Report
                    </button>
                    <button onclick="openBulkActions()" class="btn btn-outline">
                        ⚙️ Bulk Actions
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <!-- Patient Search & List -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3>👥 Patient Search</h3>
                        </div>
                        <div class="card-body">
                            <div class="search-section">
                                <input type="text" id="patient-search" class="form-control mb-3" 
                                       placeholder="Search patients..." onkeyup="searchPatients()">
                                
                                <div class="filter-options mb-3">
                                    <select id="patient-type-filter" class="form-control mb-2" onchange="filterPatients()">
                                        <option value="all">All Patients</option>
                                        <option value="mother">Mothers</option>
                                        <option value="baby">Babies</option>
                                    </select>
                                    
                                    <select id="status-filter" class="form-control" onchange="filterPatients()">
                                        <option value="all">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="critical">Critical</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div id="patients-list" class="patients-list">
                                <!-- Patient list will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card">
                        <div class="card-header">
                            <h3>📊 Quick Stats</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">Total Patients:</span>
                                <span class="stat-value" id="total-patients">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Active Cases:</span>
                                <span class="stat-value" id="active-cases">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Critical Alerts:</span>
                                <span class="stat-value critical" id="critical-alerts">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Pending Reviews:</span>
                                <span class="stat-value" id="pending-reviews">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <!-- Patient Record Details -->
                    <div id="patient-record-container" class="patient-record-container">
                        <div class="empty-selection">
                            <div class="empty-icon">👤</div>
                            <h3>Select a Patient</h3>
                            <p>Choose a patient from the list to view their health records</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Patient Record Template -->
    <div id="patient-record-template" style="display: none;">
        <div class="card patient-record-card">
            <div class="card-header">
                <div class="patient-header">
                    <div class="patient-info">
                        <h2 id="patient-name">Patient Name</h2>
                        <div class="patient-details">
                            <span id="patient-age">Age</span> • 
                            <span id="patient-type">Type</span> • 
                            <span id="patient-id">ID: #12345</span>
                        </div>
                    </div>
                    <div class="patient-actions">
                        <button onclick="editPatientInfo()" class="btn btn-outline">✏️ Edit</button>
                        <button onclick="scheduleAppointment()" class="btn btn-primary">📅 Schedule</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Record Tabs -->
                <div class="record-tabs">
                    <button class="tab-btn active" onclick="showTab('overview')">📋 Overview</button>
                    <button class="tab-btn" onclick="showTab('vitals')">💓 Vitals</button>
                    <button class="tab-btn" onclick="showTab('history')">📚 History</button>
                    <button class="tab-btn" onclick="showTab('medications')">💊 Medications</button>
                    <button class="tab-btn" onclick="showTab('reports')">📄 Reports</button>
                    <button class="tab-btn" onclick="showTab('notes')">📝 Notes</button>
                </div>

                <!-- Overview Tab -->
                <div id="overview-tab" class="tab-content active">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="overview-section">
                                <h4>🏥 Current Status</h4>
                                <div class="status-indicators">
                                    <div class="status-item">
                                        <span class="status-label">Health Status:</span>
                                        <span class="status-badge good" id="health-status">Good</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Last Visit:</span>
                                        <span id="last-visit">2024-03-15</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Next Appointment:</span>
                                        <span id="next-appointment">2024-04-15</span>
                                    </div>
                                </div>
                            </div>

                            <div class="overview-section">
                                <h4>⚠️ Active Alerts</h4>
                                <div id="active-alerts" class="alerts-list">
                                    <!-- Active alerts will be loaded here -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="overview-section">
                                <h4>📊 Recent Vitals</h4>
                                <div class="vitals-summary">
                                    <canvas id="vitals-chart" width="300" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overview-section">
                        <h4>📋 Recent Activity</h4>
                        <div id="recent-activity" class="activity-timeline">
                            <!-- Recent activity will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Vitals Tab -->
                <div id="vitals-tab" class="tab-content">
                    <div class="vitals-section">
                        <div class="vitals-header">
                            <h4>💓 Vital Signs Tracking</h4>
                            <button onclick="addVitals()" class="btn btn-primary">+ Add Vitals</button>
                        </div>
                        
                        <div class="vitals-charts">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>Blood Pressure</h5>
                                        <canvas id="bp-chart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>Weight Tracking</h5>
                                        <canvas id="weight-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="vitals-table">
                            <h5>Recent Measurements</h5>
                            <div id="vitals-history" class="table-responsive">
                                <!-- Vitals history table will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Tab -->
                <div id="history-tab" class="tab-content">
                    <div class="history-section">
                        <div class="history-header">
                            <h4>📚 Medical History</h4>
                            <div class="history-filters">
                                <select id="history-filter" class="form-control" onchange="filterHistory()">
                                    <option value="all">All Records</option>
                                    <option value="visits">Visits</option>
                                    <option value="procedures">Procedures</option>
                                    <option value="tests">Tests</option>
                                    <option value="vaccinations">Vaccinations</option>
                                </select>
                            </div>
                        </div>
                        
                        <div id="medical-history" class="medical-history">
                            <!-- Medical history will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Medications Tab -->
                <div id="medications-tab" class="tab-content">
                    <div class="medications-section">
                        <div class="medications-header">
                            <h4>💊 Current Medications</h4>
                            <button onclick="addMedication()" class="btn btn-primary">+ Add Medication</button>
                        </div>
                        
                        <div id="current-medications" class="medications-list">
                            <!-- Current medications will be loaded here -->
                        </div>
                        
                        <div class="medications-history">
                            <h5>Medication History</h5>
                            <div id="medication-history" class="medication-history-list">
                                <!-- Medication history will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Tab -->
                <div id="reports-tab" class="tab-content">
                    <div class="reports-section">
                        <div class="reports-header">
                            <h4>📄 Medical Reports</h4>
                            <button onclick="uploadReport()" class="btn btn-primary">📤 Upload Report</button>
                        </div>
                        
                        <div class="reports-filters">
                            <select id="report-type-filter" class="form-control" onchange="filterReports()">
                                <option value="all">All Reports</option>
                                <option value="lab">Lab Results</option>
                                <option value="imaging">Imaging</option>
                                <option value="consultation">Consultation</option>
                                <option value="discharge">Discharge Summary</option>
                            </select>
                        </div>
                        
                        <div id="medical-reports" class="reports-grid">
                            <!-- Medical reports will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Notes Tab -->
                <div id="notes-tab" class="tab-content">
                    <div class="notes-section">
                        <div class="notes-header">
                            <h4>📝 Clinical Notes</h4>
                            <button onclick="addNote()" class="btn btn-primary">+ Add Note</button>
                        </div>
                        
                        <div class="notes-editor">
                            <textarea id="new-note" class="form-control" rows="4" 
                                      placeholder="Add clinical notes..."></textarea>
                            <div class="note-actions">
                                <select id="note-type" class="form-control">
                                    <option value="general">General Note</option>
                                    <option value="diagnosis">Diagnosis</option>
                                    <option value="treatment">Treatment Plan</option>
                                    <option value="follow-up">Follow-up</option>
                                </select>
                                <button onclick="saveNote()" class="btn btn-primary">Save Note</button>
                            </div>
                        </div>
                        
                        <div id="clinical-notes" class="notes-list">
                            <!-- Clinical notes will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Vitals Modal -->
    <div id="vitals-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>💓 Add Vital Signs</h3>
                <span class="close" onclick="closeVitalsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="vitals-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="systolic-bp">Systolic BP (mmHg)</label>
                                <input type="number" id="systolic-bp" class="form-control" min="60" max="250">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="diastolic-bp">Diastolic BP (mmHg)</label>
                                <input type="number" id="diastolic-bp" class="form-control" min="40" max="150">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="heart-rate">Heart Rate (bpm)</label>
                                <input type="number" id="heart-rate" class="form-control" min="40" max="200">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="temperature">Temperature (°F)</label>
                                <input type="number" id="temperature" class="form-control" min="95" max="110" step="0.1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="weight">Weight (lbs)</label>
                                <input type="number" id="weight" class="form-control" min="50" max="500" step="0.1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="oxygen-saturation">Oxygen Saturation (%)</label>
                                <input type="number" id="oxygen-saturation" class="form-control" min="70" max="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="vitals-notes">Notes</label>
                        <textarea id="vitals-notes" class="form-control" rows="3" 
                                  placeholder="Any additional observations..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button onclick="closeVitalsModal()" class="btn btn-outline">Cancel</button>
                <button onclick="saveVitals()" class="btn btn-primary">Save Vitals</button>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let patients = [];
        let selectedPatient = null;
        let patientRecords = {};

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPatients();
            updateStats();
        });

        async function loadPatients() {
            try {
                const response = await apiCall('/doctor/patients', 'GET');
                if (response.success) {
                    patients = response.data;
                } else {
                    loadDemoPatients();
                }
            } catch (error) {
                console.error('Error loading patients:', error);
                loadDemoPatients();
            }
            
            displayPatients();
        }

        function loadDemoPatients() {
            patients = [
                {
                    id: '1',
                    name: 'Sarah Johnson',
                    age: '28',
                    type: 'mother',
                    status: 'active',
                    phone: '(*************',
                    lastVisit: '2024-03-15',
                    nextAppointment: '2024-04-15',
                    healthStatus: 'good',
                    alerts: 1
                },
                {
                    id: '2',
                    name: 'Emily Davis',
                    age: '32',
                    type: 'mother',
                    status: 'active',
                    phone: '(*************',
                    lastVisit: '2024-03-10',
                    nextAppointment: '2024-04-10',
                    healthStatus: 'good',
                    alerts: 0
                },
                {
                    id: '3',
                    name: 'Baby Johnson',
                    age: '3 months',
                    type: 'baby',
                    status: 'active',
                    phone: '(*************',
                    lastVisit: '2024-03-20',
                    nextAppointment: '2024-04-20',
                    healthStatus: 'excellent',
                    alerts: 0
                },
                {
                    id: '4',
                    name: 'Maria Rodriguez',
                    age: '25',
                    type: 'mother',
                    status: 'critical',
                    phone: '(*************',
                    lastVisit: '2024-03-18',
                    nextAppointment: '2024-03-25',
                    healthStatus: 'critical',
                    alerts: 3
                }
            ];

            // Load demo records for each patient
            patients.forEach(patient => {
                patientRecords[patient.id] = generateDemoRecord(patient);
            });
        }

        function generateDemoRecord(patient) {
            return {
                vitals: [
                    { date: '2024-03-15', systolic: 120, diastolic: 80, heartRate: 72, temperature: 98.6, weight: 150, oxygenSat: 98 },
                    { date: '2024-03-10', systolic: 118, diastolic: 78, heartRate: 70, temperature: 98.4, weight: 149, oxygenSat: 99 },
                    { date: '2024-03-05', systolic: 122, diastolic: 82, heartRate: 74, temperature: 98.8, weight: 148, oxygenSat: 97 }
                ],
                history: [
                    { date: '2024-03-15', type: 'visit', description: 'Routine prenatal checkup', provider: 'Dr. Smith' },
                    { date: '2024-02-15', type: 'test', description: 'Blood work - normal results', provider: 'Lab Tech' },
                    { date: '2024-01-15', type: 'vaccination', description: 'Flu shot administered', provider: 'Nurse Johnson' }
                ],
                medications: [
                    { name: 'Prenatal Vitamins', dosage: '1 tablet daily', status: 'active', startDate: '2024-01-01' },
                    { name: 'Iron Supplement', dosage: '65mg daily', status: 'active', startDate: '2024-02-01' }
                ],
                reports: [
                    { date: '2024-03-15', type: 'lab', title: 'Complete Blood Count', status: 'normal' },
                    { date: '2024-03-10', type: 'imaging', title: 'Ultrasound - 20 weeks', status: 'normal' }
                ],
                notes: [
                    { date: '2024-03-15', type: 'general', content: 'Patient reports feeling well. No concerns at this time.', author: 'Dr. Smith' },
                    { date: '2024-03-10', type: 'follow-up', content: 'Continue current prenatal care plan. Next visit in 4 weeks.', author: 'Dr. Smith' }
                ],
                alerts: patient.alerts > 0 ? [
                    { type: 'warning', message: 'Blood pressure slightly elevated', date: '2024-03-15' }
                ] : []
            };
        }

        function displayPatients() {
            const container = document.getElementById('patients-list');
            
            if (patients.length === 0) {
                container.innerHTML = '<p class="text-muted">No patients found</p>';
                return;
            }

            container.innerHTML = patients.map(patient => `
                <div class="patient-item ${patient.status}" onclick="selectPatient('${patient.id}')">
                    <div class="patient-info">
                        <div class="patient-name">${patient.name}</div>
                        <div class="patient-details">${patient.age} • ${patient.type}</div>
                        <div class="patient-status">
                            <span class="status-badge ${patient.healthStatus}">${patient.healthStatus}</span>
                            ${patient.alerts > 0 ? `<span class="alert-badge">${patient.alerts} alert${patient.alerts > 1 ? 's' : ''}</span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function selectPatient(patientId) {
            selectedPatient = patients.find(p => p.id === patientId);
            if (selectedPatient) {
                displayPatientRecord(selectedPatient);
                
                // Update active selection
                document.querySelectorAll('.patient-item').forEach(item => {
                    item.classList.remove('selected');
                });
                event.currentTarget.classList.add('selected');
            }
        }

        function displayPatientRecord(patient) {
            const container = document.getElementById('patient-record-container');
            const template = document.getElementById('patient-record-template');
            
            // Clone template and show it
            container.innerHTML = template.innerHTML;
            
            // Populate patient information
            document.getElementById('patient-name').textContent = patient.name;
            document.getElementById('patient-age').textContent = patient.age;
            document.getElementById('patient-type').textContent = patient.type;
            document.getElementById('patient-id').textContent = `ID: #${patient.id}`;
            document.getElementById('health-status').textContent = patient.healthStatus;
            document.getElementById('health-status').className = `status-badge ${patient.healthStatus}`;
            document.getElementById('last-visit').textContent = patient.lastVisit;
            document.getElementById('next-appointment').textContent = patient.nextAppointment;
            
            // Load patient record data
            const record = patientRecords[patient.id];
            if (record) {
                displayActiveAlerts(record.alerts);
                displayRecentActivity(record.history);
                displayVitalsChart(record.vitals);
                displayVitalsHistory(record.vitals);
                displayMedicalHistory(record.history);
                displayCurrentMedications(record.medications);
                displayMedicalReports(record.reports);
                displayClinicalNotes(record.notes);
            }
        }

        function displayActiveAlerts(alerts) {
            const container = document.getElementById('active-alerts');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p class="text-muted">No active alerts</p>';
                return;
            }

            container.innerHTML = alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="alert-icon">⚠️</div>
                    <div class="alert-content">
                        <div class="alert-message">${alert.message}</div>
                        <div class="alert-date">${alert.date}</div>
                    </div>
                </div>
            `).join('');
        }

        function displayRecentActivity(history) {
            const container = document.getElementById('recent-activity');
            const recentItems = history.slice(0, 5);
            
            container.innerHTML = recentItems.map(item => `
                <div class="activity-item">
                    <div class="activity-date">${item.date}</div>
                    <div class="activity-content">
                        <div class="activity-type">${item.type.toUpperCase()}</div>
                        <div class="activity-description">${item.description}</div>
                        <div class="activity-provider">by ${item.provider}</div>
                    </div>
                </div>
            `).join('');
        }

        function displayVitalsChart(vitals) {
            const ctx = document.getElementById('vitals-chart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: vitals.map(v => v.date),
                    datasets: [{
                        label: 'Systolic BP',
                        data: vitals.map(v => v.systolic),
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }, {
                        label: 'Heart Rate',
                        data: vitals.map(v => v.heartRate),
                        borderColor: 'rgb(255, 99, 132)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        function displayVitalsHistory(vitals) {
            const container = document.getElementById('vitals-history');
            
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>BP</th>
                            <th>HR</th>
                            <th>Temp</th>
                            <th>Weight</th>
                            <th>O2 Sat</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${vitals.map(vital => `
                            <tr>
                                <td>${vital.date}</td>
                                <td>${vital.systolic}/${vital.diastolic}</td>
                                <td>${vital.heartRate}</td>
                                <td>${vital.temperature}°F</td>
                                <td>${vital.weight} lbs</td>
                                <td>${vital.oxygenSat}%</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        function displayMedicalHistory(history) {
            const container = document.getElementById('medical-history');
            
            container.innerHTML = history.map(item => `
                <div class="history-item">
                    <div class="history-date">${item.date}</div>
                    <div class="history-content">
                        <div class="history-type">${item.type.toUpperCase()}</div>
                        <div class="history-description">${item.description}</div>
                        <div class="history-provider">Provider: ${item.provider}</div>
                    </div>
                </div>
            `).join('');
        }

        function displayCurrentMedications(medications) {
            const container = document.getElementById('current-medications');
            const activeMeds = medications.filter(med => med.status === 'active');
            
            container.innerHTML = activeMeds.map(med => `
                <div class="medication-item">
                    <div class="medication-info">
                        <div class="medication-name">${med.name}</div>
                        <div class="medication-dosage">${med.dosage}</div>
                        <div class="medication-start">Started: ${med.startDate}</div>
                    </div>
                    <div class="medication-actions">
                        <button onclick="editMedication('${med.name}')" class="btn btn-sm btn-outline">Edit</button>
                        <button onclick="discontinueMedication('${med.name}')" class="btn btn-sm btn-danger">Stop</button>
                    </div>
                </div>
            `).join('');
        }

        function displayMedicalReports(reports) {
            const container = document.getElementById('medical-reports');
            
            container.innerHTML = reports.map(report => `
                <div class="report-item">
                    <div class="report-icon">📄</div>
                    <div class="report-info">
                        <div class="report-title">${report.title}</div>
                        <div class="report-details">${report.type.toUpperCase()} • ${report.date}</div>
                        <div class="report-status ${report.status}">${report.status.toUpperCase()}</div>
                    </div>
                    <div class="report-actions">
                        <button onclick="viewReport('${report.title}')" class="btn btn-sm btn-outline">View</button>
                    </div>
                </div>
            `).join('');
        }

        function displayClinicalNotes(notes) {
            const container = document.getElementById('clinical-notes');
            
            container.innerHTML = notes.map(note => `
                <div class="note-item">
                    <div class="note-header">
                        <div class="note-type">${note.type.toUpperCase()}</div>
                        <div class="note-date">${note.date}</div>
                    </div>
                    <div class="note-content">${note.content}</div>
                    <div class="note-author">by ${note.author}</div>
                </div>
            `).join('');
        }

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(`${tabName}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        function searchPatients() {
            const query = document.getElementById('patient-search').value.toLowerCase();
            const filteredPatients = patients.filter(patient => 
                patient.name.toLowerCase().includes(query) ||
                patient.id.includes(query)
            );
            
            displayFilteredPatients(filteredPatients);
        }

        function filterPatients() {
            const typeFilter = document.getElementById('patient-type-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            
            let filteredPatients = patients;
            
            if (typeFilter !== 'all') {
                filteredPatients = filteredPatients.filter(p => p.type === typeFilter);
            }
            
            if (statusFilter !== 'all') {
                filteredPatients = filteredPatients.filter(p => p.status === statusFilter);
            }
            
            displayFilteredPatients(filteredPatients);
        }

        function displayFilteredPatients(filteredPatients) {
            const container = document.getElementById('patients-list');
            
            container.innerHTML = filteredPatients.map(patient => `
                <div class="patient-item ${patient.status}" onclick="selectPatient('${patient.id}')">
                    <div class="patient-info">
                        <div class="patient-name">${patient.name}</div>
                        <div class="patient-details">${patient.age} • ${patient.type}</div>
                        <div class="patient-status">
                            <span class="status-badge ${patient.healthStatus}">${patient.healthStatus}</span>
                            ${patient.alerts > 0 ? `<span class="alert-badge">${patient.alerts} alert${patient.alerts > 1 ? 's' : ''}</span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateStats() {
            document.getElementById('total-patients').textContent = patients.length;
            document.getElementById('active-cases').textContent = patients.filter(p => p.status === 'active').length;
            document.getElementById('critical-alerts').textContent = patients.filter(p => p.status === 'critical').length;
            document.getElementById('pending-reviews').textContent = '3'; // This would be calculated from actual data
        }

        function addVitals() {
            if (!selectedPatient) {
                showNotification('Please select a patient first', 'error');
                return;
            }
            document.getElementById('vitals-modal').style.display = 'block';
        }

        function closeVitalsModal() {
            document.getElementById('vitals-modal').style.display = 'none';
            document.getElementById('vitals-form').reset();
        }

        function saveVitals() {
            const vitalsData = {
                date: new Date().toISOString().split('T')[0],
                systolic: document.getElementById('systolic-bp').value,
                diastolic: document.getElementById('diastolic-bp').value,
                heartRate: document.getElementById('heart-rate').value,
                temperature: document.getElementById('temperature').value,
                weight: document.getElementById('weight').value,
                oxygenSat: document.getElementById('oxygen-saturation').value,
                notes: document.getElementById('vitals-notes').value
            };

            // Add to patient record
            if (selectedPatient && patientRecords[selectedPatient.id]) {
                patientRecords[selectedPatient.id].vitals.unshift(vitalsData);
                displayVitalsHistory(patientRecords[selectedPatient.id].vitals);
                displayVitalsChart(patientRecords[selectedPatient.id].vitals);
            }

            closeVitalsModal();
            showNotification('Vital signs recorded successfully', 'success');
        }

        function addMedication() {
            showNotification('Add medication functionality would be implemented here', 'info');
        }

        function addNote() {
            const noteContent = document.getElementById('new-note').value.trim();
            const noteType = document.getElementById('note-type').value;
            
            if (!noteContent) {
                showNotification('Please enter a note', 'error');
                return;
            }

            const newNote = {
                date: new Date().toISOString().split('T')[0],
                type: noteType,
                content: noteContent,
                author: 'Dr. Current User'
            };

            if (selectedPatient && patientRecords[selectedPatient.id]) {
                patientRecords[selectedPatient.id].notes.unshift(newNote);
                displayClinicalNotes(patientRecords[selectedPatient.id].notes);
            }

            document.getElementById('new-note').value = '';
            showNotification('Note added successfully', 'success');
        }

        function saveNote() {
            addNote();
        }

        function editPatientInfo() {
            showNotification('Edit patient info functionality would be implemented here', 'info');
        }

        function scheduleAppointment() {
            showNotification('Schedule appointment functionality would be implemented here', 'info');
        }

        function exportRecords() {
            showNotification('Export records functionality would be implemented here', 'info');
        }

        function generateReport() {
            showNotification('Generate report functionality would be implemented here', 'info');
        }

        function openBulkActions() {
            showNotification('Bulk actions functionality would be implemented here', 'info');
        }

        function filterHistory() {
            showNotification('Filter history functionality would be implemented here', 'info');
        }

        function filterReports() {
            showNotification('Filter reports functionality would be implemented here', 'info');
        }

        function uploadReport() {
            showNotification('Upload report functionality would be implemented here', 'info');
        }

        function editMedication(medicationName) {
            showNotification(`Edit medication: ${medicationName}`, 'info');
        }

        function discontinueMedication(medicationName) {
            if (confirm(`Are you sure you want to discontinue ${medicationName}?`)) {
                showNotification(`${medicationName} discontinued`, 'success');
            }
        }

        function viewReport(reportTitle) {
            showNotification(`Viewing report: ${reportTitle}`, 'info');
        }
    </script>
</body>
</html>
