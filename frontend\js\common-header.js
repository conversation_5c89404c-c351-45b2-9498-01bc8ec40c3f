/**
 * Common Header Component and Authentication Manager
 * This script provides common header functionality and authentication checks for all pages
 */

class CommonHeader {
    static init() {
        this.injectAuthCheck();
        this.updateUserDisplay();
    }

    /**
     * Inject authentication check for protected pages
     */
    static injectAuthCheck() {
        // Skip auth check for public pages
        const publicPages = ['/home.html', '/pages/login.html', '/pages/register.html', '/pages/admin-setup.html', '/index.html'];
        const currentPath = window.location.pathname;
        
        // Check if current page is public
        const isPublicPage = publicPages.some(page => currentPath.endsWith(page));
        
        if (!isPublicPage && !AuthManager.isAuthenticated()) {
            // Redirect to login for protected pages
            window.location.href = '/pages/login.html';
            return;
        }
    }

    /**
     * Update user display in header if user is authenticated
     */
    static updateUserDisplay() {
        if (AuthManager.isAuthenticated()) {
            const user = AuthManager.getUser();
            const userNameElements = document.querySelectorAll('#user-name, .user-name');
            
            userNameElements.forEach(element => {
                if (element && user) {
                    element.textContent = user.full_name || user.email || 'User';
                }
            });

            // Show authenticated user elements
            const authElements = document.querySelectorAll('.auth-required');
            authElements.forEach(element => {
                element.style.display = 'block';
            });

            // Hide login elements
            const loginElements = document.querySelectorAll('.login-required');
            loginElements.forEach(element => {
                element.style.display = 'none';
            });
        } else {
            // Hide authenticated user elements
            const authElements = document.querySelectorAll('.auth-required');
            authElements.forEach(element => {
                element.style.display = 'none';
            });

            // Show login elements
            const loginElements = document.querySelectorAll('.login-required');
            loginElements.forEach(element => {
                element.style.display = 'block';
            });
        }
    }

    /**
     * Global logout function
     */
    static logout() {
        AuthManager.logout();
        window.location.href = '/home.html';
    }

    /**
     * Navigate to appropriate main page based on user role
     */
    static goToMainPage() {
        if (AuthManager.isAuthenticated()) {
            NavigationManager.redirectToMainPage();
        } else {
            window.location.href = '/pages/login.html';
        }
    }

    /**
     * Check authentication and redirect to feature or login
     */
    static checkAuthAndRedirect(feature) {
        if (AuthManager.isAuthenticated()) {
            // User is logged in, redirect to appropriate feature page
            const role = AuthManager.getUserRole();
            switch (feature) {
                case 'pregnancy':
                case 'mother':
                    window.location.href = '/pages/mother/profile.html';
                    break;
                case 'baby':
                    window.location.href = '/pages/baby/profile.html';
                    break;
                case 'doctor':
                    if (role === 'doctor') {
                        window.location.href = '/pages/doctor/patients.html';
                    } else {
                        window.location.href = '/pages/mother/appointments.html';
                    }
                    break;
                case 'appointments':
                    if (role === 'doctor') {
                        window.location.href = '/pages/doctor/appointments.html';
                    } else {
                        window.location.href = '/pages/mother/appointments.html';
                    }
                    break;
                case 'admin':
                    if (role === 'admin') {
                        window.location.href = '/pages/admin/users.html';
                    } else {
                        window.location.href = '/pages/mother/profile.html';
                    }
                    break;
                default:
                    window.location.href = '/pages/mother/profile.html';
            }
        } else {
            // User is not logged in, redirect to login page
            window.location.href = '/pages/login.html';
        }
    }

    /**
     * Add common navigation functionality to existing headers
     */
    static enhanceExistingHeader() {
        // Add logout functionality to existing logout buttons
        const logoutButtons = document.querySelectorAll('[onclick*="logout"], .logout-btn');
        logoutButtons.forEach(button => {
            button.onclick = () => this.logout();
        });

        // Add home navigation to brand elements
        const brandElements = document.querySelectorAll('.navbar-brand');
        brandElements.forEach(brand => {
            if (!brand.href) {
                brand.style.cursor = 'pointer';
                brand.onclick = () => this.goToMainPage();
            }
        });
    }
}

// Global functions for backward compatibility
function logout() {
    CommonHeader.logout();
}

function checkAuthAndRedirect(feature) {
    CommonHeader.checkAuthAndRedirect(feature);
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    CommonHeader.init();
    CommonHeader.enhanceExistingHeader();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CommonHeader;
}
