<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Schedule - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="baby-care.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Baby Care</a>
                    <div class="navbar-brand">👶 MCHS - Vaccination Schedule</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Vaccination Schedule</h1>
                <div class="page-actions">
                    <button onclick="recordVaccination()" class="btn btn-primary">Record Vaccination</button>
                    <button onclick="scheduleVaccination()" class="btn btn-outline">Schedule Vaccination</button>
                </div>
            </div>

            <!-- Baby Selection -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="selected-baby">Select Baby</label>
                                <select id="selected-baby" class="form-control" onchange="loadVaccinationData()">
                                    <option value="">Select a baby...</option>
                                    <!-- Babies will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="vaccination-progress">
                                <span class="progress-label">Vaccination Progress:</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="vaccination-progress"></div>
                                </div>
                                <span class="progress-text" id="progress-text">0% Complete</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vaccination Content -->
            <div id="vaccination-content" style="display: none;">
                <div class="row">
                    <!-- Vaccination Schedule -->
                    <div class="col-md-8">
                        <!-- Upcoming Vaccinations -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Upcoming Vaccinations</h3>
                            </div>
                            <div class="card-body">
                                <div id="upcoming-vaccinations">
                                    <!-- Upcoming vaccinations will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Vaccination Timeline -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Vaccination Timeline</h3>
                                <div class="timeline-filters">
                                    <button class="btn btn-sm btn-outline active" onclick="filterTimeline('all')">All</button>
                                    <button class="btn btn-sm btn-outline" onclick="filterTimeline('completed')">Completed</button>
                                    <button class="btn btn-sm btn-outline" onclick="filterTimeline('pending')">Pending</button>
                                    <button class="btn btn-sm btn-outline" onclick="filterTimeline('overdue')">Overdue</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="vaccination-timeline" id="vaccination-timeline">
                                    <!-- Vaccination timeline will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Vaccination History -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Vaccination History</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Vaccine</th>
                                                <th>Dose</th>
                                                <th>Healthcare Provider</th>
                                                <th>Batch Number</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="vaccination-history">
                                            <!-- Vaccination history will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-md-4">
                        <!-- Next Vaccination -->
                        <div class="card">
                            <div class="card-header">
                                <h3>Next Vaccination</h3>
                            </div>
                            <div class="card-body" id="next-vaccination">
                                <!-- Next vaccination details will be loaded here -->
                            </div>
                        </div>

                        <!-- Vaccination Reminders -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Reminders</h3>
                            </div>
                            <div class="card-body">
                                <div class="reminder-settings">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" id="email-reminder" checked>
                                            Email reminders
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" id="sms-reminder" checked>
                                            SMS reminders
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label for="reminder-days">Remind me</label>
                                        <select id="reminder-days" class="form-control">
                                            <option value="7">7 days before</option>
                                            <option value="3">3 days before</option>
                                            <option value="1">1 day before</option>
                                        </select>
                                    </div>
                                    <button onclick="saveReminderSettings()" class="btn btn-sm btn-primary">Save Settings</button>
                                </div>
                            </div>
                        </div>

                        <!-- Vaccination Information -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Vaccination Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="vaccination-info">
                                    <h5>Why Vaccinate?</h5>
                                    <ul>
                                        <li>Protects against serious diseases</li>
                                        <li>Prevents disease outbreaks</li>
                                        <li>Protects community health</li>
                                        <li>Safe and effective</li>
                                    </ul>
                                    
                                    <h5>Common Side Effects</h5>
                                    <ul>
                                        <li>Mild fever</li>
                                        <li>Soreness at injection site</li>
                                        <li>Fussiness</li>
                                        <li>Mild rash</li>
                                    </ul>
                                    
                                    <div class="alert alert-info">
                                        <strong>Note:</strong> Serious side effects are rare. Contact your pediatrician if you have concerns.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contacts -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Emergency Contacts</h3>
                            </div>
                            <div class="card-body">
                                <div class="emergency-contacts">
                                    <div class="contact-item">
                                        <strong>Pediatrician</strong>
                                        <p id="pediatrician-contact">-</p>
                                    </div>
                                    <div class="contact-item">
                                        <strong>After-hours Clinic</strong>
                                        <p id="clinic-contact">-</p>
                                    </div>
                                    <div class="contact-item">
                                        <strong>Emergency Services</strong>
                                        <p>911</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Record Vaccination Modal -->
    <div id="record-vaccination-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Record Vaccination</h3>
                <span class="close" onclick="closeRecordVaccinationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="record-vaccination-form">
                    <input type="hidden" id="baby_id" name="baby_id">
                    
                    <div class="form-group">
                        <label for="vaccine_name">Vaccine Name</label>
                        <select id="vaccine_name" name="vaccine_name" class="form-control" required>
                            <option value="">Select Vaccine</option>
                            <option value="Hepatitis B">Hepatitis B</option>
                            <option value="DTaP">DTaP (Diphtheria, Tetanus, Pertussis)</option>
                            <option value="Hib">Hib (Haemophilus influenzae type b)</option>
                            <option value="PCV13">PCV13 (Pneumococcal)</option>
                            <option value="IPV">IPV (Polio)</option>
                            <option value="Rotavirus">Rotavirus</option>
                            <option value="MMR">MMR (Measles, Mumps, Rubella)</option>
                            <option value="Varicella">Varicella (Chickenpox)</option>
                            <option value="Hepatitis A">Hepatitis A</option>
                            <option value="Influenza">Influenza (Flu)</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vaccination_date">Date Given</label>
                                <input type="date" id="vaccination_date" name="vaccination_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dose_number">Dose Number</label>
                                <select id="dose_number" name="dose_number" class="form-control" required>
                                    <option value="">Select Dose</option>
                                    <option value="1">1st Dose</option>
                                    <option value="2">2nd Dose</option>
                                    <option value="3">3rd Dose</option>
                                    <option value="4">4th Dose</option>
                                    <option value="5">5th Dose</option>
                                    <option value="booster">Booster</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="healthcare_provider">Healthcare Provider</label>
                                <input type="text" id="healthcare_provider" name="healthcare_provider" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="clinic_location">Clinic/Location</label>
                                <input type="text" id="clinic_location" name="clinic_location" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="batch_number">Batch/Lot Number</label>
                                <input type="text" id="batch_number" name="batch_number" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="manufacturer">Manufacturer</label>
                                <input type="text" id="manufacturer" name="manufacturer" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="side_effects">Side Effects (if any)</label>
                        <textarea id="side_effects" name="side_effects" class="form-control" rows="3" placeholder="Describe any side effects observed"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="notes">Additional Notes</label>
                        <textarea id="notes" name="notes" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Record Vaccination</button>
                        <button type="button" onclick="closeRecordVaccinationModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Schedule Vaccination Modal -->
    <div id="schedule-vaccination-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Schedule Vaccination</h3>
                <span class="close" onclick="closeScheduleVaccinationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="schedule-vaccination-form">
                    <input type="hidden" id="schedule_baby_id" name="baby_id">
                    
                    <div class="form-group">
                        <label for="schedule_vaccine_name">Vaccine Name</label>
                        <select id="schedule_vaccine_name" name="vaccine_name" class="form-control" required>
                            <option value="">Select Vaccine</option>
                            <!-- Same options as record form -->
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="scheduled_date">Scheduled Date</label>
                                <input type="date" id="scheduled_date" name="scheduled_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="scheduled_time">Scheduled Time</label>
                                <input type="time" id="scheduled_time" name="scheduled_time" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="schedule_healthcare_provider">Healthcare Provider</label>
                        <input type="text" id="schedule_healthcare_provider" name="healthcare_provider" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="schedule_clinic_location">Clinic/Location</label>
                        <input type="text" id="schedule_clinic_location" name="clinic_location" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="schedule_notes">Notes</label>
                        <textarea id="schedule_notes" name="notes" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Schedule Vaccination</button>
                        <button type="button" onclick="closeScheduleVaccinationModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentBaby = null;
        let vaccinationData = [];

        // Standard vaccination schedule
        const vaccinationSchedule = [
            { vaccine: 'Hepatitis B', age_months: 0, dose: 1 },
            { vaccine: 'Hepatitis B', age_months: 1, dose: 2 },
            { vaccine: 'DTaP', age_months: 2, dose: 1 },
            { vaccine: 'Hib', age_months: 2, dose: 1 },
            { vaccine: 'PCV13', age_months: 2, dose: 1 },
            { vaccine: 'IPV', age_months: 2, dose: 1 },
            { vaccine: 'Rotavirus', age_months: 2, dose: 1 },
            { vaccine: 'DTaP', age_months: 4, dose: 2 },
            { vaccine: 'Hib', age_months: 4, dose: 2 },
            { vaccine: 'PCV13', age_months: 4, dose: 2 },
            { vaccine: 'IPV', age_months: 4, dose: 2 },
            { vaccine: 'Rotavirus', age_months: 4, dose: 2 },
            { vaccine: 'Hepatitis B', age_months: 6, dose: 3 },
            { vaccine: 'DTaP', age_months: 6, dose: 3 },
            { vaccine: 'Hib', age_months: 6, dose: 3 },
            { vaccine: 'PCV13', age_months: 6, dose: 3 },
            { vaccine: 'IPV', age_months: 6, dose: 3 },
            { vaccine: 'Rotavirus', age_months: 6, dose: 3 },
            { vaccine: 'Influenza', age_months: 6, dose: 1 },
            { vaccine: 'MMR', age_months: 12, dose: 1 },
            { vaccine: 'Varicella', age_months: 12, dose: 1 },
            { vaccine: 'Hepatitis A', age_months: 12, dose: 1 },
            { vaccine: 'PCV13', age_months: 12, dose: 4 },
            { vaccine: 'Hib', age_months: 12, dose: 4 },
            { vaccine: 'DTaP', age_months: 15, dose: 4 },
            { vaccine: 'Hepatitis A', age_months: 18, dose: 2 }
        ];

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBabies();
            setDefaultDates();
        });

        function setDefaultDates() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('vaccination_date').value = today;
            document.getElementById('scheduled_date').min = today;
        }

        async function loadBabies() {
            try {
                const response = await apiCall('/mother/babies', 'GET');
                if (response.success) {
                    const select = document.getElementById('selected-baby');
                    select.innerHTML = '<option value="">Select a baby...</option>';
                    
                    response.data.forEach(baby => {
                        const option = document.createElement('option');
                        option.value = baby.id;
                        option.textContent = baby.baby_name;
                        select.appendChild(option);
                    });

                    // Auto-select if only one baby
                    if (response.data.length === 1) {
                        select.value = response.data[0].id;
                        loadVaccinationData();
                    }
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        async function loadVaccinationData() {
            const babyId = document.getElementById('selected-baby').value;
            if (!babyId) {
                document.getElementById('vaccination-content').style.display = 'none';
                return;
            }

            try {
                const [babyResponse, vaccinationResponse] = await Promise.all([
                    apiCall(`/mother/babies/${babyId}`, 'GET'),
                    apiCall(`/mother/babies/${babyId}/vaccinations`, 'GET')
                ]);

                if (babyResponse.success && vaccinationResponse.success) {
                    currentBaby = babyResponse.data;
                    vaccinationData = vaccinationResponse.data;
                    
                    document.getElementById('baby_id').value = babyId;
                    document.getElementById('schedule_baby_id').value = babyId;
                    
                    displayVaccinationData();
                    document.getElementById('vaccination-content').style.display = 'block';
                }
            } catch (error) {
                console.error('Error loading vaccination data:', error);
            }
        }

        function displayVaccinationData() {
            displayUpcomingVaccinations();
            displayVaccinationTimeline();
            displayVaccinationHistory();
            displayNextVaccination();
            updateVaccinationProgress();
        }

        function displayUpcomingVaccinations() {
            const container = document.getElementById('upcoming-vaccinations');
            const upcoming = getUpcomingVaccinations();
            
            if (upcoming.length === 0) {
                container.innerHTML = '<p class="text-secondary">No upcoming vaccinations. Your baby is up to date!</p>';
                return;
            }

            container.innerHTML = upcoming.slice(0, 3).map(vaccination => `
                <div class="vaccination-card ${vaccination.overdue ? 'overdue' : ''}">
                    <div class="vaccination-info">
                        <h4>${vaccination.vaccine}</h4>
                        <p><strong>Dose:</strong> ${vaccination.dose}</p>
                        <p><strong>Recommended Age:</strong> ${vaccination.age_months} months</p>
                        ${vaccination.overdue ? '<p class="text-danger"><strong>OVERDUE</strong></p>' : ''}
                    </div>
                    <div class="vaccination-actions">
                        <button onclick="recordVaccination('${vaccination.vaccine}', ${vaccination.dose})" class="btn btn-sm btn-primary">Record</button>
                        <button onclick="scheduleVaccination('${vaccination.vaccine}', ${vaccination.dose})" class="btn btn-sm btn-outline">Schedule</button>
                    </div>
                </div>
            `).join('');
        }

        function displayVaccinationTimeline() {
            const container = document.getElementById('vaccination-timeline');
            const timeline = generateVaccinationTimeline();
            
            container.innerHTML = timeline.map(item => `
                <div class="timeline-item ${item.status}">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h4>${item.vaccine} - Dose ${item.dose}</h4>
                            <span class="timeline-age">${item.age_months} months</span>
                        </div>
                        <div class="timeline-details">
                            ${item.status === 'completed' ? 
                                `<p><strong>Given:</strong> ${item.date_given}</p>
                                 <p><strong>Provider:</strong> ${item.healthcare_provider}</p>` :
                                `<p><strong>Due:</strong> ${item.due_date || 'Based on age'}</p>`
                            }
                        </div>
                        <div class="timeline-status">
                            <span class="badge badge-${getStatusColor(item.status)}">${formatStatus(item.status)}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function displayVaccinationHistory() {
            const tbody = document.getElementById('vaccination-history');
            const completed = vaccinationData.filter(v => v.status === 'completed');
            
            if (completed.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-secondary">No vaccination records found.</td></tr>';
                return;
            }

            tbody.innerHTML = completed.map(vaccination => `
                <tr>
                    <td>${formatDate(vaccination.vaccination_date)}</td>
                    <td>${vaccination.vaccine_name}</td>
                    <td>${vaccination.dose_number}</td>
                    <td>${vaccination.healthcare_provider}</td>
                    <td>${vaccination.batch_number || '-'}</td>
                    <td>
                        <button onclick="viewVaccinationDetails('${vaccination.id}')" class="btn btn-sm btn-outline">View</button>
                        <button onclick="editVaccination('${vaccination.id}')" class="btn btn-sm btn-secondary">Edit</button>
                    </td>
                </tr>
            `).join('');
        }

        function displayNextVaccination() {
            const container = document.getElementById('next-vaccination');
            const upcoming = getUpcomingVaccinations();
            
            if (upcoming.length === 0) {
                container.innerHTML = '<p class="text-secondary">No upcoming vaccinations scheduled.</p>';
                return;
            }

            const next = upcoming[0];
            container.innerHTML = `
                <div class="next-vaccination-details">
                    <h4>${next.vaccine}</h4>
                    <p><strong>Dose:</strong> ${next.dose}</p>
                    <p><strong>Recommended Age:</strong> ${next.age_months} months</p>
                    ${next.overdue ? 
                        '<p class="text-danger"><strong>This vaccination is overdue!</strong></p>' :
                        `<p class="text-info">Due in ${next.days_until_due} days</p>`
                    }
                    <div class="next-vaccination-actions">
                        <button onclick="recordVaccination('${next.vaccine}', ${next.dose})" class="btn btn-sm btn-primary">Record Now</button>
                        <button onclick="scheduleVaccination('${next.vaccine}', ${next.dose})" class="btn btn-sm btn-outline">Schedule</button>
                    </div>
                </div>
            `;
        }

        function updateVaccinationProgress() {
            const totalVaccinations = vaccinationSchedule.length;
            const completedVaccinations = vaccinationData.filter(v => v.status === 'completed').length;
            const percentage = Math.round((completedVaccinations / totalVaccinations) * 100);
            
            document.getElementById('vaccination-progress').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = `${percentage}% Complete (${completedVaccinations}/${totalVaccinations})`;
        }

        function getUpcomingVaccinations() {
            if (!currentBaby || !currentBaby.date_of_birth) return [];
            
            const birthDate = new Date(currentBaby.date_of_birth);
            const today = new Date();
            const ageInMonths = Math.floor((today - birthDate) / (1000 * 60 * 60 * 24 * 30.44));
            
            const completed = vaccinationData.filter(v => v.status === 'completed');
            
            return vaccinationSchedule
                .filter(schedule => {
                    // Check if this vaccination is already completed
                    const isCompleted = completed.some(v => 
                        v.vaccine_name === schedule.vaccine && 
                        parseInt(v.dose_number) === schedule.dose
                    );
                    return !isCompleted && schedule.age_months <= ageInMonths + 2; // Include upcoming within 2 months
                })
                .map(schedule => {
                    const dueDate = new Date(birthDate);
                    dueDate.setMonth(dueDate.getMonth() + schedule.age_months);
                    const daysDiff = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
                    
                    return {
                        ...schedule,
                        due_date: dueDate.toISOString().split('T')[0],
                        days_until_due: daysDiff,
                        overdue: daysDiff < 0
                    };
                })
                .sort((a, b) => a.age_months - b.age_months);
        }

        function generateVaccinationTimeline() {
            const completed = vaccinationData.filter(v => v.status === 'completed');
            
            return vaccinationSchedule.map(schedule => {
                const completedVaccination = completed.find(v => 
                    v.vaccine_name === schedule.vaccine && 
                    parseInt(v.dose_number) === schedule.dose
                );
                
                if (completedVaccination) {
                    return {
                        ...schedule,
                        status: 'completed',
                        date_given: completedVaccination.vaccination_date,
                        healthcare_provider: completedVaccination.healthcare_provider
                    };
                } else {
                    const birthDate = new Date(currentBaby.date_of_birth);
                    const today = new Date();
                    const ageInMonths = Math.floor((today - birthDate) / (1000 * 60 * 60 * 24 * 30.44));
                    
                    if (schedule.age_months <= ageInMonths) {
                        return { ...schedule, status: 'overdue' };
                    } else {
                        return { ...schedule, status: 'pending' };
                    }
                }
            });
        }

        function recordVaccination(vaccineName = '', doseNumber = '') {
            if (vaccineName) {
                document.getElementById('vaccine_name').value = vaccineName;
            }
            if (doseNumber) {
                document.getElementById('dose_number').value = doseNumber;
            }
            document.getElementById('record-vaccination-modal').style.display = 'block';
        }

        function closeRecordVaccinationModal() {
            document.getElementById('record-vaccination-modal').style.display = 'none';
            document.getElementById('record-vaccination-form').reset();
        }

        function scheduleVaccination(vaccineName = '', doseNumber = '') {
            if (vaccineName) {
                document.getElementById('schedule_vaccine_name').value = vaccineName;
            }
            document.getElementById('schedule-vaccination-modal').style.display = 'block';
        }

        function closeScheduleVaccinationModal() {
            document.getElementById('schedule-vaccination-modal').style.display = 'none';
            document.getElementById('schedule-vaccination-form').reset();
        }

        // Form submission handlers
        document.getElementById('record-vaccination-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/babies/vaccinations', 'POST', data);
                if (response.success) {
                    showNotification('Vaccination recorded successfully!', 'success');
                    closeRecordVaccinationModal();
                    loadVaccinationData();
                }
            } catch (error) {
                showNotification('Error recording vaccination: ' + error.message, 'error');
            }
        });

        document.getElementById('schedule-vaccination-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                data.status = 'scheduled';
                
                const response = await apiCall('/mother/babies/vaccinations/schedule', 'POST', data);
                if (response.success) {
                    showNotification('Vaccination scheduled successfully!', 'success');
                    closeScheduleVaccinationModal();
                    loadVaccinationData();
                }
            } catch (error) {
                showNotification('Error scheduling vaccination: ' + error.message, 'error');
            }
        });

        // Utility functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function formatStatus(status) {
            return status.charAt(0).toUpperCase() + status.slice(1);
        }

        function getStatusColor(status) {
            const colors = {
                'completed': 'success',
                'pending': 'secondary',
                'overdue': 'danger',
                'scheduled': 'primary'
            };
            return colors[status] || 'secondary';
        }

        function filterTimeline(filter) {
            // Update active button
            document.querySelectorAll('.timeline-filters .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter timeline items
            const items = document.querySelectorAll('.timeline-item');
            items.forEach(item => {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Placeholder functions
        async function saveReminderSettings() {
            // Save reminder settings
            showNotification('Reminder settings saved!', 'success');
        }

        async function viewVaccinationDetails(vaccinationId) {
            // View vaccination details
        }

        async function editVaccination(vaccinationId) {
            // Edit vaccination
        }
    </script>
</body>
</html>
