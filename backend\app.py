#!/usr/bin/env python3
"""
Maternal-Child Health Care System - Flask Backend
A comprehensive backend API for pregnancy and baby care management
"""

import os
import sqlite3
import hashlib
import json
import logging
from datetime import datetime, timedelta
from functools import wraps

from flask import Flask, request, jsonify, g, send_from_directory, render_template_string, redirect, url_for, session, flash, render_template
from flask_cors import CORS
import google.generativeai as genai
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the frontend directory path
FRONTEND_DIR = Path(__file__).parent.parent / "frontend"

# Initialize Flask app with static folder pointing to frontend
app = Flask(__name__,
            static_folder=str(FRONTEND_DIR),
            static_url_path='')
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'

# Initialize extensions - Allow all origins since we're serving from same port
CORS(app, origins=["*"])

# Database configuration
DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'database', 'preg_baby_care.db')
os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)

# Google AI configuration
GOOGLE_AI_API_KEY = "AIzaSyCicw6WxXq5-TtRmwRLwlnmSVArF4JH1KA"
genai.configure(api_key=GOOGLE_AI_API_KEY)

def get_db():
    """Get database connection"""
    if 'db' not in g:
        g.db = sqlite3.connect(DATABASE_PATH)
        g.db.row_factory = sqlite3.Row
    return g.db

def close_db(e=None):
    """Close database connection"""
    db = g.pop('db', None)
    if db is not None:
        db.close()

@app.teardown_appcontext
def close_db_teardown(error):
    close_db()

def init_database():
    """Initialize database with all required tables"""
    db = sqlite3.connect(DATABASE_PATH)
    cursor = db.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            phone TEXT,
            date_of_birth DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Pregnancy profiles table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pregnancy_profiles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            due_date DATE,
            current_week INTEGER,
            pre_pregnancy_weight REAL,
            current_weight REAL,
            blood_type TEXT,
            allergies TEXT,
            medical_conditions TEXT,
            doctor_name TEXT,
            hospital_name TEXT,
            emergency_contact_name TEXT,
            emergency_contact_phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Baby profiles table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS baby_profiles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            baby_name TEXT NOT NULL,
            baby_id TEXT UNIQUE NOT NULL,
            date_of_birth DATE,
            birth_weight REAL,
            birth_height REAL,
            blood_type TEXT,
            gender TEXT,
            birth_hospital TEXT,
            pediatrician_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Weight tracking table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS weight_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            weight REAL NOT NULL,
            date_recorded DATE NOT NULL,
            week_of_pregnancy INTEGER,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Sleep tracking table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sleep_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            baby_id INTEGER NOT NULL,
            sleep_start TIMESTAMP NOT NULL,
            sleep_end TIMESTAMP,
            duration_minutes INTEGER,
            sleep_quality TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (baby_id) REFERENCES baby_profiles (id)
        )
    ''')
    
    # Vaccinations table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vaccinations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            baby_id INTEGER NOT NULL,
            vaccine_name TEXT NOT NULL,
            date_administered DATE NOT NULL,
            next_due_date DATE,
            administered_by TEXT,
            location TEXT,
            batch_number TEXT,
            side_effects TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (baby_id) REFERENCES baby_profiles (id)
        )
    ''')
    
    # Appointments table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS appointments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            appointment_type TEXT NOT NULL,
            doctor_name TEXT NOT NULL,
            appointment_date TIMESTAMP NOT NULL,
            location TEXT,
            notes TEXT,
            status TEXT DEFAULT 'scheduled',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Nutrition tracking table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nutrition_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            meal_type TEXT NOT NULL,
            food_items TEXT NOT NULL,
            calories INTEGER,
            date_recorded DATE NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Meditation sessions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS meditation_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_type TEXT NOT NULL,
            duration_minutes INTEGER NOT NULL,
            date_recorded DATE NOT NULL,
            mood_before TEXT,
            mood_after TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Chat messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            message TEXT NOT NULL,
            response TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Admin content management tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS admin_content (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            content TEXT NOT NULL,
            image_url TEXT,
            tags TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_by INTEGER NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')

    # Content categories table for better organization
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS content_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            display_name TEXT NOT NULL,
            description TEXT,
            icon TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    db.commit()
    db.close()
    logger.info("Database initialized successfully")

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_default_users():
    """Create default users for testing"""
    db = sqlite3.connect(DATABASE_PATH)
    cursor = db.cursor()

    # Check if users already exist
    cursor.execute("SELECT COUNT(*) FROM users")
    if cursor.fetchone()[0] > 0:
        db.close()
        return

    # Create default users
    default_users = [
        {
            'email': '<EMAIL>',
            'password': 'admin123',
            'full_name': 'System Administrator',
            'role': 'admin',
            'phone': '+1234567890'
        },
        {
            'email': '<EMAIL>',
            'password': 'doctor123',
            'full_name': 'Dr. Sarah Johnson',
            'role': 'doctor',
            'phone': '+1234567891'
        },
        {
            'email': '<EMAIL>',
            'password': 'user123',
            'full_name': 'Jane Smith',
            'role': 'user',
            'phone': '+1234567892'
        }
    ]

    for user_data in default_users:
        cursor.execute('''
            INSERT INTO users (email, password_hash, full_name, role, phone)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            user_data['email'],
            hash_password(user_data['password']),
            user_data['full_name'],
            user_data['role'],
            user_data['phone']
        ))

    db.commit()
    db.close()
    logger.info("Default users created successfully")

def create_default_content_categories():
    """Create default content categories"""
    try:
        db = get_db()
        cursor = db.cursor()

        categories = [
            ('nutrition_plans', 'Nutrition Plans', 'Comprehensive nutrition guides for mothers and babies', 'fas fa-apple-alt'),
            ('meal_plans', 'Meal Plans', 'Daily and weekly meal planning guides', 'fas fa-utensils'),
            ('government_schemes', 'Government Schemes', 'Government support programs and schemes', 'fas fa-landmark'),
            ('exercise_guides', 'Exercise Guides', 'Safe exercise routines for pregnancy and postpartum', 'fas fa-dumbbell'),
            ('vaccinations', 'Vaccinations', 'Vaccination schedules and information', 'fas fa-syringe'),
            ('physical_activities', 'Physical Activities', 'Physical activity recommendations and guides', 'fas fa-running'),
            ('baby_nutrition', 'Baby Nutrition', 'Infant and toddler nutrition guidelines', 'fas fa-baby'),
            ('sleep_tracker', 'Sleep Tracker', 'Sleep patterns and improvement guides', 'fas fa-bed')
        ]

        for name, display_name, description, icon in categories:
            cursor.execute("SELECT id FROM content_categories WHERE name = ?", (name,))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO content_categories (name, display_name, description, icon)
                    VALUES (?, ?, ?, ?)
                ''', (name, display_name, description, icon))

        db.commit()
        db.close()
        logger.info("Default content categories initialized")

    except Exception as e:
        logger.error(f"Error creating content categories: {str(e)}")

# Initialize database on startup
with app.app_context():
    init_database()
    create_default_users()
    create_default_content_categories()

# Utility functions
def require_login(f):
    """Decorator to require user login"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in') or not session.get('user_email'):
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

def require_role(required_role):
    """Decorator to require specific user role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not session.get('logged_in') or not session.get('user_email'):
                return jsonify({'error': 'Authentication required'}), 401

            if session.get('user_role') != required_role:
                return jsonify({'error': 'Insufficient permissions'}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_current_user_email():
    """Get current user email from session"""
    return session.get('user_email')

# API Routes

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Maternal-Child Health Care System API is running',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0'
    })

@app.route('/api/register', methods=['POST'])
def register():
    """User registration endpoint"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'full_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        db = get_db()
        cursor = db.cursor()
        
        # Check if user already exists
        cursor.execute("SELECT id FROM users WHERE email = ?", (data['email'],))
        if cursor.fetchone():
            return jsonify({'error': 'User already exists'}), 409
        
        # Create new user
        cursor.execute('''
            INSERT INTO users (email, password_hash, full_name, role, phone, date_of_birth)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            data['email'],
            hash_password(data['password']),
            data['full_name'],
            data.get('role', 'user'),
            data.get('phone'),
            data.get('date_of_birth')
        ))
        
        db.commit()
        
        return jsonify({
            'message': 'User registered successfully',
            'user': {
                'email': data['email'],
                'full_name': data['full_name'],
                'role': data.get('role', 'user')
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    """User login endpoint"""
    try:
        data = request.get_json()
        
        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400
        
        db = get_db()
        cursor = db.cursor()
        
        # Find user
        cursor.execute('''
            SELECT id, email, password_hash, full_name, role, phone, date_of_birth
            FROM users WHERE email = ?
        ''', (data['email'],))
        
        user = cursor.fetchone()
        
        if not user or user['password_hash'] != hash_password(data['password']):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user['id'],
                'email': user['email'],
                'full_name': user['full_name'],
                'role': user['role'],
                'phone': user['phone'],
                'date_of_birth': user['date_of_birth']
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({'error': 'Login failed'}), 500

# Pregnancy Profile Routes

@app.route('/api/pregnancy/profile', methods=['GET', 'POST'])
@require_login
def pregnancy_profile():
    """Get or create/update pregnancy profile"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM pregnancy_profiles WHERE user_id = ?
            ''', (user_id,))
            profile = cursor.fetchone()

            if profile:
                return jsonify(dict(profile)), 200
            else:
                return jsonify({'message': 'No pregnancy profile found'}), 404

        elif request.method == 'POST':
            data = request.get_json()

            # Check if profile exists
            cursor.execute("SELECT id FROM pregnancy_profiles WHERE user_id = ?", (user_id,))
            existing_profile = cursor.fetchone()

            if existing_profile:
                # Update existing profile
                cursor.execute('''
                    UPDATE pregnancy_profiles SET
                        due_date = ?, current_week = ?, pre_pregnancy_weight = ?,
                        current_weight = ?, blood_type = ?, allergies = ?,
                        medical_conditions = ?, doctor_name = ?, hospital_name = ?,
                        emergency_contact_name = ?, emergency_contact_phone = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ''', (
                    data.get('due_date'), data.get('current_week'),
                    data.get('pre_pregnancy_weight'), data.get('current_weight'),
                    data.get('blood_type'), data.get('allergies'),
                    data.get('medical_conditions'), data.get('doctor_name'),
                    data.get('hospital_name'), data.get('emergency_contact_name'),
                    data.get('emergency_contact_phone'), user_id
                ))
            else:
                # Create new profile
                cursor.execute('''
                    INSERT INTO pregnancy_profiles (
                        user_id, due_date, current_week, pre_pregnancy_weight,
                        current_weight, blood_type, allergies, medical_conditions,
                        doctor_name, hospital_name, emergency_contact_name,
                        emergency_contact_phone
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_id, data.get('due_date'), data.get('current_week'),
                    data.get('pre_pregnancy_weight'), data.get('current_weight'),
                    data.get('blood_type'), data.get('allergies'),
                    data.get('medical_conditions'), data.get('doctor_name'),
                    data.get('hospital_name'), data.get('emergency_contact_name'),
                    data.get('emergency_contact_phone')
                ))

            db.commit()
            return jsonify({'message': 'Pregnancy profile saved successfully'}), 200

    except Exception as e:
        logger.error(f"Pregnancy profile error: {str(e)}")
        return jsonify({'error': 'Failed to process pregnancy profile'}), 500

# Weight Tracking Routes

@app.route('/api/weight/track', methods=['GET', 'POST'])
@require_login
def weight_tracking():
    """Get or add weight tracking data"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM weight_tracking
                WHERE user_id = ?
                ORDER BY date_recorded DESC
            ''', (user_id,))
            weights = cursor.fetchall()

            return jsonify([dict(weight) for weight in weights]), 200

        elif request.method == 'POST':
            data = request.get_json()

            if not data.get('weight') or not data.get('date_recorded'):
                return jsonify({'error': 'Weight and date are required'}), 400

            cursor.execute('''
                INSERT INTO weight_tracking (
                    user_id, weight, date_recorded, week_of_pregnancy, notes
                ) VALUES (?, ?, ?, ?, ?)
            ''', (
                user_id, data['weight'], data['date_recorded'],
                data.get('week_of_pregnancy'), data.get('notes')
            ))

            db.commit()
            return jsonify({'message': 'Weight recorded successfully'}), 201

    except Exception as e:
        logger.error(f"Weight tracking error: {str(e)}")
        return jsonify({'error': 'Failed to process weight tracking'}), 500

# Baby Profile Routes

@app.route('/api/baby/profile', methods=['GET', 'POST'])
@require_login
def baby_profile():
    """Get or create/update baby profile"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM baby_profiles WHERE user_id = ?
            ''', (user_id,))
            profiles = cursor.fetchall()

            return jsonify([dict(profile) for profile in profiles]), 200

        elif request.method == 'POST':
            data = request.get_json()

            if not data.get('baby_name'):
                return jsonify({'error': 'Baby name is required'}), 400

            # Generate unique baby ID
            import uuid
            baby_id = f"BABY-{uuid.uuid4().hex[:8].upper()}"

            cursor.execute('''
                INSERT INTO baby_profiles (
                    user_id, baby_name, baby_id, date_of_birth, birth_weight,
                    birth_height, blood_type, gender, birth_hospital, pediatrician_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, data['baby_name'], baby_id, data.get('date_of_birth'),
                data.get('birth_weight'), data.get('birth_height'),
                data.get('blood_type'), data.get('gender'),
                data.get('birth_hospital'), data.get('pediatrician_name')
            ))

            db.commit()
            return jsonify({
                'message': 'Baby profile created successfully',
                'baby_id': baby_id
            }), 201

    except Exception as e:
        logger.error(f"Baby profile error: {str(e)}")
        return jsonify({'error': 'Failed to process baby profile'}), 500

# Sleep Tracking Routes

@app.route('/api/baby/sleep', methods=['GET', 'POST'])
@require_login
def sleep_tracking():
    """Get or add sleep tracking data"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            baby_id = request.args.get('baby_id')
            if not baby_id:
                return jsonify({'error': 'Baby ID is required'}), 400

            # Verify baby belongs to user
            cursor.execute("SELECT id FROM baby_profiles WHERE id = ? AND user_id = ?", (baby_id, user_id))
            if not cursor.fetchone():
                return jsonify({'error': 'Baby not found'}), 404

            cursor.execute('''
                SELECT * FROM sleep_tracking
                WHERE baby_id = ?
                ORDER BY sleep_start DESC
            ''', (baby_id,))
            sleep_records = cursor.fetchall()

            return jsonify([dict(record) for record in sleep_records]), 200

        elif request.method == 'POST':
            data = request.get_json()

            if not data.get('baby_id') or not data.get('sleep_start'):
                return jsonify({'error': 'Baby ID and sleep start time are required'}), 400

            # Verify baby belongs to user
            cursor.execute("SELECT id FROM baby_profiles WHERE id = ? AND user_id = ?", (data['baby_id'], user_id))
            if not cursor.fetchone():
                return jsonify({'error': 'Baby not found'}), 404

            cursor.execute('''
                INSERT INTO sleep_tracking (
                    baby_id, sleep_start, sleep_end, duration_minutes, sleep_quality, notes
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                data['baby_id'], data['sleep_start'], data.get('sleep_end'),
                data.get('duration_minutes'), data.get('sleep_quality'), data.get('notes')
            ))

            db.commit()
            return jsonify({'message': 'Sleep record added successfully'}), 201

    except Exception as e:
        logger.error(f"Sleep tracking error: {str(e)}")
        return jsonify({'error': 'Failed to process sleep tracking'}), 500

# Vaccination Routes

@app.route('/api/vaccinations', methods=['GET', 'POST'])
@require_login
def vaccinations():
    """Get or add vaccination records"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            baby_id = request.args.get('baby_id')
            if not baby_id:
                return jsonify({'error': 'Baby ID is required'}), 400

            # Verify baby belongs to user
            cursor.execute("SELECT id FROM baby_profiles WHERE id = ? AND user_id = ?", (baby_id, user_id))
            if not cursor.fetchone():
                return jsonify({'error': 'Baby not found'}), 404

            cursor.execute('''
                SELECT * FROM vaccinations
                WHERE baby_id = ?
                ORDER BY date_administered DESC
            ''', (baby_id,))
            vaccination_records = cursor.fetchall()

            return jsonify([dict(record) for record in vaccination_records]), 200

        elif request.method == 'POST':
            data = request.get_json()

            required_fields = ['baby_id', 'vaccine_name', 'date_administered']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            # Verify baby belongs to user
            cursor.execute("SELECT id FROM baby_profiles WHERE id = ? AND user_id = ?", (data['baby_id'], user_id))
            if not cursor.fetchone():
                return jsonify({'error': 'Baby not found'}), 404

            cursor.execute('''
                INSERT INTO vaccinations (
                    baby_id, vaccine_name, date_administered, next_due_date,
                    administered_by, location, batch_number, side_effects
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['baby_id'], data['vaccine_name'], data['date_administered'],
                data.get('next_due_date'), data.get('administered_by'),
                data.get('location'), data.get('batch_number'), data.get('side_effects')
            ))

            db.commit()
            return jsonify({'message': 'Vaccination record added successfully'}), 201

    except Exception as e:
        logger.error(f"Vaccination error: {str(e)}")
        return jsonify({'error': 'Failed to process vaccination record'}), 500

# Appointment Routes

@app.route('/api/appointments', methods=['GET', 'POST'])
@require_login
def appointments():
    """Get or create appointments"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM appointments
                WHERE user_id = ?
                ORDER BY appointment_date ASC
            ''', (user_id,))
            appointment_records = cursor.fetchall()

            return jsonify([dict(record) for record in appointment_records]), 200

        elif request.method == 'POST':
            data = request.get_json()

            required_fields = ['appointment_type', 'doctor_name', 'appointment_date']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            cursor.execute('''
                INSERT INTO appointments (
                    user_id, appointment_type, doctor_name, appointment_date,
                    location, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, data['appointment_type'], data['doctor_name'],
                data['appointment_date'], data.get('location'),
                data.get('notes'), data.get('status', 'scheduled')
            ))

            db.commit()
            return jsonify({'message': 'Appointment scheduled successfully'}), 201

    except Exception as e:
        logger.error(f"Appointment error: {str(e)}")
        return jsonify({'error': 'Failed to process appointment'}), 500

# Nutrition Tracking Routes

@app.route('/api/nutrition', methods=['GET', 'POST'])
@require_login
def nutrition_tracking():
    """Get or add nutrition tracking data"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM nutrition_tracking
                WHERE user_id = ?
                ORDER BY date_recorded DESC
            ''', (user_id,))
            nutrition_records = cursor.fetchall()

            return jsonify([dict(record) for record in nutrition_records]), 200

        elif request.method == 'POST':
            data = request.get_json()

            required_fields = ['meal_type', 'food_items', 'date_recorded']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            cursor.execute('''
                INSERT INTO nutrition_tracking (
                    user_id, meal_type, food_items, calories, date_recorded, notes
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                user_id, data['meal_type'], data['food_items'],
                data.get('calories'), data['date_recorded'], data.get('notes')
            ))

            db.commit()
            return jsonify({'message': 'Nutrition record added successfully'}), 201

    except Exception as e:
        logger.error(f"Nutrition tracking error: {str(e)}")
        return jsonify({'error': 'Failed to process nutrition tracking'}), 500

# Meditation Routes

@app.route('/api/meditation', methods=['GET', 'POST'])
@require_login
def meditation_sessions():
    """Get or add meditation session data"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']

        if request.method == 'GET':
            cursor.execute('''
                SELECT * FROM meditation_sessions
                WHERE user_id = ?
                ORDER BY date_recorded DESC
            ''', (user_id,))
            meditation_records = cursor.fetchall()

            return jsonify([dict(record) for record in meditation_records]), 200

        elif request.method == 'POST':
            data = request.get_json()

            required_fields = ['session_type', 'duration_minutes', 'date_recorded']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required'}), 400

            cursor.execute('''
                INSERT INTO meditation_sessions (
                    user_id, session_type, duration_minutes, date_recorded,
                    mood_before, mood_after, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, data['session_type'], data['duration_minutes'],
                data['date_recorded'], data.get('mood_before'),
                data.get('mood_after'), data.get('notes')
            ))

            db.commit()
            return jsonify({'message': 'Meditation session recorded successfully'}), 201

    except Exception as e:
        logger.error(f"Meditation tracking error: {str(e)}")
        return jsonify({'error': 'Failed to process meditation session'}), 500

# AI Chatbot Routes

@app.route('/api/chat', methods=['POST'])
@require_login
def ai_chat():
    """AI chatbot endpoint using Google Gemini"""
    try:
        current_user_email = get_current_user_email()
        db = get_db()
        cursor = db.cursor()

        # Get user ID
        cursor.execute("SELECT id FROM users WHERE email = ?", (current_user_email,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404

        user_id = user['id']
        data = request.get_json()

        if not data.get('message'):
            return jsonify({'error': 'Message is required'}), 400

        user_message = data['message']

        try:
            # Initialize Gemini model
            model = genai.GenerativeModel('gemini-pro')

            # Create context for maternal and child health
            context = """You are a helpful AI assistant specializing in maternal and child health care.
            You provide accurate, supportive information about pregnancy, baby care, nutrition, and wellness.
            Always recommend consulting healthcare professionals for medical concerns.
            Keep responses helpful, empathetic, and informative."""

            # Generate response
            response = model.generate_content(f"{context}\n\nUser question: {user_message}")
            ai_response = response.text

        except Exception as ai_error:
            logger.error(f"AI generation error: {str(ai_error)}")
            # Fallback response
            ai_response = "I'm sorry, I'm having trouble connecting to the AI service right now. Please try again later or consult with your healthcare provider for medical questions."

        # Save chat message to database
        cursor.execute('''
            INSERT INTO chat_messages (user_id, message, response)
            VALUES (?, ?, ?)
        ''', (user_id, user_message, ai_response))

        db.commit()

        return jsonify({
            'message': user_message,
            'response': ai_response,
            'timestamp': datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        return jsonify({'error': 'Failed to process chat message'}), 500

# Admin Routes

@app.route('/api/admin/users', methods=['GET'])
@require_role('admin')
def get_all_users():
    """Get all users (admin only)"""
    try:
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
            SELECT id, email, full_name, role, phone, date_of_birth, created_at
            FROM users ORDER BY created_at DESC
        ''')
        users = cursor.fetchall()

        return jsonify([dict(user) for user in users]), 200

    except Exception as e:
        logger.error(f"Admin users error: {str(e)}")
        return jsonify({'error': 'Failed to fetch users'}), 500

@app.route('/api/admin/stats', methods=['GET'])
@require_role('admin')
def get_system_stats():
    """Get system statistics (admin only)"""
    try:
        db = get_db()
        cursor = db.cursor()

        # Get user counts by role
        cursor.execute("SELECT role, COUNT(*) as count FROM users GROUP BY role")
        user_stats = dict(cursor.fetchall())

        # Get total counts
        cursor.execute("SELECT COUNT(*) FROM pregnancy_profiles")
        pregnancy_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM baby_profiles")
        baby_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM appointments")
        appointment_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM chat_messages")
        chat_count = cursor.fetchone()[0]

        return jsonify({
            'user_stats': user_stats,
            'pregnancy_profiles': pregnancy_count,
            'baby_profiles': baby_count,
            'appointments': appointment_count,
            'chat_messages': chat_count,
            'timestamp': datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Admin stats error: {str(e)}")
        return jsonify({'error': 'Failed to fetch statistics'}), 500

# ============================================================================
# FRONTEND ROUTES - Serve frontend files from the same Flask app
# ============================================================================

# Form-based authentication routes (no API/AJAX)
@app.route('/login', methods=['POST'])
def form_login():
    """Handle form-based login"""
    try:
        email = request.form.get('email')
        password = request.form.get('password')

        if not email or not password:
            flash('Email and password are required', 'error')
            return redirect('/login')

        db = get_db()
        cursor = db.cursor()

        # Find user
        cursor.execute('''
            SELECT id, email, password_hash, full_name, role
            FROM users WHERE email = ?
        ''', (email,))

        user = cursor.fetchone()

        if not user or user['password_hash'] != hash_password(password):
            flash('Invalid email or password', 'error')
            return redirect('/login')

        # Store user in session
        session['user_id'] = user['id']
        session['user_email'] = user['email']
        session['user_name'] = user['full_name']
        session['user_role'] = user['role']
        session['logged_in'] = True

        flash('Login successful!', 'success')

        # Redirect based on role
        if user['role'] == 'admin':
            return redirect('/pages/admin/dashboard.html')
        elif user['role'] == 'doctor':
            return redirect('/pages/doctor/dashboard.html')
        else:
            return redirect('/pages/Preg/pregcare.html')

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        flash('Login failed. Please try again.', 'error')
        return redirect('/login')

@app.route('/signup', methods=['POST'])
def form_signup():
    """Handle form-based signup"""
    try:
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirmPassword')
        full_name = request.form.get('fullName')

        if not all([email, password, confirm_password, full_name]):
            flash('All fields are required', 'error')
            return redirect('/signup')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return redirect('/signup')

        if len(password) < 6:
            flash('Password must be at least 6 characters long', 'error')
            return redirect('/signup')

        db = get_db()
        cursor = db.cursor()

        # Check if user already exists
        cursor.execute("SELECT id FROM users WHERE email = ?", (email,))
        if cursor.fetchone():
            flash('An account with this email already exists', 'error')
            return redirect('/signup')

        # Create new user
        cursor.execute('''
            INSERT INTO users (email, password_hash, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', (email, hash_password(password), full_name, 'user'))

        db.commit()

        flash('Account created successfully! Please login.', 'success')
        return redirect('/login')

    except Exception as e:
        logger.error(f"Signup error: {str(e)}")
        flash('Registration failed. Please try again.', 'error')
        return redirect('/signup')

@app.route('/logout')
def logout():
    """Handle logout"""
    session.clear()
    flash('You have been logged out successfully', 'info')
    return redirect('/')

@app.route('/api/auth/status')
def auth_status():
    """Check authentication status"""
    if session.get('logged_in'):
        return jsonify({
            'authenticated': True,
            'user': {
                'id': session.get('user_id'),
                'email': session.get('user_email'),
                'name': session.get('user_name'),
                'role': session.get('user_role')
            }
        })
    else:
        return jsonify({'authenticated': False})

# ===== DOCTOR API ENDPOINTS =====

@app.route('/api/doctor/patients', methods=['GET'])
@require_login
def get_doctor_patients():
    """Get all patients for doctor dashboard"""
    try:
        # Check if user is a doctor
        if session.get('user_role') != 'doctor':
            return jsonify({'error': 'Access denied. Doctor privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        # Get all users who are patients (not doctors or admins)
        cursor.execute('''
            SELECT u.id, u.full_name, u.email, u.phone, u.created_at,
                   pp.current_week, pp.due_date, pp.pregnancy_status,
                   COUNT(DISTINCT a.id) as appointment_count,
                   MAX(a.appointment_date) as last_appointment
            FROM users u
            LEFT JOIN pregnancy_profiles pp ON u.id = pp.user_id
            LEFT JOIN appointments a ON u.id = a.user_id
            WHERE u.role = 'user'
            GROUP BY u.id
            ORDER BY u.full_name
        ''')

        patients = cursor.fetchall()
        return jsonify([dict(patient) for patient in patients]), 200

    except Exception as e:
        print(f"Error getting doctor patients: {str(e)}")
        return jsonify({'error': 'Failed to get patients'}), 500

@app.route('/api/doctor/appointments', methods=['GET'])
@require_login
def get_doctor_appointments():
    """Get all appointments for doctor"""
    try:
        # Check if user is a doctor
        if session.get('user_role') != 'doctor':
            return jsonify({'error': 'Access denied. Doctor privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        # Get all appointments with patient details
        cursor.execute('''
            SELECT a.*, u.full_name as patient_name, u.email as patient_email, u.phone as patient_phone
            FROM appointments a
            JOIN users u ON a.user_id = u.id
            ORDER BY a.appointment_date DESC
        ''')

        appointments = cursor.fetchall()
        return jsonify([dict(appointment) for appointment in appointments]), 200

    except Exception as e:
        print(f"Error getting doctor appointments: {str(e)}")
        return jsonify({'error': 'Failed to get appointments'}), 500

@app.route('/api/doctor/appointments/<int:appointment_id>/status', methods=['PUT'])
@require_login
def update_appointment_status(appointment_id):
    """Update appointment status"""
    try:
        # Check if user is a doctor
        if session.get('user_role') != 'doctor':
            return jsonify({'error': 'Access denied. Doctor privileges required.'}), 403

        data = request.get_json()
        status = data.get('status')

        if not status:
            return jsonify({'error': 'Status is required'}), 400

        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
            UPDATE appointments
            SET status = ?, updated_at = ?
            WHERE id = ?
        ''', (status, datetime.now().isoformat(), appointment_id))

        db.commit()

        if cursor.rowcount == 0:
            return jsonify({'error': 'Appointment not found'}), 404

        return jsonify({'message': 'Appointment status updated successfully'}), 200

    except Exception as e:
        print(f"Error updating appointment status: {str(e)}")
        return jsonify({'error': 'Failed to update appointment status'}), 500

@app.route('/api/doctor/patient/<int:patient_id>/details', methods=['GET'])
@require_login
def get_patient_details(patient_id):
    """Get detailed patient information"""
    try:
        # Check if user is a doctor
        if session.get('user_role') != 'doctor':
            return jsonify({'error': 'Access denied. Doctor privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        # Get patient basic info
        cursor.execute('''
            SELECT u.*, pp.current_week, pp.due_date, pp.pregnancy_status, pp.last_period_date
            FROM users u
            LEFT JOIN pregnancy_profiles pp ON u.id = pp.user_id
            WHERE u.id = ? AND u.role = 'user'
        ''', (patient_id,))

        patient = cursor.fetchone()
        if not patient:
            return jsonify({'error': 'Patient not found'}), 404

        # Get baby profiles
        cursor.execute('SELECT * FROM baby_profiles WHERE user_id = ?', (patient_id,))
        babies = cursor.fetchall()

        # Get recent appointments
        cursor.execute('''
            SELECT * FROM appointments
            WHERE user_id = ?
            ORDER BY appointment_date DESC
            LIMIT 5
        ''', (patient_id,))
        appointments = cursor.fetchall()

        # Get recent weight tracking
        cursor.execute('''
            SELECT * FROM weight_tracking
            WHERE user_id = ?
            ORDER BY date DESC
            LIMIT 10
        ''', (patient_id,))
        weight_records = cursor.fetchall()

        # Get vaccinations
        cursor.execute('''
            SELECT v.*, bp.name as baby_name
            FROM vaccinations v
            LEFT JOIN baby_profiles bp ON v.baby_id = bp.id
            WHERE v.user_id = ?
            ORDER BY v.vaccination_date DESC
        ''', (patient_id,))
        vaccinations = cursor.fetchall()

        return jsonify({
            'patient': dict(patient),
            'babies': [dict(baby) for baby in babies],
            'appointments': [dict(apt) for apt in appointments],
            'weight_records': [dict(wr) for wr in weight_records],
            'vaccinations': [dict(vac) for vac in vaccinations]
        }), 200

    except Exception as e:
        print(f"Error getting patient details: {str(e)}")
        return jsonify({'error': 'Failed to get patient details'}), 500

@app.route('/api/doctor/stats', methods=['GET'])
@require_login
def get_doctor_stats():
    """Get doctor dashboard statistics"""
    try:
        # Check if user is a doctor
        if session.get('user_role') != 'doctor':
            return jsonify({'error': 'Access denied. Doctor privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        # Total patients
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'user'")
        total_patients = cursor.fetchone()['count']

        # Today's appointments
        today = datetime.now().date().isoformat()
        cursor.execute('''
            SELECT COUNT(*) as count FROM appointments
            WHERE DATE(appointment_date) = ?
        ''', (today,))
        today_appointments = cursor.fetchone()['count']

        # This week's appointments
        week_start = (datetime.now() - timedelta(days=datetime.now().weekday())).date().isoformat()
        week_end = (datetime.now() + timedelta(days=6-datetime.now().weekday())).date().isoformat()
        cursor.execute('''
            SELECT COUNT(*) as count FROM appointments
            WHERE DATE(appointment_date) BETWEEN ? AND ?
        ''', (week_start, week_end))
        week_appointments = cursor.fetchone()['count']

        # Active pregnancies
        cursor.execute('''
            SELECT COUNT(*) as count FROM pregnancy_profiles
            WHERE pregnancy_status = 'active'
        ''')
        active_pregnancies = cursor.fetchone()['count']

        # Recent appointments for chart
        cursor.execute('''
            SELECT DATE(appointment_date) as date, COUNT(*) as count
            FROM appointments
            WHERE appointment_date >= date('now', '-30 days')
            GROUP BY DATE(appointment_date)
            ORDER BY date
        ''')
        appointment_chart_data = cursor.fetchall()

        return jsonify({
            'total_patients': total_patients,
            'today_appointments': today_appointments,
            'week_appointments': week_appointments,
            'active_pregnancies': active_pregnancies,
            'appointment_chart_data': [dict(row) for row in appointment_chart_data]
        }), 200

    except Exception as e:
        print(f"Error getting doctor stats: {str(e)}")
        return jsonify({'error': 'Failed to get doctor statistics'}), 500

# ===== ADMIN CONTENT MANAGEMENT API ENDPOINTS =====

@app.route('/api/admin/content/categories', methods=['GET'])
@require_login
def get_content_categories():
    """Get all content categories"""
    try:
        # Check if user is admin
        if session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied. Admin privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        cursor.execute('SELECT * FROM content_categories WHERE is_active = 1 ORDER BY display_name')
        categories = cursor.fetchall()

        return jsonify([dict(category) for category in categories]), 200

    except Exception as e:
        print(f"Error getting content categories: {str(e)}")
        return jsonify({'error': 'Failed to get content categories'}), 500

@app.route('/api/admin/content', methods=['GET'])
@require_login
def get_admin_content():
    """Get all admin content with optional category filter"""
    try:
        # Check if user is admin
        if session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied. Admin privileges required.'}), 403

        category = request.args.get('category')

        db = get_db()
        cursor = db.cursor()

        if category:
            cursor.execute('''
                SELECT ac.*, u.full_name as created_by_name, cc.display_name as category_display_name
                FROM admin_content ac
                JOIN users u ON ac.created_by = u.id
                JOIN content_categories cc ON ac.category = cc.name
                WHERE ac.category = ? AND ac.is_active = 1
                ORDER BY ac.created_at DESC
            ''', (category,))
        else:
            cursor.execute('''
                SELECT ac.*, u.full_name as created_by_name, cc.display_name as category_display_name
                FROM admin_content ac
                JOIN users u ON ac.created_by = u.id
                JOIN content_categories cc ON ac.category = cc.name
                WHERE ac.is_active = 1
                ORDER BY ac.created_at DESC
            ''')

        content = cursor.fetchall()
        return jsonify([dict(item) for item in content]), 200

    except Exception as e:
        print(f"Error getting admin content: {str(e)}")
        return jsonify({'error': 'Failed to get admin content'}), 500

@app.route('/api/admin/content', methods=['POST'])
@require_login
def create_admin_content():
    """Create new admin content"""
    try:
        # Check if user is admin
        if session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied. Admin privileges required.'}), 403

        data = request.get_json()

        # Validate required fields
        required_fields = ['category', 'title', 'content']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        db = get_db()
        cursor = db.cursor()

        # Verify category exists
        cursor.execute('SELECT id FROM content_categories WHERE name = ?', (data['category'],))
        if not cursor.fetchone():
            return jsonify({'error': 'Invalid category'}), 400

        # Create content
        cursor.execute('''
            INSERT INTO admin_content (category, title, description, content, image_url, tags, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['category'],
            data['title'],
            data.get('description', ''),
            data['content'],
            data.get('image_url', ''),
            data.get('tags', ''),
            session.get('user_id')
        ))

        content_id = cursor.lastrowid
        db.commit()

        return jsonify({
            'message': 'Content created successfully',
            'content_id': content_id
        }), 201

    except Exception as e:
        print(f"Error creating admin content: {str(e)}")
        return jsonify({'error': 'Failed to create content'}), 500

@app.route('/api/admin/content/<int:content_id>', methods=['PUT'])
@require_login
def update_admin_content(content_id):
    """Update admin content"""
    try:
        # Check if user is admin
        if session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied. Admin privileges required.'}), 403

        data = request.get_json()

        db = get_db()
        cursor = db.cursor()

        # Check if content exists
        cursor.execute('SELECT id FROM admin_content WHERE id = ?', (content_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'Content not found'}), 404

        # Update content
        cursor.execute('''
            UPDATE admin_content
            SET title = ?, description = ?, content = ?, image_url = ?, tags = ?, updated_at = ?
            WHERE id = ?
        ''', (
            data.get('title'),
            data.get('description', ''),
            data.get('content'),
            data.get('image_url', ''),
            data.get('tags', ''),
            datetime.now().isoformat(),
            content_id
        ))

        db.commit()

        return jsonify({'message': 'Content updated successfully'}), 200

    except Exception as e:
        print(f"Error updating admin content: {str(e)}")
        return jsonify({'error': 'Failed to update content'}), 500

@app.route('/api/admin/content/<int:content_id>', methods=['DELETE'])
@require_login
def delete_admin_content(content_id):
    """Delete admin content (soft delete)"""
    try:
        # Check if user is admin
        if session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied. Admin privileges required.'}), 403

        db = get_db()
        cursor = db.cursor()

        # Check if content exists
        cursor.execute('SELECT id FROM admin_content WHERE id = ?', (content_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'Content not found'}), 404

        # Soft delete (set is_active to 0)
        cursor.execute('''
            UPDATE admin_content
            SET is_active = 0, updated_at = ?
            WHERE id = ?
        ''', (datetime.now().isoformat(), content_id))

        db.commit()

        return jsonify({'message': 'Content deleted successfully'}), 200

    except Exception as e:
        print(f"Error deleting admin content: {str(e)}")
        return jsonify({'error': 'Failed to delete content'}), 500

# ===== USER CONTENT ACCESS API ENDPOINTS =====

@app.route('/api/content/categories', methods=['GET'])
def get_public_content_categories():
    """Get all active content categories for users"""
    try:
        db = get_db()
        cursor = db.cursor()

        cursor.execute('SELECT * FROM content_categories WHERE is_active = 1 ORDER BY display_name')
        categories = cursor.fetchall()

        return jsonify([dict(category) for category in categories]), 200

    except Exception as e:
        print(f"Error getting public content categories: {str(e)}")
        return jsonify({'error': 'Failed to get content categories'}), 500

@app.route('/api/content/<category>', methods=['GET'])
def get_public_content(category):
    """Get all active content for a specific category"""
    try:
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
            SELECT ac.id, ac.title, ac.description, ac.content, ac.image_url, ac.tags, ac.created_at,
                   cc.display_name as category_display_name, cc.icon as category_icon
            FROM admin_content ac
            JOIN content_categories cc ON ac.category = cc.name
            WHERE ac.category = ? AND ac.is_active = 1 AND cc.is_active = 1
            ORDER BY ac.created_at DESC
        ''', (category,))

        content = cursor.fetchall()
        return jsonify([dict(item) for item in content]), 200

    except Exception as e:
        print(f"Error getting public content: {str(e)}")
        return jsonify({'error': 'Failed to get content'}), 500

@app.route('/api/content/item/<int:content_id>', methods=['GET'])
def get_content_item(content_id):
    """Get a specific content item by ID"""
    try:
        db = get_db()
        cursor = db.cursor()

        cursor.execute('''
            SELECT ac.*, cc.display_name as category_display_name, cc.icon as category_icon
            FROM admin_content ac
            JOIN content_categories cc ON ac.category = cc.name
            WHERE ac.id = ? AND ac.is_active = 1 AND cc.is_active = 1
        ''', (content_id,))

        content = cursor.fetchone()

        if not content:
            return jsonify({'error': 'Content not found'}), 404

        return jsonify(dict(content)), 200

    except Exception as e:
        print(f"Error getting content item: {str(e)}")
        return jsonify({'error': 'Failed to get content item'}), 500

@app.route('/')
def index():
    """Serve the home page as the default route"""
    return send_from_directory(str(FRONTEND_DIR), 'home.html')

def require_login_for_page(f):
    """Decorator to require login for page access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            flash('Please login to access this page', 'error')
            return redirect('/login')
        return f(*args, **kwargs)
    return decorated_function

@app.route('/home')
@app.route('/home.html')
def home():
    """Serve the home page"""
    return send_from_directory(str(FRONTEND_DIR), 'home.html')

@app.route('/login', methods=['GET'])
@app.route('/pages/login.html', methods=['GET'])
def login_page():
    """Serve the simple login page"""
    # Get flash messages
    messages = []
    if '_flashes' in session:
        messages = session.get('_flashes', [])
        session.pop('_flashes', None)

    # Read the login page and inject messages if any
    login_file = FRONTEND_DIR / 'pages' / 'simple_login.html'
    with open(login_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Inject flash messages into the page
    if messages:
        message_html = ""
        for category, message in messages:
            message_html += f'<div class="message {category}">{message}</div>'
        content = content.replace('<!-- Simple form that submits directly to backend -->',
                                f'{message_html}\n        <!-- Simple form that submits directly to backend -->')

    return content

@app.route('/signup', methods=['GET'])
@app.route('/pages/signup.html', methods=['GET'])
def signup_page():
    """Serve the simple signup page"""
    # Get flash messages
    messages = []
    if '_flashes' in session:
        messages = session.get('_flashes', [])
        session.pop('_flashes', None)

    # Read the signup page and inject messages if any
    signup_file = FRONTEND_DIR / 'pages' / 'simple_signup.html'
    with open(signup_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Inject flash messages into the page
    if messages:
        message_html = ""
        for category, message in messages:
            message_html += f'<div class="message {category}">{message}</div>'
        content = content.replace('<!-- Simple form that submits directly to backend -->',
                                f'{message_html}\n        <!-- Simple form that submits directly to backend -->')

    return content

# Protected dashboard routes
@app.route('/pages/admin/<path:filename>')
@require_login_for_page
def admin_pages(filename):
    """Serve admin pages - requires login"""
    if session.get('user_role') != 'admin':
        flash('Access denied. Admin privileges required.', 'error')
        return redirect('/')
    return send_from_directory(str(FRONTEND_DIR / 'pages' / 'admin'), filename)

@app.route('/pages/doctor/<path:filename>')
@require_login_for_page
def doctor_pages(filename):
    """Serve doctor pages - requires login"""
    if session.get('user_role') != 'doctor':
        flash('Access denied. Doctor privileges required.', 'error')
        return redirect('/')
    return send_from_directory(str(FRONTEND_DIR / 'pages' / 'doctor'), filename)

@app.route('/pages/Preg/<path:filename>')
@require_login_for_page
def preg_pages(filename):
    """Serve pregnancy care pages - requires login"""
    return send_from_directory(str(FRONTEND_DIR / 'pages' / 'Preg'), filename)

@app.route('/content')
def content_viewer():
    """Serve content viewer page for all users"""
    return send_from_directory(str(FRONTEND_DIR / 'pages'), 'content-viewer.html')

@app.route('/pages/<path:filename>')
def pages(filename):
    """Serve files from the pages directory"""
    # Check if it's a protected page
    if filename.startswith('admin/') or filename.startswith('doctor/') or filename.startswith('Preg/'):
        if not session.get('logged_in'):
            flash('Please login to access this page', 'error')
            return redirect('/login')
    return send_from_directory(str(FRONTEND_DIR / 'pages'), filename)

@app.route('/js/<path:filename>')
def javascript(filename):
    """Serve JavaScript files"""
    return send_from_directory(str(FRONTEND_DIR / 'js'), filename)

@app.route('/css/<path:filename>')
def stylesheets(filename):
    """Serve CSS files"""
    return send_from_directory(str(FRONTEND_DIR / 'css'), filename)

@app.route('/images/<path:filename>')
def images(filename):
    """Serve image files"""
    return send_from_directory(str(FRONTEND_DIR / 'images'), filename)

@app.route('/<path:filename>')
def static_files(filename):
    """Serve any other static files from frontend directory"""
    try:
        return send_from_directory(str(FRONTEND_DIR), filename)
    except:
        # If file not found, return 404 or redirect to home
        return send_from_directory(str(FRONTEND_DIR), 'home.html')

if __name__ == '__main__':
    print("="*80)
    print("🏥 MATERNAL-CHILD HEALTH CARE SYSTEM - UNIFIED SERVER")
    print("="*80)
    print("🚀 Starting unified Flask server...")
    print("🔧 Backend API + Frontend serving on single port")
    print("🌐 Frontend: http://localhost:5000/")
    print("🔐 Login: http://localhost:5000/login")
    print("🔗 API: http://localhost:5000/api/health")
    print("="*80)
    print("✅ Database already initialized")
    print("✅ Default users already created")
    print("🚀 Server starting on http://localhost:5000")
    print("="*80)

    app.run(debug=True, host='0.0.0.0', port=5000)
