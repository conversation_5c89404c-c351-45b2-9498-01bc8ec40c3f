# Maternal-Child Health Care System

A comprehensive web application for maternal and child health care management, featuring pregnancy tracking, baby care monitoring, appointment scheduling, and health insights.

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher

### Installation & Running

1. **Clone or download the project**
2. **Run the main file**
   ```bash
   python run_main.py
   ```
3. **Access the application**
   - Browser will open automatically to: http://localhost:5000
   - Or manually go to: http://localhost:5000

### Default Login Credentials
- **Admin**: <EMAIL> / admin123
- **Doctor**: <EMAIL> / doctor123
- **User**: <EMAIL> / user123

## 📁 Project Structure

```
maternal-child-health-care/
├── backend/
│   ├── app.py              # Main Flask application (ONLY FILE NEEDED TO RUN)
│   ├── requirements.txt    # Python dependencies
│   └── database/          # SQLite database (auto-created)
├── frontend/
│   ├── home.html          # Main homepage
│   ├── pages/             # Application pages
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── assets/            # Images and assets
└── run_main.py            # Main launcher script
```

## ✨ Features

### 🤰 Pregnancy Care
- Pregnancy profile management and tracking
- Weight monitoring with visual charts
- Appointment scheduling
- Health insights and recommendations

### 👶 Baby Care
- Baby profile management
- Sleep and feeding tracking
- Vaccination schedules
- Growth monitoring

### 🏥 Healthcare Management
- Doctor dashboard for patient management
- Admin panel for system administration
- Medical records and appointment system

### 🧘 Wellness Features
- Meditation and mindfulness sessions
- Nutrition guidance and meal planning
- AI health assistant chatbot
- Educational health resources

## 🔧 Technology

- **Backend**: Python Flask (unified server)
- **Frontend**: HTML5, CSS3, JavaScript
- **Database**: SQLite3 (auto-initialized)
- **Authentication**: JWT with role-based access
- **AI**: Google Gemini for health assistant

## 🌐 Single Port Architecture

The application runs both frontend and backend on **port 5000** for simplified deployment:
- Frontend pages served by Flask
- API endpoints at `/api/*`
- Static files (CSS, JS, images) served directly
- No separate frontend server needed

## 🔒 Security

- JWT-based authentication
- Password hashing (SHA-256)
- Role-based access control (Admin, Doctor, User)
- Secure API endpoints with validation

## 📊 API Endpoints

- **Auth**: `/api/login`, `/api/register`
- **Pregnancy**: `/api/pregnancy/*`
- **Baby Care**: `/api/baby/*`
- **Health**: `/api/nutrition`, `/api/meditation`
- **System**: `/api/health`

## 🛠️ Development

The application auto-initializes:
- Database tables and schema
- Default user accounts
- Required directories
- Dependencies installation

## 📝 License

MIT License - see LICENSE file for details.
- **Languages**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: CSS Grid, Flexbox, CSS Variables
- **Icons**: Font Awesome 6.4.0
- **Charts**: Chart.js for data visualization
- **API Client**: Custom JavaScript API client
- **Architecture**: Single Page Application (SPA)

### Database Schema
- **Users**: Authentication and profile management
- **Pregnancy Profiles**: Maternal health tracking
- **Baby Profiles**: Child information and unique IDs
- **Weight Tracking**: Pregnancy weight monitoring
- **Sleep Tracking**: Baby sleep pattern analysis
- **Vaccinations**: Immunization records
- **Appointments**: Medical consultation scheduling
- **Nutrition**: Meal and dietary tracking
- **Meditation**: Wellness session records
- **Chat Messages**: AI assistant conversations

## 📁 Project Structure

```
preg-baby-care/
├── backend/
│   ├── app.py                  # Flask application
│   ├── requirements.txt        # Python dependencies
│   └── database/
│       └── preg_baby_care.db  # SQLite database
├── frontend/
│   ├── home.html              # Landing page
│   ├── js/
│   │   ├── api-client.js      # Backend API client
│   │   ├── script.js          # Main JavaScript
│   │   └── common-header.js   # Shared functionality
│   └── pages/
│       ├── login.html         # Authentication page
│       ├── admin/             # Admin feature pages (5 pages)
│       ├── baby/              # Baby feature pages (5 pages)
│       ├── doctor/            # Doctor feature pages (4 pages)
│       ├── mother/            # Mother feature pages (12 pages)
│       └── Preg/              # Pregnancy-specific pages
├── start_backend.py           # Backend server launcher
├── start_frontend.py          # Frontend server launcher
├── README.md                  # Project documentation
└── .gitignore                # Git ignore rules
```

## 🔧 Installation & Setup

### Prerequisites
- **Python 3.7+**: For backend server
- **pip**: Python package manager
- **Modern web browser**: Chrome, Firefox, Safari, Edge

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd preg-baby-care
   ```

2. **Start the backend server**
   ```bash
   python start_backend.py
   ```
   This will:
   - Install required Python packages
   - Initialize SQLite database
   - Start Flask server on http://localhost:5000

3. **Start the frontend server** (in a new terminal)
   ```bash
   python start_frontend.py
   ```
   This will:
   - Start HTTP server on http://localhost:3000
   - Automatically open browser to home page

4. **Access the application**
   - **Home Page**: http://localhost:3000/home.html
   - **Login Page**: http://localhost:3000/pages/login.html
   - **API Health**: http://localhost:5000/api/health

### Manual Setup

1. **Backend Setup**
   ```bash
   cd backend
   pip install -r requirements.txt
   python app.py
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   python -m http.server 3000
   ```

## 🔌 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `GET /api/health` - Health check

### Pregnancy Features
- `GET/POST /api/pregnancy/profile` - Pregnancy profile management
- `GET/POST /api/weight/track` - Weight tracking

### Baby Features
- `GET/POST /api/baby/profile` - Baby profile management
- `GET/POST /api/baby/sleep` - Sleep tracking

### Health Features
- `GET/POST /api/vaccinations` - Vaccination records
- `GET/POST /api/appointments` - Appointment scheduling
- `GET/POST /api/nutrition` - Nutrition tracking
- `GET/POST /api/meditation` - Meditation sessions
- `GET/POST /api/chat` - AI chatbot interactions

## 📱 Usage Guide

### Getting Started
1. **Registration**: Create a new account on the login page
2. **Login**: Sign in with your credentials
3. **Profile Setup**: Complete your profile information
4. **Feature Access**: Navigate to pregnancy care or baby care sections

### Key Features
- **Pregnancy Care Hub**: Access nutrition, meditation, weight tracking, and appointments
- **Baby Care Center**: Manage baby profiles, sleep tracking, and vaccinations
- **Weight Tracking**: Interactive charts with real-time data from backend
- **Unique Baby ID**: Generate and manage digital baby identification
- **AI Assistant**: Chat with AI for pregnancy and baby care guidance

### API Integration
- **Real-time Data**: All forms save data to SQLite database
- **Authentication**: JWT tokens for secure API access
- **Error Handling**: Comprehensive error messages and validation
- **Offline Support**: Local storage fallback for temporary data

## 🌐 Deployment

### Development
- **Backend**: Flask development server (localhost:5000)
- **Frontend**: Python HTTP server (localhost:3000)
- **Database**: SQLite file-based database

### Production
- **Backend**: Deploy Flask app to Heroku, AWS, or DigitalOcean
- **Frontend**: Deploy to Netlify, Vercel, or static hosting
- **Database**: Upgrade to PostgreSQL or MySQL for production

### Environment Variables
```bash
# Backend configuration
DATABASE_PATH=database/preg_baby_care.db
JWT_SECRET_KEY=your-secret-key-here
FLASK_ENV=production
```

## 🤖 Google AI Integration

### AI-Powered Chatbot
The system now includes an advanced AI chatbot powered by Google's Gemini Pro model:

- **API Key**: `AIzaSyCicw6WxXq5-TtRmwRLwlnmSVArF4JH1KA`
- **Model**: Google Gemini Pro
- **Specialization**: Maternal and child health care
- **Features**:
  - Natural language understanding
  - Medical knowledge base
  - Contextual responses
  - Voice input/output support
  - Fallback responses for offline mode

### Chatbot Capabilities
- **Pregnancy Advice**: Nutrition, exercise, symptoms, and general care
- **Baby Care**: Feeding, sleep, development, and health concerns
- **Medical Guidance**: When to consult doctors, emergency signs
- **Vaccination Info**: Schedule reminders and side effect management
- **Nutrition Planning**: Meal suggestions and dietary recommendations

### Voice Assistant Features
- **Speech Recognition**: Hands-free interaction using Web Speech API
- **Text-to-Speech**: Audio responses for accessibility
- **Voice Commands**: Natural language voice input
- **Multi-language Support**: Configurable language settings

### Integration Details
- **Backend Endpoint**: `/api/chat`
- **Request Format**: JSON with message content
- **Response Format**: AI-generated text with metadata
- **Error Handling**: Graceful fallback to offline responses
- **Rate Limiting**: Configured for optimal performance

### Usage Example
```javascript
// Send message to AI chatbot
const response = await window.apiClient.sendChatMessage("How much weight should I gain during pregnancy?");
console.log(response.data.response);
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: SHA-256 password encryption
- **CORS Protection**: Cross-origin request security
- **Input Validation**: Server-side and client-side validation
- **SQL Injection Prevention**: Parameterized queries
- **Session Management**: Secure token storage and expiration

## 🎯 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+

## 📊 Performance

- **Backend**: Fast SQLite queries with optimized schema
- **Frontend**: Lightweight JavaScript with efficient API calls
- **Database**: Indexed tables for quick data retrieval
- **Caching**: Browser caching for static assets
- **Compression**: Minified CSS and JavaScript

## 🧪 Testing

### Backend Testing
```bash
cd backend
python -m pytest tests/
```

### Frontend Testing
- Manual testing with browser developer tools
- API endpoint testing with Postman or curl
- Cross-browser compatibility testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check API documentation at `/api/health`
- Review browser console for frontend errors
- Check Flask logs for backend issues

## 🔄 Version History

- **v2.0.0**: Full-stack implementation with Flask backend and SQLite database
- **v1.2.0**: Enhanced pregnancy tracking and weight monitoring
- **v1.1.0**: Added baby care features and improved UI
- **v1.0.0**: Initial frontend-only release

---

**Preg and Baby Care** - Complete full-stack solution supporting families through every step of the journey! 👶💕🚀
