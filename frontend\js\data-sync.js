/**
 * Data Synchronization Service
 * Ensures all frontend features work seamlessly with backend APIs
 */

class DataSyncService {
    constructor() {
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.syncInterval = null;
        this.offlineData = this.getOfflineData();
        
        this.initializeEventListeners();
        this.startPeriodicSync();
    }

    // Initialize event listeners for online/offline status
    initializeEventListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // Sync data before page unload
        window.addEventListener('beforeunload', () => {
            this.saveOfflineData();
        });
    }

    // Start periodic synchronization
    startPeriodicSync() {
        this.syncInterval = setInterval(() => {
            if (this.isOnline) {
                this.syncAllData();
            }
        }, 30000); // Sync every 30 seconds
    }

    // Stop periodic synchronization
    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }

    // Get offline data from localStorage
    getOfflineData() {
        try {
            return JSON.parse(localStorage.getItem('offline_data') || '{}');
        } catch (error) {
            console.error('Error loading offline data:', error);
            return {};
        }
    }

    // Save offline data to localStorage
    saveOfflineData() {
        try {
            localStorage.setItem('offline_data', JSON.stringify(this.offlineData));
        } catch (error) {
            console.error('Error saving offline data:', error);
        }
    }

    // Add data to sync queue
    addToSyncQueue(action, data) {
        const syncItem = {
            id: Date.now() + Math.random(),
            action,
            data,
            timestamp: new Date().toISOString(),
            retries: 0
        };

        this.syncQueue.push(syncItem);
        
        if (this.isOnline) {
            this.processSyncQueue();
        } else {
            this.saveToOfflineStorage(syncItem);
        }
    }

    // Save data to offline storage
    saveToOfflineStorage(syncItem) {
        if (!this.offlineData[syncItem.action]) {
            this.offlineData[syncItem.action] = [];
        }
        this.offlineData[syncItem.action].push(syncItem);
        this.saveOfflineData();
    }

    // Process sync queue
    async processSyncQueue() {
        while (this.syncQueue.length > 0 && this.isOnline) {
            const item = this.syncQueue.shift();
            try {
                await this.syncItem(item);
            } catch (error) {
                console.error('Sync failed for item:', item, error);
                item.retries++;
                
                if (item.retries < 3) {
                    this.syncQueue.push(item);
                } else {
                    console.error('Max retries reached for sync item:', item);
                }
            }
        }
    }

    // Sync individual item
    async syncItem(item) {
        switch (item.action) {
            case 'weight_record':
                return await window.apiClient.addWeightRecord(item.data);
            case 'nutrition_record':
                return await window.apiClient.addNutritionRecord(item.data);
            case 'sleep_record':
                return await window.apiClient.addSleepRecord(item.data);
            case 'meditation_session':
                return await window.apiClient.addMeditationSession(item.data);
            case 'chat_message':
                return await window.apiClient.sendChatMessage(item.data.message, item.data.type);
            case 'appointment':
                return await window.apiClient.createAppointment(item.data);
            case 'vaccination':
                return await window.apiClient.addVaccination(item.data);
            default:
                console.warn('Unknown sync action:', item.action);
        }
    }

    // Sync offline data when coming back online
    async syncOfflineData() {
        for (const [action, items] of Object.entries(this.offlineData)) {
            for (const item of items) {
                this.syncQueue.push(item);
            }
        }

        this.offlineData = {};
        this.saveOfflineData();
        await this.processSyncQueue();
    }

    // Sync all data from server
    async syncAllData() {
        try {
            // Update local cache with latest data from server
            await this.syncUserProfile();
            await this.syncPregnancyData();
            await this.syncBabyData();
            await this.syncAppointments();
            await this.syncNutritionData();
        } catch (error) {
            console.error('Error syncing all data:', error);
        }
    }

    // Sync user profile
    async syncUserProfile() {
        try {
            const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
            if (userData.id) {
                // In a real app, fetch updated user data
                // const response = await window.apiClient.getUserProfile();
                // localStorage.setItem('user_data', JSON.stringify(response.data));
            }
        } catch (error) {
            console.error('Error syncing user profile:', error);
        }
    }

    // Sync pregnancy data
    async syncPregnancyData() {
        try {
            const response = await window.apiClient.getPregnancyProfile();
            if (response.success) {
                localStorage.setItem('pregnancy_profile', JSON.stringify(response.data));
            }

            const weightResponse = await window.apiClient.getWeightRecords();
            if (weightResponse.success) {
                localStorage.setItem('weight_records', JSON.stringify(weightResponse.data));
            }
        } catch (error) {
            console.error('Error syncing pregnancy data:', error);
        }
    }

    // Sync baby data
    async syncBabyData() {
        try {
            const response = await window.apiClient.getBabyProfiles();
            if (response.success) {
                localStorage.setItem('baby_profiles', JSON.stringify(response.data));
                
                // Sync sleep data for each baby
                for (const baby of response.data) {
                    const sleepResponse = await window.apiClient.getSleepRecords(baby.id);
                    if (sleepResponse.success) {
                        localStorage.setItem(`sleep_records_${baby.id}`, JSON.stringify(sleepResponse.data));
                    }
                }
            }

            const vaccinationResponse = await window.apiClient.getVaccinations();
            if (vaccinationResponse.success) {
                localStorage.setItem('vaccinations', JSON.stringify(vaccinationResponse.data));
            }
        } catch (error) {
            console.error('Error syncing baby data:', error);
        }
    }

    // Sync appointments
    async syncAppointments() {
        try {
            const response = await window.apiClient.getAppointments();
            if (response.success) {
                localStorage.setItem('appointments', JSON.stringify(response.data));
            }
        } catch (error) {
            console.error('Error syncing appointments:', error);
        }
    }

    // Sync nutrition data
    async syncNutritionData() {
        try {
            const response = await window.apiClient.getNutritionRecords();
            if (response.success) {
                localStorage.setItem('nutrition_records', JSON.stringify(response.data));
            }

            const meditationResponse = await window.apiClient.getMeditationSessions();
            if (meditationResponse.success) {
                localStorage.setItem('meditation_sessions', JSON.stringify(meditationResponse.data));
            }
        } catch (error) {
            console.error('Error syncing nutrition data:', error);
        }
    }

    // Get cached data with fallback
    getCachedData(key, fallback = []) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : fallback;
        } catch (error) {
            console.error(`Error getting cached data for ${key}:`, error);
            return fallback;
        }
    }

    // Update cached data
    updateCachedData(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error(`Error updating cached data for ${key}:`, error);
        }
    }

    // Clear all cached data
    clearCache() {
        const keysToRemove = [
            'pregnancy_profile',
            'weight_records',
            'baby_profiles',
            'vaccinations',
            'appointments',
            'nutrition_records',
            'meditation_sessions',
            'offline_data'
        ];

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });

        // Clear baby-specific sleep records
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('sleep_records_')) {
                localStorage.removeItem(key);
            }
        }
    }

    // Force sync all data
    async forceSyncAll() {
        this.clearCache();
        await this.syncAllData();
        return true;
    }
}

// Create global instance
window.dataSyncService = new DataSyncService();

// Helper functions for easy access
window.syncData = (action, data) => {
    window.dataSyncService.addToSyncQueue(action, data);
};

window.getCachedData = (key, fallback) => {
    return window.dataSyncService.getCachedData(key, fallback);
};

window.updateCachedData = (key, data) => {
    window.dataSyncService.updateCachedData(key, data);
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataSyncService;
}
