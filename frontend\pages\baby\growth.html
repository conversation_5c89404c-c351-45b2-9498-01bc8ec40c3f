<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Growth Tracking - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="../mother/profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">👶 MCHS - Growth Tracking</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Growth Tracking</h1>
                <div class="page-actions">
                    <button onclick="addMeasurement()" class="btn btn-primary">Add Measurement</button>
                    <button onclick="exportGrowthChart()" class="btn btn-outline">Export Chart</button>
                </div>
            </div>

            <!-- Baby Selection -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="selected-baby">Select Baby</label>
                                <select id="selected-baby" class="form-control" onchange="loadGrowthData()">
                                    <option value="">Select a baby...</option>
                                    <!-- Babies will be loaded here -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="baby-info" id="baby-info">
                                <!-- Baby info will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Growth Content -->
            <div id="growth-content" style="display: none;">
                <!-- Growth Charts -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Weight Chart</h3>
                                <div class="chart-controls">
                                    <select id="weight-percentile" class="form-control form-control-sm" onchange="updateWeightChart()">
                                        <option value="show">Show Percentiles</option>
                                        <option value="hide">Hide Percentiles</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="weight-chart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Length/Height Chart</h3>
                                <div class="chart-controls">
                                    <select id="length-percentile" class="form-control form-control-sm" onchange="updateLengthChart()">
                                        <option value="show">Show Percentiles</option>
                                        <option value="hide">Hide Percentiles</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="length-chart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Head Circumference Chart</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="head-chart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Growth Summary</h3>
                            </div>
                            <div class="card-body">
                                <div class="growth-summary" id="growth-summary">
                                    <!-- Growth summary will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Growth History -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3>Growth Measurements History</h3>
                        <div class="table-filters">
                            <select id="measurement-filter" class="form-control" onchange="filterMeasurements()">
                                <option value="">All Measurements</option>
                                <option value="weight">Weight Only</option>
                                <option value="length">Length Only</option>
                                <option value="head_circumference">Head Circumference Only</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Age</th>
                                        <th>Weight (kg)</th>
                                        <th>Length (cm)</th>
                                        <th>Head Circumference (cm)</th>
                                        <th>Percentiles</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="measurements-table">
                                    <!-- Measurements will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Growth Milestones -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h3>Growth Milestones</h3>
                    </div>
                    <div class="card-body">
                        <div class="milestones-grid" id="growth-milestones">
                            <!-- Growth milestones will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Measurement Modal -->
    <div id="measurement-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Growth Measurement</h3>
                <span class="close" onclick="closeMeasurementModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="measurement-form">
                    <input type="hidden" id="baby_id" name="baby_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="measurement_date">Measurement Date</label>
                                <input type="date" id="measurement_date" name="measurement_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="baby_age">Baby's Age</label>
                                <input type="text" id="baby_age" class="form-control" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="weight">Weight (kg)</label>
                                <input type="number" id="weight" name="weight" class="form-control" step="0.01" min="0">
                                <small class="form-text text-muted">Leave empty if not measured</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="length">Length/Height (cm)</label>
                                <input type="number" id="length" name="length" class="form-control" step="0.1" min="0">
                                <small class="form-text text-muted">Leave empty if not measured</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="head_circumference">Head Circumference (cm)</label>
                                <input type="number" id="head_circumference" name="head_circumference" class="form-control" step="0.1" min="0">
                                <small class="form-text text-muted">Leave empty if not measured</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="measured_by">Measured By</label>
                        <input type="text" id="measured_by" name="measured_by" class="form-control" placeholder="e.g., Dr. Smith, Home measurement">
                    </div>

                    <div class="form-group">
                        <label for="measurement_notes">Notes</label>
                        <textarea id="measurement_notes" name="notes" class="form-control" rows="3" placeholder="Any observations or notes about the measurement"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Add Measurement</button>
                        <button type="button" onclick="closeMeasurementModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentBaby = null;
        let growthData = [];
        let weightChart = null;
        let lengthChart = null;
        let headChart = null;

        // WHO Growth Standards (simplified percentiles)
        const growthStandards = {
            weight: {
                male: {
                    3: [3.2, 4.4, 5.0, 5.6, 6.4],   // 3rd, 15th, 50th, 85th, 97th percentiles
                    6: [6.4, 7.5, 8.0, 8.8, 9.8],
                    12: [8.4, 9.6, 10.2, 11.3, 12.8],
                    24: [10.5, 12.2, 13.0, 14.3, 16.2]
                },
                female: {
                    3: [2.9, 4.0, 4.6, 5.2, 6.0],
                    6: [5.7, 6.8, 7.3, 8.2, 9.3],
                    12: [7.7, 8.9, 9.5, 10.6, 12.1],
                    24: [9.7, 11.2, 12.0, 13.3, 15.1]
                }
            }
        };

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBabies();
            setDefaultDate();
        });

        function setDefaultDate() {
            document.getElementById('measurement_date').value = new Date().toISOString().split('T')[0];
        }

        async function loadBabies() {
            try {
                const response = await apiCall('/mother/babies', 'GET');
                if (response.success) {
                    const select = document.getElementById('selected-baby');
                    select.innerHTML = '<option value="">Select a baby...</option>';
                    
                    response.data.forEach(baby => {
                        const option = document.createElement('option');
                        option.value = baby.id;
                        option.textContent = baby.baby_name;
                        select.appendChild(option);
                    });

                    // Auto-select if only one baby
                    if (response.data.length === 1) {
                        select.value = response.data[0].id;
                        loadGrowthData();
                    }
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        async function loadGrowthData() {
            const babyId = document.getElementById('selected-baby').value;
            if (!babyId) {
                document.getElementById('growth-content').style.display = 'none';
                return;
            }

            try {
                const [babyResponse, growthResponse] = await Promise.all([
                    apiCall(`/mother/babies/${babyId}`, 'GET'),
                    apiCall(`/mother/babies/${babyId}/growth`, 'GET')
                ]);

                if (babyResponse.success && growthResponse.success) {
                    currentBaby = babyResponse.data;
                    growthData = growthResponse.data;
                    
                    document.getElementById('baby_id').value = babyId;
                    
                    displayBabyInfo();
                    displayGrowthData();
                    document.getElementById('growth-content').style.display = 'block';
                }
            } catch (error) {
                console.error('Error loading growth data:', error);
            }
        }

        function displayBabyInfo() {
            const container = document.getElementById('baby-info');
            const age = calculateAge(currentBaby.date_of_birth);
            
            container.innerHTML = `
                <div class="baby-info-display">
                    <h4>${currentBaby.baby_name}</h4>
                    <p><strong>Age:</strong> ${age}</p>
                    <p><strong>Gender:</strong> ${currentBaby.gender}</p>
                    <p><strong>Birth Weight:</strong> ${currentBaby.birth_weight} kg</p>
                    <p><strong>Birth Length:</strong> ${currentBaby.birth_length} cm</p>
                </div>
            `;
        }

        function displayGrowthData() {
            createWeightChart();
            createLengthChart();
            createHeadChart();
            displayMeasurementsTable();
            displayGrowthSummary();
            displayGrowthMilestones();
        }

        function createWeightChart() {
            const ctx = document.getElementById('weight-chart').getContext('2d');
            
            if (weightChart) {
                weightChart.destroy();
            }

            const weightData = growthData.filter(d => d.weight).map(d => ({
                x: calculateAgeInMonths(currentBaby.date_of_birth, d.measurement_date),
                y: d.weight
            }));

            weightChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Weight',
                        data: weightData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Age (months)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Weight (kg)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Weight Growth Chart'
                        }
                    }
                }
            });
        }

        function createLengthChart() {
            const ctx = document.getElementById('length-chart').getContext('2d');
            
            if (lengthChart) {
                lengthChart.destroy();
            }

            const lengthData = growthData.filter(d => d.length).map(d => ({
                x: calculateAgeInMonths(currentBaby.date_of_birth, d.measurement_date),
                y: d.length
            }));

            lengthChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Length/Height',
                        data: lengthData,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Age (months)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Length/Height (cm)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Length/Height Growth Chart'
                        }
                    }
                }
            });
        }

        function createHeadChart() {
            const ctx = document.getElementById('head-chart').getContext('2d');
            
            if (headChart) {
                headChart.destroy();
            }

            const headData = growthData.filter(d => d.head_circumference).map(d => ({
                x: calculateAgeInMonths(currentBaby.date_of_birth, d.measurement_date),
                y: d.head_circumference
            }));

            headChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Head Circumference',
                        data: headData,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Age (months)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Head Circumference (cm)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Head Circumference Growth Chart'
                        }
                    }
                }
            });
        }

        function displayMeasurementsTable() {
            const tbody = document.getElementById('measurements-table');
            
            if (growthData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-secondary">No measurements recorded yet.</td></tr>';
                return;
            }

            tbody.innerHTML = growthData
                .sort((a, b) => new Date(b.measurement_date) - new Date(a.measurement_date))
                .map(measurement => {
                    const age = calculateAge(currentBaby.date_of_birth, measurement.measurement_date);
                    const percentiles = calculatePercentiles(measurement);
                    
                    return `
                        <tr>
                            <td>${formatDate(measurement.measurement_date)}</td>
                            <td>${age}</td>
                            <td>${measurement.weight || '-'}</td>
                            <td>${measurement.length || '-'}</td>
                            <td>${measurement.head_circumference || '-'}</td>
                            <td>${percentiles}</td>
                            <td>${measurement.notes || '-'}</td>
                            <td>
                                <button onclick="editMeasurement('${measurement.id}')" class="btn btn-sm btn-outline">Edit</button>
                                <button onclick="deleteMeasurement('${measurement.id}')" class="btn btn-sm btn-danger">Delete</button>
                            </td>
                        </tr>
                    `;
                }).join('');
        }

        function displayGrowthSummary() {
            const container = document.getElementById('growth-summary');
            
            if (growthData.length === 0) {
                container.innerHTML = '<p class="text-secondary">No measurements available for summary.</p>';
                return;
            }

            const latest = growthData.sort((a, b) => new Date(b.measurement_date) - new Date(a.measurement_date))[0];
            const age = calculateAgeInMonths(currentBaby.date_of_birth, latest.measurement_date);
            
            container.innerHTML = `
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">Latest Weight</span>
                        <span class="stat-value">${latest.weight || 'Not recorded'} kg</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Latest Length</span>
                        <span class="stat-value">${latest.length || 'Not recorded'} cm</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Latest Head Circumference</span>
                        <span class="stat-value">${latest.head_circumference || 'Not recorded'} cm</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Growth Rate</span>
                        <span class="stat-value">${calculateGrowthRate()}</span>
                    </div>
                </div>
                
                <div class="growth-alerts">
                    ${generateGrowthAlerts()}
                </div>
            `;
        }

        function displayGrowthMilestones() {
            const container = document.getElementById('growth-milestones');
            const milestones = [
                { age: 3, description: 'Birth weight typically doubles' },
                { age: 6, description: 'Birth weight typically triples' },
                { age: 12, description: 'Birth length increases by 50%' },
                { age: 24, description: 'Birth weight typically quadruples' }
            ];

            container.innerHTML = milestones.map(milestone => {
                const achieved = checkMilestoneAchieved(milestone);
                return `
                    <div class="milestone-item ${achieved ? 'achieved' : 'pending'}">
                        <div class="milestone-age">${milestone.age} months</div>
                        <div class="milestone-description">${milestone.description}</div>
                        <div class="milestone-status">
                            ${achieved ? 
                                '<span class="badge badge-success">Achieved</span>' : 
                                '<span class="badge badge-secondary">Pending</span>'
                            }
                        </div>
                    </div>
                `;
            }).join('');
        }

        function addMeasurement() {
            document.getElementById('measurement-modal').style.display = 'block';
            updateBabyAge();
        }

        function closeMeasurementModal() {
            document.getElementById('measurement-modal').style.display = 'none';
            document.getElementById('measurement-form').reset();
        }

        function updateBabyAge() {
            const measurementDate = document.getElementById('measurement_date').value;
            if (measurementDate && currentBaby) {
                const age = calculateAge(currentBaby.date_of_birth, measurementDate);
                document.getElementById('baby_age').value = age;
            }
        }

        // Event listener for date change
        document.getElementById('measurement_date').addEventListener('change', updateBabyAge);

        // Form submission handler
        document.getElementById('measurement-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                // Remove empty values
                Object.keys(data).forEach(key => {
                    if (data[key] === '' || data[key] === null) {
                        delete data[key];
                    }
                });

                const response = await apiCall('/mother/babies/growth', 'POST', data);
                if (response.success) {
                    showNotification('Measurement added successfully!', 'success');
                    closeMeasurementModal();
                    loadGrowthData();
                }
            } catch (error) {
                showNotification('Error adding measurement: ' + error.message, 'error');
            }
        });

        // Utility functions
        function calculateAge(birthDate, measurementDate = null) {
            const birth = new Date(birthDate);
            const measure = measurementDate ? new Date(measurementDate) : new Date();
            const diffTime = Math.abs(measure - birth);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 30) {
                return `${diffDays} days`;
            } else if (diffDays < 365) {
                const months = Math.floor(diffDays / 30);
                const remainingDays = diffDays % 30;
                return `${months} months, ${remainingDays} days`;
            } else {
                const years = Math.floor(diffDays / 365);
                const remainingDays = diffDays % 365;
                const months = Math.floor(remainingDays / 30);
                return `${years} years, ${months} months`;
            }
        }

        function calculateAgeInMonths(birthDate, measurementDate) {
            const birth = new Date(birthDate);
            const measure = new Date(measurementDate);
            const diffTime = measure - birth;
            return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30.44));
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function calculatePercentiles(measurement) {
            // Simplified percentile calculation
            // In a real application, this would use WHO growth standards
            return 'Normal range';
        }

        function calculateGrowthRate() {
            if (growthData.length < 2) return 'Insufficient data';
            
            const sorted = growthData.sort((a, b) => new Date(a.measurement_date) - new Date(b.measurement_date));
            const first = sorted[0];
            const last = sorted[sorted.length - 1];
            
            if (first.weight && last.weight) {
                const weightGain = (last.weight - first.weight).toFixed(2);
                return `+${weightGain} kg`;
            }
            
            return 'Normal';
        }

        function generateGrowthAlerts() {
            // Generate growth alerts based on patterns
            return '<div class="alert alert-info">Growth is progressing normally. Continue regular checkups.</div>';
        }

        function checkMilestoneAchieved(milestone) {
            // Check if milestone is achieved based on current data
            return false; // Simplified for demo
        }

        // Placeholder functions
        function updateWeightChart() {
            // Update weight chart with/without percentiles
        }

        function updateLengthChart() {
            // Update length chart with/without percentiles
        }

        function filterMeasurements() {
            // Filter measurements table
        }

        function exportGrowthChart() {
            // Export growth charts
            showNotification('Growth chart exported successfully!', 'success');
        }

        async function editMeasurement(measurementId) {
            // Edit measurement
        }

        async function deleteMeasurement(measurementId) {
            if (confirm('Are you sure you want to delete this measurement?')) {
                try {
                    const response = await apiCall(`/mother/babies/growth/${measurementId}`, 'DELETE');
                    if (response.success) {
                        showNotification('Measurement deleted successfully!', 'success');
                        loadGrowthData();
                    }
                } catch (error) {
                    showNotification('Error deleting measurement: ' + error.message, 'error');
                }
            }
        }
    </script>
</body>
</html>
