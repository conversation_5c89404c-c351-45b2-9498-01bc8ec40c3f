<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sleep Tracking - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="baby-care.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Baby Care</a>
                    <div class="navbar-brand">😴 MCHS - Baby Sleep Tracking</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Baby Sleep Tracking</h1>
                <p class="page-description">Monitor your baby's sleep patterns and establish healthy sleep routines</p>
                <div class="baby-selector">
                    <select id="baby-select" class="form-control" onchange="loadBabyData()">
                        <option value="">Select Baby</option>
                    </select>
                </div>
            </div>

            <!-- Current Sleep Status -->
            <div class="sleep-status-card mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="current-status">
                            <div class="status-icon" id="sleep-status-icon">😴</div>
                            <div class="status-info">
                                <h3 id="sleep-status-text">Currently Sleeping</h3>
                                <p id="sleep-duration">Sleep duration: 2h 15m</p>
                                <div class="status-actions">
                                    <button id="sleep-toggle-btn" onclick="toggleSleep()" class="btn btn-primary">
                                        Wake Up
                                    </button>
                                    <button onclick="addSleepNote()" class="btn btn-outline">Add Note</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="today-summary">
                            <h4>Today's Sleep Summary</h4>
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <div class="stat-number" id="total-sleep-today">8h 30m</div>
                                    <div class="stat-label">Total Sleep</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="naps-today">4</div>
                                    <div class="stat-label">Naps</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="longest-sleep">3h 45m</div>
                                    <div class="stat-label">Longest Sleep</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="wake-ups">2</div>
                                    <div class="stat-label">Night Wake-ups</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Sleep Log -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between items-center">
                                <h3>📊 Sleep Log</h3>
                                <div class="log-controls">
                                    <button onclick="addManualSleep()" class="btn btn-outline">+ Add Sleep</button>
                                    <select id="log-period" class="form-control" onchange="loadSleepLog()">
                                        <option value="today">Today</option>
                                        <option value="week">This Week</option>
                                        <option value="month">This Month</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="sleep-log" class="sleep-log">
                                <!-- Sleep entries will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Sleep Patterns Chart -->
                    <div class="card">
                        <div class="card-header">
                            <h3>📈 Sleep Patterns</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-controls mb-3">
                                <div class="btn-group">
                                    <button class="btn btn-outline active" onclick="showChart('daily')">Daily</button>
                                    <button class="btn btn-outline" onclick="showChart('weekly')">Weekly</button>
                                    <button class="btn btn-outline" onclick="showChart('monthly')">Monthly</button>
                                </div>
                            </div>
                            <canvas id="sleep-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Sleep Guidelines -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3>💡 Sleep Guidelines</h3>
                        </div>
                        <div class="card-body">
                            <div id="sleep-guidelines" class="sleep-guidelines">
                                <!-- Guidelines will be loaded based on baby's age -->
                            </div>
                        </div>
                    </div>

                    <!-- Sleep Environment -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3>🛏️ Sleep Environment</h3>
                        </div>
                        <div class="card-body">
                            <div class="environment-checklist">
                                <div class="checklist-item">
                                    <input type="checkbox" id="room-temp">
                                    <label for="room-temp">Room temperature 68-70°F</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="checkbox" id="dark-room">
                                    <label for="dark-room">Dark room</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="checkbox" id="quiet-environment">
                                    <label for="quiet-environment">Quiet environment</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="checkbox" id="safe-crib">
                                    <label for="safe-crib">Safe crib setup</label>
                                </div>
                                <div class="checklist-item">
                                    <input type="checkbox" id="comfortable-clothing">
                                    <label for="comfortable-clothing">Comfortable sleepwear</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sleep Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h3>💤 Sleep Tips</h3>
                        </div>
                        <div class="card-body">
                            <div class="sleep-tips">
                                <div class="tip-item">
                                    <div class="tip-icon">🌙</div>
                                    <div class="tip-content">
                                        <h5>Establish a Routine</h5>
                                        <p>Create consistent bedtime rituals to signal sleep time.</p>
                                    </div>
                                </div>
                                <div class="tip-item">
                                    <div class="tip-icon">☀️</div>
                                    <div class="tip-content">
                                        <h5>Day/Night Distinction</h5>
                                        <p>Keep days bright and active, nights dark and calm.</p>
                                    </div>
                                </div>
                                <div class="tip-item">
                                    <div class="tip-icon">👶</div>
                                    <div class="tip-content">
                                        <h5>Watch Sleep Cues</h5>
                                        <p>Look for yawning, rubbing eyes, or fussiness.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Sleep Modal -->
    <div id="sleep-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="sleep-modal-title">Add Sleep Entry</h3>
                <span class="close" onclick="closeSleepModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="sleep-form">
                    <input type="hidden" id="sleep-entry-id">
                    
                    <div class="form-group">
                        <label for="sleep-type">Sleep Type</label>
                        <select id="sleep-type" class="form-control" required>
                            <option value="nap">Nap</option>
                            <option value="night">Night Sleep</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sleep-start-date">Start Date</label>
                                <input type="date" id="sleep-start-date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sleep-start-time">Start Time</label>
                                <input type="time" id="sleep-start-time" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sleep-end-date">End Date</label>
                                <input type="date" id="sleep-end-date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="sleep-end-time">End Time</label>
                                <input type="time" id="sleep-end-time" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sleep-quality">Sleep Quality</label>
                        <select id="sleep-quality" class="form-control">
                            <option value="excellent">Excellent - Peaceful, no interruptions</option>
                            <option value="good">Good - Minor stirring</option>
                            <option value="fair">Fair - Some restlessness</option>
                            <option value="poor">Poor - Frequent wake-ups</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sleep-location">Sleep Location</label>
                        <select id="sleep-location" class="form-control">
                            <option value="crib">Crib</option>
                            <option value="bassinet">Bassinet</option>
                            <option value="parent-bed">Parent's Bed</option>
                            <option value="stroller">Stroller</option>
                            <option value="car-seat">Car Seat</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sleep-notes">Notes</label>
                        <textarea id="sleep-notes" class="form-control" rows="3" placeholder="Any observations about the sleep..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" onclick="closeSleepModal()" class="btn btn-outline">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Sleep Entry</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sleep Note Modal -->
    <div id="note-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Sleep Note</h3>
                <span class="close" onclick="closeNoteModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="note-form">
                    <div class="form-group">
                        <label for="note-text">Note</label>
                        <textarea id="note-text" class="form-control" rows="4" placeholder="Add a note about current sleep..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="closeNoteModal()" class="btn btn-outline">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Note</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let currentBaby = null;
        let sleepData = [];
        let currentSleepSession = null;
        let sleepChart = null;

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBabies();
            updateCurrentTime();
            setInterval(updateCurrentTime, 60000); // Update every minute
        });

        async function loadBabies() {
            try {
                const response = await apiCall('/babies', 'GET');
                if (response.success) {
                    populateBabySelector(response.data);
                    if (response.data.length > 0) {
                        currentBaby = response.data[0];
                        document.getElementById('baby-select').value = currentBaby.id;
                        loadBabyData();
                    }
                } else {
                    // Demo baby
                    const demoBaby = { id: 'demo', name: 'Baby Smith', birth_date: '2024-01-15' };
                    populateBabySelector([demoBaby]);
                    currentBaby = demoBaby;
                    loadBabyData();
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        function populateBabySelector(babies) {
            const select = document.getElementById('baby-select');
            select.innerHTML = '<option value="">Select Baby</option>';
            
            babies.forEach(baby => {
                const option = document.createElement('option');
                option.value = baby.id;
                option.textContent = baby.name;
                select.appendChild(option);
            });
        }

        async function loadBabyData() {
            const babyId = document.getElementById('baby-select').value;
            if (!babyId) return;

            currentBaby = { id: babyId };
            
            try {
                await Promise.all([
                    loadSleepData(),
                    loadCurrentSleepStatus(),
                    loadSleepGuidelines(),
                    loadTodaySummary()
                ]);
            } catch (error) {
                console.error('Error loading baby data:', error);
            }
        }

        async function loadSleepData() {
            try {
                const response = await apiCall(`/baby/${currentBaby.id}/sleep`, 'GET');
                if (response.success) {
                    sleepData = response.data;
                } else {
                    loadDemoSleepData();
                }
                loadSleepLog();
                loadSleepChart();
            } catch (error) {
                console.error('Error loading sleep data:', error);
                loadDemoSleepData();
            }
        }

        function loadDemoSleepData() {
            const today = new Date();
            sleepData = [
                {
                    id: '1',
                    type: 'night',
                    start_time: new Date(today.getTime() - 8 * 60 * 60 * 1000).toISOString(),
                    end_time: new Date(today.getTime() - 2 * 60 * 60 * 1000).toISOString(),
                    quality: 'good',
                    location: 'crib',
                    notes: 'Slept well through the night'
                },
                {
                    id: '2',
                    type: 'nap',
                    start_time: new Date(today.getTime() - 4 * 60 * 60 * 1000).toISOString(),
                    end_time: new Date(today.getTime() - 2.5 * 60 * 60 * 1000).toISOString(),
                    quality: 'excellent',
                    location: 'crib',
                    notes: 'Peaceful nap'
                }
            ];
            loadSleepLog();
            loadSleepChart();
        }

        async function loadCurrentSleepStatus() {
            try {
                const response = await apiCall(`/baby/${currentBaby.id}/sleep/current`, 'GET');
                if (response.success && response.data.is_sleeping) {
                    currentSleepSession = response.data;
                    updateSleepStatus(true);
                } else {
                    updateSleepStatus(false);
                }
            } catch (error) {
                console.error('Error loading sleep status:', error);
                updateSleepStatus(false);
            }
        }

        function updateSleepStatus(isSleeping) {
            const statusIcon = document.getElementById('sleep-status-icon');
            const statusText = document.getElementById('sleep-status-text');
            const durationText = document.getElementById('sleep-duration');
            const toggleBtn = document.getElementById('sleep-toggle-btn');

            if (isSleeping) {
                statusIcon.textContent = '😴';
                statusText.textContent = 'Currently Sleeping';
                toggleBtn.textContent = 'Wake Up';
                toggleBtn.className = 'btn btn-danger';
                
                if (currentSleepSession) {
                    const startTime = new Date(currentSleepSession.start_time);
                    const duration = formatDuration(Date.now() - startTime.getTime());
                    durationText.textContent = `Sleep duration: ${duration}`;
                }
            } else {
                statusIcon.textContent = '👁️';
                statusText.textContent = 'Currently Awake';
                toggleBtn.textContent = 'Start Sleep';
                toggleBtn.className = 'btn btn-success';
                durationText.textContent = 'Ready for next sleep';
            }
        }

        async function toggleSleep() {
            if (currentSleepSession) {
                // End current sleep session
                await endSleepSession();
            } else {
                // Start new sleep session
                await startSleepSession();
            }
        }

        async function startSleepSession() {
            try {
                const response = await apiCall(`/baby/${currentBaby.id}/sleep/start`, 'POST', {
                    start_time: new Date().toISOString(),
                    type: 'nap' // Default to nap, can be changed later
                });
                
                if (response.success) {
                    currentSleepSession = response.data;
                    updateSleepStatus(true);
                    showNotification('Sleep session started', 'success');
                }
            } catch (error) {
                showNotification('Error starting sleep session: ' + error.message, 'error');
            }
        }

        async function endSleepSession() {
            try {
                const response = await apiCall(`/baby/${currentBaby.id}/sleep/end`, 'POST', {
                    session_id: currentSleepSession.id,
                    end_time: new Date().toISOString()
                });
                
                if (response.success) {
                    currentSleepSession = null;
                    updateSleepStatus(false);
                    loadSleepData(); // Refresh data
                    loadTodaySummary();
                    showNotification('Sleep session ended', 'success');
                }
            } catch (error) {
                showNotification('Error ending sleep session: ' + error.message, 'error');
            }
        }

        function loadSleepLog() {
            const period = document.getElementById('log-period').value;
            const container = document.getElementById('sleep-log');
            
            // Filter data based on period
            let filteredData = sleepData;
            const now = new Date();
            
            switch (period) {
                case 'today':
                    filteredData = sleepData.filter(entry => {
                        const entryDate = new Date(entry.start_time);
                        return entryDate.toDateString() === now.toDateString();
                    });
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    filteredData = sleepData.filter(entry => {
                        const entryDate = new Date(entry.start_time);
                        return entryDate >= weekAgo;
                    });
                    break;
                case 'month':
                    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    filteredData = sleepData.filter(entry => {
                        const entryDate = new Date(entry.start_time);
                        return entryDate >= monthAgo;
                    });
                    break;
            }

            if (filteredData.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">😴</div>
                        <h4>No Sleep Data</h4>
                        <p>No sleep entries found for the selected period.</p>
                        <button onclick="addManualSleep()" class="btn btn-primary">Add First Sleep Entry</button>
                    </div>
                `;
                return;
            }

            // Sort by start time (newest first)
            filteredData.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));

            container.innerHTML = filteredData.map(entry => {
                const startTime = new Date(entry.start_time);
                const endTime = new Date(entry.end_time);
                const duration = formatDuration(endTime - startTime);
                
                return `
                    <div class="sleep-entry">
                        <div class="sleep-entry-header">
                            <div class="sleep-type ${entry.type}">
                                ${entry.type === 'nap' ? '💤' : '🌙'} ${entry.type.charAt(0).toUpperCase() + entry.type.slice(1)}
                            </div>
                            <div class="sleep-duration">${duration}</div>
                            <div class="sleep-actions">
                                <button onclick="editSleepEntry('${entry.id}')" class="btn btn-sm btn-outline">✏️</button>
                                <button onclick="deleteSleepEntry('${entry.id}')" class="btn btn-sm btn-danger">🗑️</button>
                            </div>
                        </div>
                        <div class="sleep-entry-details">
                            <div class="sleep-time">
                                ${formatTime(startTime)} - ${formatTime(endTime)}
                            </div>
                            <div class="sleep-quality quality-${entry.quality}">
                                Quality: ${entry.quality.charAt(0).toUpperCase() + entry.quality.slice(1)}
                            </div>
                            <div class="sleep-location">
                                📍 ${entry.location.charAt(0).toUpperCase() + entry.location.slice(1)}
                            </div>
                            ${entry.notes ? `<div class="sleep-notes">📝 ${entry.notes}</div>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        async function loadTodaySummary() {
            try {
                const response = await apiCall(`/baby/${currentBaby.id}/sleep/summary/today`, 'GET');
                if (response.success) {
                    updateSummaryDisplay(response.data);
                } else {
                    // Calculate from local data
                    calculateTodaySummary();
                }
            } catch (error) {
                console.error('Error loading summary:', error);
                calculateTodaySummary();
            }
        }

        function calculateTodaySummary() {
            const today = new Date();
            const todayEntries = sleepData.filter(entry => {
                const entryDate = new Date(entry.start_time);
                return entryDate.toDateString() === today.toDateString();
            });

            let totalSleep = 0;
            let naps = 0;
            let longestSleep = 0;
            let nightWakeUps = 0;

            todayEntries.forEach(entry => {
                const duration = new Date(entry.end_time) - new Date(entry.start_time);
                totalSleep += duration;
                
                if (entry.type === 'nap') {
                    naps++;
                } else {
                    // Count wake-ups during night sleep (simplified)
                    if (entry.notes && entry.notes.includes('wake')) {
                        nightWakeUps++;
                    }
                }
                
                if (duration > longestSleep) {
                    longestSleep = duration;
                }
            });

            updateSummaryDisplay({
                total_sleep: totalSleep,
                naps: naps,
                longest_sleep: longestSleep,
                night_wake_ups: nightWakeUps
            });
        }

        function updateSummaryDisplay(summary) {
            document.getElementById('total-sleep-today').textContent = formatDuration(summary.total_sleep);
            document.getElementById('naps-today').textContent = summary.naps;
            document.getElementById('longest-sleep').textContent = formatDuration(summary.longest_sleep);
            document.getElementById('wake-ups').textContent = summary.night_wake_ups;
        }

        function loadSleepGuidelines() {
            // Calculate baby's age (simplified)
            const ageInMonths = 3; // This would be calculated from birth date
            
            let guidelines = '';
            if (ageInMonths < 3) {
                guidelines = `
                    <h5>Newborn (0-3 months)</h5>
                    <ul>
                        <li><strong>Total Sleep:</strong> 14-17 hours per day</li>
                        <li><strong>Night Sleep:</strong> 8-9 hours (with feedings)</li>
                        <li><strong>Naps:</strong> 3-5 naps, 30 minutes to 3 hours</li>
                        <li><strong>Sleep Cycles:</strong> 2-4 hours at a time</li>
                    </ul>
                    <div class="guideline-note">
                        <strong>Note:</strong> Newborns don't have established circadian rhythms yet.
                    </div>
                `;
            } else if (ageInMonths < 6) {
                guidelines = `
                    <h5>Infant (3-6 months)</h5>
                    <ul>
                        <li><strong>Total Sleep:</strong> 12-15 hours per day</li>
                        <li><strong>Night Sleep:</strong> 9-11 hours</li>
                        <li><strong>Naps:</strong> 3-4 naps, 30 minutes to 2 hours</li>
                        <li><strong>Sleep Cycles:</strong> 4-6 hours at night</li>
                    </ul>
                    <div class="guideline-note">
                        <strong>Note:</strong> Sleep patterns are becoming more regular.
                    </div>
                `;
            } else {
                guidelines = `
                    <h5>Older Infant (6+ months)</h5>
                    <ul>
                        <li><strong>Total Sleep:</strong> 11-14 hours per day</li>
                        <li><strong>Night Sleep:</strong> 10-12 hours</li>
                        <li><strong>Naps:</strong> 2-3 naps, 1-3 hours each</li>
                        <li><strong>Sleep Cycles:</strong> 6-8 hours at night</li>
                    </ul>
                    <div class="guideline-note">
                        <strong>Note:</strong> Most babies can sleep through the night.
                    </div>
                `;
            }

            document.getElementById('sleep-guidelines').innerHTML = guidelines;
        }

        function addManualSleep() {
            document.getElementById('sleep-modal-title').textContent = 'Add Sleep Entry';
            document.getElementById('sleep-form').reset();
            document.getElementById('sleep-entry-id').value = '';
            
            // Set default dates to today
            const today = new Date();
            document.getElementById('sleep-start-date').value = today.toISOString().split('T')[0];
            document.getElementById('sleep-end-date').value = today.toISOString().split('T')[0];
            
            document.getElementById('sleep-modal').style.display = 'block';
        }

        function editSleepEntry(entryId) {
            const entry = sleepData.find(e => e.id === entryId);
            if (!entry) return;

            document.getElementById('sleep-modal-title').textContent = 'Edit Sleep Entry';
            document.getElementById('sleep-entry-id').value = entry.id;
            
            const startTime = new Date(entry.start_time);
            const endTime = new Date(entry.end_time);
            
            document.getElementById('sleep-type').value = entry.type;
            document.getElementById('sleep-start-date').value = startTime.toISOString().split('T')[0];
            document.getElementById('sleep-start-time').value = startTime.toTimeString().slice(0, 5);
            document.getElementById('sleep-end-date').value = endTime.toISOString().split('T')[0];
            document.getElementById('sleep-end-time').value = endTime.toTimeString().slice(0, 5);
            document.getElementById('sleep-quality').value = entry.quality;
            document.getElementById('sleep-location').value = entry.location;
            document.getElementById('sleep-notes').value = entry.notes || '';
            
            document.getElementById('sleep-modal').style.display = 'block';
        }

        function closeSleepModal() {
            document.getElementById('sleep-modal').style.display = 'none';
        }

        document.getElementById('sleep-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const entryData = {
                id: document.getElementById('sleep-entry-id').value || generateId(),
                baby_id: currentBaby.id,
                type: document.getElementById('sleep-type').value,
                start_time: new Date(
                    document.getElementById('sleep-start-date').value + 'T' + 
                    document.getElementById('sleep-start-time').value
                ).toISOString(),
                end_time: new Date(
                    document.getElementById('sleep-end-date').value + 'T' + 
                    document.getElementById('sleep-end-time').value
                ).toISOString(),
                quality: document.getElementById('sleep-quality').value,
                location: document.getElementById('sleep-location').value,
                notes: document.getElementById('sleep-notes').value
            };

            try {
                const response = await apiCall('/baby/sleep/save', 'POST', entryData);
                if (response.success) {
                    // Update local data
                    const existingIndex = sleepData.findIndex(e => e.id === entryData.id);
                    if (existingIndex >= 0) {
                        sleepData[existingIndex] = entryData;
                    } else {
                        sleepData.push(entryData);
                    }
                    
                    loadSleepLog();
                    loadTodaySummary();
                    loadSleepChart();
                    closeSleepModal();
                    showNotification('Sleep entry saved successfully!', 'success');
                }
            } catch (error) {
                showNotification('Error saving sleep entry: ' + error.message, 'error');
            }
        });

        async function deleteSleepEntry(entryId) {
            if (!confirm('Are you sure you want to delete this sleep entry?')) return;

            try {
                const response = await apiCall(`/baby/sleep/${entryId}`, 'DELETE');
                if (response.success) {
                    sleepData = sleepData.filter(e => e.id !== entryId);
                    loadSleepLog();
                    loadTodaySummary();
                    loadSleepChart();
                    showNotification('Sleep entry deleted successfully!', 'success');
                }
            } catch (error) {
                showNotification('Error deleting sleep entry: ' + error.message, 'error');
            }
        }

        function addSleepNote() {
            document.getElementById('note-modal').style.display = 'block';
        }

        function closeNoteModal() {
            document.getElementById('note-modal').style.display = 'none';
        }

        document.getElementById('note-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const noteText = document.getElementById('note-text').value;
            
            try {
                const response = await apiCall('/baby/sleep/note', 'POST', {
                    baby_id: currentBaby.id,
                    note: noteText,
                    timestamp: new Date().toISOString()
                });
                
                if (response.success) {
                    closeNoteModal();
                    showNotification('Note added successfully!', 'success');
                    document.getElementById('note-text').value = '';
                }
            } catch (error) {
                showNotification('Error saving note: ' + error.message, 'error');
            }
        });

        function loadSleepChart() {
            const ctx = document.getElementById('sleep-chart').getContext('2d');
            
            if (sleepChart) {
                sleepChart.destroy();
            }

            // Prepare data for the last 7 days
            const last7Days = [];
            const sleepHours = [];
            const napCounts = [];
            
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                last7Days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
                
                // Calculate sleep for this day
                const dayEntries = sleepData.filter(entry => {
                    const entryDate = new Date(entry.start_time);
                    return entryDate.toDateString() === date.toDateString();
                });
                
                let totalSleep = 0;
                let naps = 0;
                
                dayEntries.forEach(entry => {
                    const duration = new Date(entry.end_time) - new Date(entry.start_time);
                    totalSleep += duration;
                    if (entry.type === 'nap') naps++;
                });
                
                sleepHours.push(Math.round(totalSleep / (1000 * 60 * 60) * 10) / 10);
                napCounts.push(naps);
            }

            sleepChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: last7Days,
                    datasets: [{
                        label: 'Total Sleep (hours)',
                        data: sleepHours,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Number of Naps',
                        data: napCounts,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Sleep Hours'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Number of Naps'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function showChart(period) {
            // Update active button
            document.querySelectorAll('.chart-controls .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Reload chart with new period
            loadSleepChart();
        }

        function updateCurrentTime() {
            if (currentSleepSession) {
                const startTime = new Date(currentSleepSession.start_time);
                const duration = formatDuration(Date.now() - startTime.getTime());
                document.getElementById('sleep-duration').textContent = `Sleep duration: ${duration}`;
            }
        }

        // Utility functions
        function formatDuration(milliseconds) {
            const hours = Math.floor(milliseconds / (1000 * 60 * 60));
            const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        }

        function formatTime(date) {
            return date.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
            });
        }

        function generateId() {
            return Date.now().toString() + Math.random().toString(36).substr(2, 9);
        }
    </script>
</body>
</html>
