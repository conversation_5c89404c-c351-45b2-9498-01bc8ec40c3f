# 🔐 Login Credentials & System Access

## 🚀 System Status
- **Backend API**: ✅ Running on `http://localhost:5000`
- **Frontend**: ✅ Running on `http://localhost:3000`
- **Google AI Integration**: ✅ Active with API key configured
- **Database**: ✅ SQLite with fresh schema and demo users

## 👥 Demo User Accounts

### 🛡️ Administrator Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: `admin`
- **Access**: Full system administration
- **Dashboard**: `/pages/admin/admin-dashboard.html`

**Admin Capabilities:**
- Manage doctors and healthcare providers
- Create and manage nutrition plans
- Configure vaccination schedules
- Set up sleep schedules for babies
- Manage government health schemes
- View system analytics and reports
- User management and role assignment

### 👨‍⚕️ Doctor Account
- **Email**: `<EMAIL>`
- **Password**: `doctor123`
- **Role**: `doctor`
- **Name**: Dr. <PERSON>
- **Access**: Patient management and medical records
- **Dashboard**: `/pages/doctor/doctor-dashboard.html`

**Doctor Capabilities:**
- View patient details and medical history
- Generate professional patient reports
- Manage patient appointments
- Access comprehensive medical records
- Provide medical consultations
- Track patient progress

### 👤 Regular User Account
- **Registration**: Available through signup page
- **Role**: `user`
- **Access**: Personal pregnancy and baby care features
- **Dashboard**: `/pages/Preg/pregcare.html`

**User Capabilities:**
- Pregnancy tracking and monitoring
- Baby care management
- AI chatbot assistance
- Vaccination schedules
- Nutrition planning
- Weight tracking
- Appointment booking

## 🌐 Access URLs

### Main Application
- **Home Page**: `http://localhost:3000`
- **Login Page**: `http://localhost:3000/pages/login.html`
- **Registration**: `http://localhost:3000/pages/signup.html`

### Role-Based Dashboards
- **Admin Dashboard**: `http://localhost:3000/pages/admin/admin-dashboard.html`
- **Doctor Dashboard**: `http://localhost:3000/pages/doctor/doctor-dashboard.html`
- **User Dashboard**: `http://localhost:3000/pages/Preg/pregcare.html`

### API Endpoints
- **Health Check**: `http://localhost:5000/api/health`
- **Login**: `http://localhost:5000/api/login`
- **Register**: `http://localhost:5000/api/register`
- **Chat (AI)**: `http://localhost:5000/api/chat`

## 🔧 Quick Start Guide

### 1. Start the System
```bash
# Option 1: Use batch file (Windows)
start.bat

# Option 2: Use Python script
python start_server.py

# Option 3: Manual startup
cd backend && python app.py
cd frontend && python -m http.server 3000
```

### 2. Access the Application
1. Open browser to `http://localhost:3000`
2. Click "Login" in the header
3. Use demo credentials or register new account
4. Explore features based on your role

### 3. Test Different Roles
1. **Admin Experience**: Login with admin credentials to manage the system
2. **Doctor Experience**: Login with doctor credentials to manage patients
3. **User Experience**: Register as a new user for personal care features

## 🤖 AI Chatbot Features

### Google AI Integration
- **Model**: Google Gemini Pro
- **API Key**: `AIzaSyCicw6WxXq5-TtRmwRLwlnmSVArF4JH1KA`
- **Specialization**: Maternal and child health care
- **Access**: Available to all authenticated users

### Chatbot Capabilities
- Pregnancy advice and guidance
- Baby care recommendations
- Medical information and tips
- Vaccination schedule assistance
- Nutrition planning support
- Emergency guidance
- Voice input/output support

### Voice Features
- **Speech Recognition**: Hands-free interaction
- **Text-to-Speech**: Audio responses
- **Multi-language**: Configurable language settings
- **Accessibility**: Enhanced user experience

## 🔒 Security Features

### Authentication
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access**: Admin, Doctor, User permissions
- **Session Management**: Automatic token refresh
- **Password Security**: SHA-256 hashing

### Data Protection
- **CORS Configuration**: Cross-origin request security
- **Input Validation**: Server-side and client-side validation
- **SQL Injection Prevention**: Parameterized queries
- **Secure Storage**: Local storage with encryption

## 📊 System Features

### For Administrators
- Doctor management and registration
- Nutrition plan creation and management
- Vaccination schedule configuration
- Sleep schedule templates
- Government scheme management
- System analytics and reporting
- User role management

### For Doctors
- Patient profile management
- Medical record access
- Report generation
- Appointment scheduling
- Patient progress tracking
- Medical consultation tools

### For Users
- Pregnancy tracking (12 feature pages)
- Baby care management (5 feature pages)
- AI-powered assistance
- Voice interaction
- PDF report generation
- Government scheme access
- Educational resources

## 🛠️ Troubleshooting

### Common Issues
1. **Backend Connection Failed**: Ensure backend server is running on port 5000
2. **Frontend Not Loading**: Ensure frontend server is running on port 3000
3. **Login Issues**: Check credentials and ensure database is initialized
4. **AI Chatbot Not Working**: Verify Google AI API key configuration

### Support Commands
```bash
# Check backend health
curl http://localhost:5000/api/health

# Test login
curl -X POST http://localhost:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Run integration tests
python test_integration.py
```

## 📞 Next Steps

1. **Explore Admin Features**: Login as admin to configure system settings
2. **Test Doctor Workflow**: Use doctor account to manage patient data
3. **Experience User Journey**: Register as user for personal care features
4. **Try AI Chatbot**: Test voice and text interactions
5. **Generate Reports**: Create and download PDF reports
6. **Mobile Testing**: Test responsive design on mobile devices

---

**🎉 System is fully operational and ready for use!**

All authentication issues have been resolved, and the system now properly handles role-based access control with secure login/logout functionality.
