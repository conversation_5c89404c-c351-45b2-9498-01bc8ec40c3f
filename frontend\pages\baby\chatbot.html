<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Baby Care Assistant - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .voice-button {
            min-width: 45px !important;
            padding: 0.75rem !important;
            margin-right: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .voice-button:hover {
            background-color: #f0f0f0;
            transform: scale(1.05);
        }

        .voice-button.recording {
            background-color: #ff4444 !important;
            color: white !important;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .chat-input-wrapper {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="baby-care.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Baby Care</a>
                    <div class="navbar-brand">🤖 MCHS - AI Baby Care Assistant</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>AI Baby Care Assistant</h1>
                <p class="page-description">Get instant answers to your baby care questions from our AI-powered assistant</p>
                <div class="baby-selector">
                    <select id="baby-select" class="form-control" onchange="loadBabyContext()">
                        <option value="">Select Baby</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Chat Interface -->
                    <div class="card chat-container">
                        <div class="card-header">
                            <div class="d-flex justify-content-between items-center">
                                <div class="chat-header-info">
                                    <h3>💬 Chat with AI Assistant</h3>
                                    <div class="ai-status">
                                        <div class="status-indicator online"></div>
                                        <span>AI Assistant is online</span>
                                    </div>
                                </div>
                                <div class="chat-actions">
                                    <button onclick="clearChat()" class="btn btn-outline">🗑️ Clear Chat</button>
                                    <button onclick="exportChat()" class="btn btn-outline">💾 Export</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body chat-body">
                            <div id="chat-messages" class="chat-messages">
                                <!-- Welcome message -->
                                <div class="message ai-message">
                                    <div class="message-avatar">🤖</div>
                                    <div class="message-content">
                                        <div class="message-text">
                                            Hello! I'm your AI Baby Care Assistant. I'm here to help you with questions about 
                                            baby care, development, health, feeding, sleep, and more. How can I assist you today?
                                        </div>
                                        <div class="message-time">Just now</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Typing indicator -->
                            <div id="typing-indicator" class="typing-indicator" style="display: none;">
                                <div class="message-avatar">🤖</div>
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer chat-input-container">
                            <div class="chat-input-wrapper">
                                <textarea id="chat-input" class="chat-input"
                                         placeholder="Ask me anything about baby care..."
                                         rows="1"
                                         onkeypress="handleKeyPress(event)"
                                         oninput="autoResize()"></textarea>
                                <button id="voice-button" onclick="toggleVoiceInput()" class="btn btn-outline voice-button" title="Voice Input">
                                    🎤
                                </button>
                                <button id="speaker-button" onclick="toggleTextToSpeech()" class="btn btn-outline voice-button" title="Toggle Text-to-Speech">
                                    🔊
                                </button>
                                <button id="send-button" onclick="sendMessage()" class="btn btn-primary send-button">
                                    📤 Send
                                </button>
                            </div>
                            <div class="input-suggestions">
                                <span class="suggestions-label">Quick questions:</span>
                                <button class="suggestion-btn" onclick="askQuestion('How often should I feed my baby?')">
                                    Feeding frequency
                                </button>
                                <button class="suggestion-btn" onclick="askQuestion('What are normal sleep patterns for babies?')">
                                    Sleep patterns
                                </button>
                                <button class="suggestion-btn" onclick="askQuestion('When should I be concerned about fever?')">
                                    Fever concerns
                                </button>
                                <button class="suggestion-btn" onclick="askQuestion('How to soothe a crying baby?')">
                                    Soothing techniques
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- AI Assistant Info -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3>🤖 Assistant Capabilities</h3>
                        </div>
                        <div class="card-body">
                            <div class="capabilities-list">
                                <div class="capability-item">
                                    <div class="capability-icon">🍼</div>
                                    <div class="capability-text">
                                        <h5>Feeding Guidance</h5>
                                        <p>Breastfeeding, formula feeding, introducing solids</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <div class="capability-icon">😴</div>
                                    <div class="capability-text">
                                        <h5>Sleep Support</h5>
                                        <p>Sleep schedules, sleep training, bedtime routines</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <div class="capability-icon">🏥</div>
                                    <div class="capability-text">
                                        <h5>Health Questions</h5>
                                        <p>Common illnesses, symptoms, when to call doctor</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <div class="capability-icon">📈</div>
                                    <div class="capability-text">
                                        <h5>Development</h5>
                                        <p>Milestones, activities, developmental concerns</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <div class="capability-icon">🎯</div>
                                    <div class="capability-text">
                                        <h5>Personalized Advice</h5>
                                        <p>Based on your baby's age, health data, and history</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Topics -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3>📚 Recent Topics</h3>
                        </div>
                        <div class="card-body">
                            <div id="recent-topics" class="recent-topics">
                                <!-- Recent topics will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Disclaimer -->
                    <div class="card emergency-disclaimer">
                        <div class="card-header">
                            <h3>⚠️ Important Notice</h3>
                        </div>
                        <div class="card-body">
                            <div class="disclaimer-content">
                                <p><strong>This AI assistant is for informational purposes only and does not replace professional medical advice.</strong></p>
                                
                                <div class="emergency-actions">
                                    <h5>For Emergencies:</h5>
                                    <button onclick="callEmergency()" class="btn btn-danger btn-block">
                                        🚨 Call 911
                                    </button>
                                    <button onclick="contactPediatrician()" class="btn btn-outline btn-block">
                                        📞 Contact Pediatrician
                                    </button>
                                </div>

                                <div class="when-to-seek-help">
                                    <h5>Seek immediate medical attention for:</h5>
                                    <ul>
                                        <li>Difficulty breathing</li>
                                        <li>High fever in newborns</li>
                                        <li>Signs of severe dehydration</li>
                                        <li>Unusual lethargy</li>
                                        <li>Persistent vomiting</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let currentBaby = null;
        let chatHistory = [];
        let recentTopics = [];

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadBabies();
            loadRecentTopics();
            setupChatInput();
        });

        async function loadBabies() {
            try {
                const response = await apiCall('/babies', 'GET');
                if (response.success) {
                    populateBabySelector(response.data);
                    if (response.data.length > 0) {
                        currentBaby = response.data[0];
                        document.getElementById('baby-select').value = currentBaby.id;
                        loadBabyContext();
                    }
                } else {
                    // Demo baby
                    const demoBaby = { 
                        id: 'demo', 
                        name: 'Baby Smith', 
                        birth_date: '2024-01-15',
                        age_months: 3
                    };
                    populateBabySelector([demoBaby]);
                    currentBaby = demoBaby;
                    loadBabyContext();
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        function populateBabySelector(babies) {
            const select = document.getElementById('baby-select');
            select.innerHTML = '<option value="">Select Baby</option>';
            
            babies.forEach(baby => {
                const option = document.createElement('option');
                option.value = baby.id;
                option.textContent = baby.name;
                select.appendChild(option);
            });
        }

        function loadBabyContext() {
            const babyId = document.getElementById('baby-select').value;
            if (!babyId) return;

            // In a real app, this would load baby-specific context for the AI
            currentBaby = { id: babyId };
            
            // Add context message to chat
            addMessage('ai', `I now have access to ${currentBaby.name || 'your baby'}'s information. I can provide more personalized advice based on their age and health data.`);
        }

        function setupChatInput() {
            const chatInput = document.getElementById('chat-input');
            
            // Auto-resize textarea
            chatInput.addEventListener('input', autoResize);
            
            // Handle enter key
            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        function autoResize() {
            const textarea = document.getElementById('chat-input');
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessage('user', message);

            // Clear input
            input.value = '';
            autoResize();

            // Add to chat history
            chatHistory.push({ sender: 'user', message, timestamp: new Date() });

            // Show typing indicator
            showTypingIndicator();

            try {
                // Send message to backend API with Google AI
                const response = await window.apiClient.sendChatMessage(message);

                hideTypingIndicator();

                if (response.success) {
                    const aiResponse = response.data.response;
                    addMessage('ai', aiResponse);
                    chatHistory.push({ sender: 'ai', message: aiResponse, timestamp: new Date() });

                    // Text-to-speech if enabled
                    if (textToSpeechEnabled) {
                        speakText(aiResponse);
                    }

                    // Add to recent topics
                    addToRecentTopics(message);
                } else {
                    throw new Error(response.error || 'Failed to get AI response');
                }
            } catch (error) {
                console.error('Chat error:', error);
                hideTypingIndicator();

                // Fallback to local response
                const fallbackResponse = generateAIResponse(message);
                addMessage('ai', fallbackResponse);
                chatHistory.push({ sender: 'ai', message: fallbackResponse, timestamp: new Date() });

                // Add to recent topics
                addToRecentTopics(message);

                // Show error notification
                showNotification('Using offline mode - limited responses available', 'warning');
            }
        }

        function askQuestion(question) {
            document.getElementById('chat-input').value = question;
            sendMessage();
        }

        function addMessage(sender, text) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const avatar = sender === 'ai' ? '🤖' : '👤';
            const time = new Date().toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
            });
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${text}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Add to chat history
            chatHistory.push({ sender, text, timestamp: new Date().toISOString() });

            // Speak AI responses
            if (sender === 'ai') {
                speakResponse(text);
            }
        }

        function showTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'flex';
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'none';
        }

        function generateAIResponse(userMessage) {
            const message = userMessage.toLowerCase();
            
            // Simple keyword-based responses (in real app, this would use actual AI)
            if (message.includes('feed') || message.includes('feeding')) {
                return `For a ${currentBaby?.age_months || 3}-month-old baby, feeding frequency depends on whether you're breastfeeding or formula feeding. 

**Breastfeeding:** Every 2-3 hours (8-12 times per day)
**Formula feeding:** Every 3-4 hours (6-8 times per day)

Signs your baby is hungry:
• Rooting or turning head toward your hand
• Sucking motions
• Bringing hands to mouth
• Fussiness or crying (late hunger cue)

Always feed on demand and watch for hunger cues rather than strictly following a schedule. If you have concerns about feeding patterns, consult your pediatrician.`;
            }
            
            if (message.includes('sleep') || message.includes('nap')) {
                return `Sleep patterns for babies vary by age. For a ${currentBaby?.age_months || 3}-month-old:

**Total sleep:** 14-17 hours per day
**Night sleep:** 8-9 hours (may wake for feedings)
**Naps:** 3-4 naps during the day

**Tips for better sleep:**
• Establish a consistent bedtime routine
• Create a calm, dark sleep environment
• Watch for sleep cues (yawning, rubbing eyes)
• Consider swaddling for younger babies
• Be patient - sleep patterns develop gradually

Remember, every baby is different. Some may sleep longer stretches earlier than others.`;
            }
            
            if (message.includes('fever') || message.includes('temperature')) {
                return `Fever in babies requires careful attention:

**When to call doctor immediately:**
• Temperature over 100.4°F (38°C) in babies under 3 months
• Temperature over 102°F (38.9°C) in older babies
• Fever lasting more than 3 days
• Signs of dehydration or difficulty breathing

**How to help:**
• Give infant acetaminophen or ibuprofen (if over 6 months) as directed
• Dress baby in light clothing
• Offer frequent fluids
• Monitor temperature regularly

**Always trust your instincts** - if your baby seems very unwell, contact your pediatrician regardless of temperature.`;
            }
            
            if (message.includes('cry') || message.includes('fussy') || message.includes('soothe')) {
                return `Crying is normal for babies, but here are soothing techniques to try:

**The 5 S's method:**
1. **Swaddling** - Wrap baby snugly in a blanket
2. **Side/stomach position** - Hold baby on their side (never for sleeping)
3. **Shushing** - Make "shh" sounds or use white noise
4. **Swinging** - Gentle rhythmic motion
5. **Sucking** - Offer pacifier or clean finger

**Other techniques:**
• Check for basic needs (hunger, dirty diaper, temperature)
• Try skin-to-skin contact
• Take baby for a walk or car ride
• Dim the lights and reduce stimulation

**Remember:** It's normal for babies to cry 1-3 hours per day. If crying seems excessive or you're concerned, contact your pediatrician.`;
            }
            
            if (message.includes('development') || message.includes('milestone')) {
                return `At ${currentBaby?.age_months || 3} months, typical milestones include:

**Physical:**
• Better head control when held upright
• Beginning to push up when on tummy
• Bringing hands to mouth
• Grasping objects briefly

**Social/Emotional:**
• Smiling responsively
• Enjoying interaction with people
• Beginning to show excitement

**Communication:**
• Cooing and making vowel sounds
• Turning head toward sounds
• Beginning to laugh

**Remember:** All babies develop at their own pace. These are general guidelines. If you have concerns about development, discuss them with your pediatrician during regular checkups.`;
            }
            
            // Default response
            return `I understand you're asking about "${userMessage}". While I can provide general baby care information, I'd recommend:

1. **Consulting your pediatrician** for specific medical concerns
2. **Checking reputable sources** like the American Academy of Pediatrics
3. **Trusting your parental instincts** - you know your baby best

Is there a specific aspect of baby care you'd like me to help with? I can provide information about:
• Feeding and nutrition
• Sleep patterns and routines
• Development and milestones
• Common health concerns
• Soothing techniques

Feel free to ask more specific questions!`;
        }

        function addToRecentTopics(topic) {
            // Extract key topic from question
            let topicKeyword = 'General question';
            const message = topic.toLowerCase();
            
            if (message.includes('feed')) topicKeyword = 'Feeding';
            else if (message.includes('sleep')) topicKeyword = 'Sleep';
            else if (message.includes('fever')) topicKeyword = 'Fever';
            else if (message.includes('cry')) topicKeyword = 'Crying/Soothing';
            else if (message.includes('development')) topicKeyword = 'Development';
            
            // Add to recent topics (avoid duplicates)
            if (!recentTopics.includes(topicKeyword)) {
                recentTopics.unshift(topicKeyword);
                if (recentTopics.length > 5) {
                    recentTopics.pop();
                }
                updateRecentTopicsDisplay();
            }
        }

        function loadRecentTopics() {
            // Load from localStorage or use defaults
            const saved = localStorage.getItem('recentChatTopics');
            if (saved) {
                recentTopics = JSON.parse(saved);
            } else {
                recentTopics = ['Feeding', 'Sleep patterns', 'Development'];
            }
            updateRecentTopicsDisplay();
        }

        function updateRecentTopicsDisplay() {
            const container = document.getElementById('recent-topics');
            
            if (recentTopics.length === 0) {
                container.innerHTML = '<p class="text-muted">No recent topics</p>';
                return;
            }
            
            container.innerHTML = recentTopics.map(topic => `
                <div class="recent-topic-item" onclick="askAboutTopic('${topic}')">
                    <div class="topic-icon">💬</div>
                    <div class="topic-text">${topic}</div>
                </div>
            `).join('');
            
            // Save to localStorage
            localStorage.setItem('recentChatTopics', JSON.stringify(recentTopics));
        }

        function askAboutTopic(topic) {
            const questions = {
                'Feeding': 'How often should I feed my baby?',
                'Sleep': 'What are normal sleep patterns for my baby?',
                'Fever': 'When should I be concerned about fever?',
                'Crying/Soothing': 'How can I soothe my crying baby?',
                'Development': 'What milestones should I expect for my baby\'s age?'
            };
            
            const question = questions[topic] || `Tell me about ${topic}`;
            askQuestion(question);
        }

        function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                document.getElementById('chat-messages').innerHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <div class="message-text">
                                Hello! I'm your AI Baby Care Assistant. I'm here to help you with questions about 
                                baby care, development, health, feeding, sleep, and more. How can I assist you today?
                            </div>
                            <div class="message-time">Just now</div>
                        </div>
                    </div>
                `;
                chatHistory = [];
                showNotification('Chat cleared', 'success');
            }
        }

        function exportChat() {
            if (chatHistory.length === 0) {
                showNotification('No chat history to export', 'info');
                return;
            }
            
            const chatText = chatHistory.map(msg => 
                `[${new Date(msg.timestamp).toLocaleString()}] ${msg.sender.toUpperCase()}: ${msg.text}`
            ).join('\n\n');
            
            const blob = new Blob([chatText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `baby-care-chat-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            
            showNotification('Chat exported successfully', 'success');
        }

        function callEmergency() {
            if (confirm('Call emergency services (911)?')) {
                window.location.href = 'tel:911';
            }
        }

        function contactPediatrician() {
            showNotification('Connecting to your pediatrician...', 'info');
        }

        // Voice Assistant Functionality
        let recognition = null;
        let isRecording = false;
        let textToSpeechEnabled = true;

        function initializeSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'en-US';

                recognition.onstart = function() {
                    isRecording = true;
                    const voiceButton = document.getElementById('voice-button');
                    voiceButton.classList.add('recording');
                    voiceButton.innerHTML = '🔴';
                    voiceButton.title = 'Recording... Click to stop';
                    showNotification('Listening... Speak now', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('chat-input').value = transcript;
                    showNotification('Voice input captured: "' + transcript + '"', 'success');
                    autoResize();
                };

                recognition.onerror = function(event) {
                    console.error('Speech recognition error:', event.error);
                    showNotification('Voice input error: ' + event.error, 'error');
                    stopRecording();
                };

                recognition.onend = function() {
                    stopRecording();
                };

                return true;
            } else {
                showNotification('Speech recognition not supported in this browser', 'error');
                return false;
            }
        }

        function toggleVoiceInput() {
            if (!recognition && !initializeSpeechRecognition()) {
                return;
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        function stopRecording() {
            isRecording = false;
            const voiceButton = document.getElementById('voice-button');
            voiceButton.classList.remove('recording');
            voiceButton.innerHTML = '🎤';
            voiceButton.title = 'Voice Input';
        }

        // Text-to-Speech for AI responses
        function speakResponse(text) {
            if (textToSpeechEnabled && 'speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.8;
                utterance.pitch = 1;
                utterance.volume = 0.8;

                // Try to use a female voice if available
                const voices = speechSynthesis.getVoices();
                const femaleVoice = voices.find(voice =>
                    voice.name.toLowerCase().includes('female') ||
                    voice.name.toLowerCase().includes('woman') ||
                    voice.name.toLowerCase().includes('samantha') ||
                    voice.name.toLowerCase().includes('karen')
                );

                if (femaleVoice) {
                    utterance.voice = femaleVoice;
                }

                speechSynthesis.speak(utterance);
            }
        }

        function toggleTextToSpeech() {
            textToSpeechEnabled = !textToSpeechEnabled;
            const speakerButton = document.getElementById('speaker-button');

            if (textToSpeechEnabled) {
                speakerButton.innerHTML = '🔊';
                speakerButton.title = 'Text-to-Speech: ON (Click to disable)';
                showNotification('Text-to-Speech enabled', 'success');
            } else {
                speakerButton.innerHTML = '🔇';
                speakerButton.title = 'Text-to-Speech: OFF (Click to enable)';
                showNotification('Text-to-Speech disabled', 'info');
                // Stop any current speech
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
            }
        }

        // Initialize speech recognition when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSpeechRecognition();
            loadBabies();
            setupChatInput();
        });
    </script>

    <!-- Configuration and API Scripts -->
    <script src="../../js/config.js"></script>
    <script src="../../js/api-integration.js"></script>
    <script src="../../js/auth-service.js"></script>
    <script src="../../js/rbac.js"></script>
</body>
</html>
