<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregnancy Care - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
            min-height: 100vh;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #e91e63 0%, #ff6b9d 50%, #f06292 100%);
            color: white;
            text-align: center;
            padding: 5rem 2rem;
            position: relative;
            overflow: hidden;
            min-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="20" r="1.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="90" r="1.2" fill="white" opacity="0.1"/></svg>');
            background-size: 50px 50px;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: white;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-content p {
            font-size: 1.4rem;
            opacity: 0.95;
            max-width: 600px;
            margin: 0 auto;
            color: white;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
            line-height: 1.6;
        }

        /* Quick Access Section */
        .quick-access {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 5rem 2rem;
            text-align: center;
            position: relative;
            margin-top: -50px;
            z-index: 10;
        }

        .quick-access::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23e91e63" opacity="0.1"/><circle cx="80" cy="30" r="1.5" fill="%234caf50" opacity="0.1"/><circle cx="60" cy="70" r="2.5" fill="%232196f3" opacity="0.1"/><circle cx="30" cy="80" r="1" fill="%23ff9800" opacity="0.1"/></svg>');
            background-size: 100px 100px;
            pointer-events: none;
        }

        .quick-access h2 {
            font-size: 3rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1;
        }

        .quick-access-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2.5rem;
            max-width: 1000px;
            margin: 0 auto 4rem;
            position: relative;
            z-index: 1;
        }

        .features-grid-bottom {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 2.5rem;
            max-width: 1200px;
            margin: 0 auto;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .feature-item {
            background: white;
            border-radius: 25px;
            padding: 2.5rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            text-decoration: none;
            color: #333;
            text-align: center;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e91e63, #ff6b9d);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-item:hover::before {
            transform: scaleX(1);
        }

        .feature-item:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: rgba(233, 30, 99, 0.2);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #e91e63, #ff6b9d);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1.2rem;
            box-shadow: 0 8px 20px rgba(233, 30, 99, 0.3);
            transition: all 0.3s ease;
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.05) rotate(3deg);
            box-shadow: 0 12px 30px rgba(233, 30, 99, 0.4);
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            text-align: center;
            line-height: 1.4;
            margin: 0;
        }

        /* Special Ask Assistant Card */
        .ask-assistant-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2.5rem 1.5rem;
            border-radius: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            text-decoration: none;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .ask-assistant-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .ask-assistant-card:hover::before {
            transform: scaleX(1);
        }

        .ask-assistant-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .ask-assistant-card:hover .ask-assistant-icon {
            transform: scale(1.05) rotate(3deg);
            background: rgba(255, 255, 255, 0.3);
        }

        .ask-assistant-icon {
            width: 70px;
            height: 70px;
            margin: 0 auto 1.2rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .ask-assistant-title {
            font-size: 1.2rem;
            color: white;
            margin: 0;
            font-weight: 600;
            line-height: 1.4;
        }

        /* Download Report Button Styling */
        .download-report-item .feature-icon {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .download-report-item:hover .feature-icon {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 35px rgba(220, 53, 69, 0.4);
        }



        /* Responsive Design */
        @media (max-width: 1024px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .features-grid-bottom {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                min-height: 50vh;
                padding: 4rem 1rem;
            }

            .hero-content h1 {
                font-size: 2.8rem;
            }

            .hero-content p {
                font-size: 1.2rem;
            }

            .quick-access {
                padding: 4rem 1rem;
            }

            .quick-access h2 {
                font-size: 2.2rem;
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .features-grid-bottom {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-item {
                padding: 2rem 1rem;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.8rem;
            }

            .feature-title {
                font-size: 1.1rem;
            }

            .ask-assistant-card {
                padding: 1.5rem 1rem;
            }

            .ask-assistant-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .ask-assistant-title {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .hero-section {
                min-height: 45vh;
                padding: 3rem 1rem;
            }

            .hero-content h1 {
                font-size: 2.2rem;
            }

            .hero-content p {
                font-size: 1.1rem;
            }

            .quick-access h2 {
                font-size: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-item {
                padding: 2rem 1.5rem;
            }
        }
        /* Header Styles */
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: #e91e63;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .logo-icon {
            background: #e91e63;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn {
            background: #f5f5f5;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #e91e63;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="../../home.html" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Preg and Baby Care</span>
            </a>
            <a href="../../home.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <h1>Pregnancy Care</h1>
            <p>Comprehensive guidance for a healthy and happy pregnancy journey</p>
        </div>
    </section>

    <!-- Quick Access Section -->
    <section class="quick-access">
        <h2>Quick Access</h2>
        <p class="quick-access-subtitle">Essential tools and resources for your pregnancy journey</p>
        <!-- First Row - 3 buttons -->
        <div class="features-grid">
            <!-- Nutrition Plans -->
            <a href="nutrition.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="feature-title">Nutrition Plans</div>
            </a>

            <!-- Meal Plans -->
            <a href="nutrition.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-apple-alt"></i>
                </div>
                <div class="feature-title">Meal Plans</div>
            </a>

            <!-- Government Schemes -->
            <a href="government-schemes.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-hands-helping"></i>
                </div>
                <div class="feature-title">Government Schemes</div>
            </a>

            <!-- Weight Tracker -->
            <a href="weight-tracker.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-weight"></i>
                </div>
                <div class="feature-title">Weight Tracker</div>
            </a>

            <!-- Exercise Guides -->
            <a href="exercise.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-running"></i>
                </div>
                <div class="feature-title">Exercise Guides</div>
            </a>

            <!-- Download Report -->
            <a href="#" onclick="downloadPregnancyReport(); return false;" class="feature-item download-report-item">
                <div class="feature-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="feature-title">Download Report</div>
            </a>
        </div>

        <!-- Bottom Row with Ask Assistant and Report Downloader -->
        <div class="features-grid-bottom">
            <!-- Doctor Appointments -->
            <a href="appointments.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="feature-title">Doctor<br>Appointments</div>
            </a>

            <!-- Ask Assistant (Large Center Card) -->
            <a href="chatbot.html" class="ask-assistant-card">
                <div class="ask-assistant-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ask-assistant-title">Ask Assistant</div>
            </a>

            <!-- FAQ -->
            <a href="faq.html" class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="feature-title">FAQ</div>
            </a>
        </div>


    </section>

    <script>
        // Add hover effects and animations
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.feature-item, .ask-assistant-card');

            items.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    if (this.classList.contains('ask-assistant-card')) {
                        this.style.transform = 'translateY(-8px)';
                    } else {
                        this.style.transform = 'translateY(-5px)';
                    }
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // PDF Report Generation Function
        async function downloadPregnancyReport() {
            try {
                // Show loading notification
                showNotification('Generating pregnancy report...', 'info');

                // Get jsPDF instance
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Get current date
                const currentDate = new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                // Sample pregnancy data (in a real app, this would come from API)
                const pregnancyData = await getPregnancyData();

                // Set up document styling
                doc.setFontSize(20);
                doc.setTextColor(233, 30, 99); // Primary color
                doc.text('Pregnancy Care Report', 20, 30);

                doc.setFontSize(12);
                doc.setTextColor(100, 100, 100);
                doc.text(`Generated on: ${currentDate}`, 20, 40);

                // Patient Information Section
                doc.setFontSize(16);
                doc.setTextColor(0, 0, 0);
                doc.text('Patient Information', 20, 60);

                doc.setFontSize(12);
                doc.text(`Name: ${pregnancyData.patientName}`, 25, 75);
                doc.text(`Age: ${pregnancyData.age} years`, 25, 85);
                doc.text(`Due Date: ${pregnancyData.dueDate}`, 25, 95);
                doc.text(`Current Week: ${pregnancyData.currentWeek} weeks`, 25, 105);
                doc.text(`Trimester: ${pregnancyData.trimester}`, 25, 115);

                // Pregnancy Progress Section
                doc.setFontSize(16);
                doc.text('Pregnancy Progress', 20, 135);

                doc.setFontSize(12);
                doc.text(`Baby Size: ${pregnancyData.babySize}`, 25, 150);
                doc.text(`Weight Gain: ${pregnancyData.weightGain} kg`, 25, 160);
                doc.text(`Blood Pressure: ${pregnancyData.bloodPressure}`, 25, 170);
                doc.text(`Heart Rate: ${pregnancyData.heartRate} bpm`, 25, 180);

                // Appointments Section
                doc.setFontSize(16);
                doc.text('Recent Appointments', 20, 200);

                doc.setFontSize(12);
                pregnancyData.appointments.forEach((appointment, index) => {
                    const yPos = 215 + (index * 10);
                    doc.text(`${appointment.date} - ${appointment.type}`, 25, yPos);
                });

                // Add new page for additional content
                doc.addPage();

                // Nutrition & Health Section
                doc.setFontSize(16);
                doc.setTextColor(0, 0, 0);
                doc.text('Nutrition & Health Tracking', 20, 30);

                doc.setFontSize(12);
                doc.text('Daily Nutrition Goals:', 25, 50);
                doc.text(`• Calories: ${pregnancyData.nutrition.calories} kcal`, 30, 65);
                doc.text(`• Protein: ${pregnancyData.nutrition.protein} g`, 30, 75);
                doc.text(`• Calcium: ${pregnancyData.nutrition.calcium} mg`, 30, 85);
                doc.text(`• Iron: ${pregnancyData.nutrition.iron} mg`, 30, 95);
                doc.text(`• Folic Acid: ${pregnancyData.nutrition.folicAcid} mcg`, 30, 105);

                // Exercise & Wellness
                doc.setFontSize(16);
                doc.text('Exercise & Wellness', 20, 125);

                doc.setFontSize(12);
                doc.text('Recommended Activities:', 25, 145);
                pregnancyData.exercises.forEach((exercise, index) => {
                    const yPos = 160 + (index * 10);
                    doc.text(`• ${exercise}`, 30, yPos);
                });

                // Important Notes Section
                doc.setFontSize(16);
                doc.text('Important Notes & Reminders', 20, 210);

                doc.setFontSize(12);
                pregnancyData.notes.forEach((note, index) => {
                    const yPos = 225 + (index * 10);
                    doc.text(`• ${note}`, 25, yPos);
                });

                // Footer
                doc.setFontSize(10);
                doc.setTextColor(150, 150, 150);
                doc.text('This report is generated by Preg and Baby Care System', 20, 280);
                doc.text('For medical advice, please consult with your healthcare provider', 20, 290);

                // Save the PDF
                const fileName = `pregnancy-report-${new Date().toISOString().split('T')[0]}.pdf`;
                doc.save(fileName);

                showNotification('Pregnancy report downloaded successfully!', 'success');

            } catch (error) {
                console.error('Error generating PDF:', error);
                showNotification('Error generating report. Please try again.', 'error');
            }
        }

        // Function to get pregnancy data (mock data for demo)
        async function getPregnancyData() {
            // In a real application, this would fetch data from your API
            return {
                patientName: 'Sarah Johnson',
                age: 28,
                dueDate: 'March 15, 2024',
                currentWeek: 24,
                trimester: 'Second Trimester',
                babySize: 'Size of a corn (30 cm)',
                weightGain: 8.5,
                bloodPressure: '120/80 mmHg',
                heartRate: 72,
                appointments: [
                    { date: '2024-01-15', type: 'Regular Checkup' },
                    { date: '2024-01-01', type: 'Ultrasound Scan' },
                    { date: '2023-12-15', type: 'Blood Test' }
                ],
                nutrition: {
                    calories: 2200,
                    protein: 75,
                    calcium: 1200,
                    iron: 27,
                    folicAcid: 600
                },
                exercises: [
                    'Prenatal Yoga (30 minutes)',
                    'Walking (20-30 minutes daily)',
                    'Swimming (2-3 times per week)',
                    'Pelvic Floor Exercises',
                    'Breathing Exercises'
                ],
                notes: [
                    'Take prenatal vitamins daily',
                    'Stay hydrated - drink 8-10 glasses of water',
                    'Avoid raw fish, unpasteurized dairy, and deli meats',
                    'Get adequate sleep (7-9 hours per night)',
                    'Schedule next appointment in 4 weeks'
                ]
            };
        }

        // Notification function
        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            `;

            // Set background color based on type
            switch(type) {
                case 'success':
                    notification.style.backgroundColor = '#4caf50';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#f44336';
                    break;
                case 'info':
                    notification.style.backgroundColor = '#2196f3';
                    break;
                default:
                    notification.style.backgroundColor = '#333';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

    </script>
</body>
</html>