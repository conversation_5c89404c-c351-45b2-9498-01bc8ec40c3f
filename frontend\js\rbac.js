/**
 * Role-Based Access Control (RBAC) System
 * Manages user permissions and access control throughout the application
 */

class RBACSystem {
    constructor() {
        this.roles = {
            admin: {
                name: 'Administrator',
                permissions: [
                    'system.manage',
                    'users.create',
                    'users.read',
                    'users.update',
                    'users.delete',
                    'analytics.view',
                    'reports.generate',
                    'settings.manage',
                    'logs.view',
                    'backup.create',
                    'all.access'
                ],
                pages: [
                    '/admin/',
                    '/doctor/',
                    '/mother/',
                    '/baby/'
                ]
            },
            doctor: {
                name: 'Doctor',
                permissions: [
                    'patients.read',
                    'patients.update',
                    'appointments.manage',
                    'medical_records.create',
                    'medical_records.read',
                    'medical_records.update',
                    'prescriptions.create',
                    'vaccinations.manage',
                    'reports.patient'
                ],
                pages: [
                    '/doctor/',
                    '/mother/',
                    '/baby/'
                ]
            },
            user: {
                name: 'User/Patient',
                permissions: [
                    'profile.read',
                    'profile.update',
                    'appointments.create',
                    'appointments.read',
                    'records.own.read',
                    'tracking.own.manage',
                    'chat.use',
                    'reports.own'
                ],
                pages: [
                    '/mother/',
                    '/baby/'
                ]
            }
        };

        this.pagePermissions = {
            // Admin pages
            '/admin/dashboard.html': ['system.manage'],
            '/admin/users.html': ['users.read'],
            '/admin/analytics.html': ['analytics.view'],
            '/admin/reports.html': ['reports.generate'],
            '/admin/settings.html': ['settings.manage'],
            '/admin/system-logs.html': ['logs.view'],

            // Doctor pages
            '/doctor/dashboard.html': ['patients.read'],
            '/doctor/patients.html': ['patients.read'],
            '/doctor/appointments.html': ['appointments.manage'],
            '/doctor/records.html': ['medical_records.read'],

            // Mother/Pregnancy pages
            '/mother/pregnancy-care.html': ['profile.read'],
            '/mother/weight-tracking.html': ['tracking.own.manage'],
            '/mother/nutrition-guide.html': ['profile.read'],
            '/mother/exercise-routines.html': ['profile.read'],
            '/mother/meditation.html': ['profile.read'],
            '/mother/appointment-booking.html': ['appointments.create'],
            '/mother/government-schemes.html': ['profile.read'],
            '/mother/prenatal-classes.html': ['profile.read'],
            '/mother/health-tips.html': ['profile.read'],
            '/mother/emergency-contacts.html': ['profile.read'],
            '/mother/pregnancy-journal.html': ['tracking.own.manage'],
            '/mother/birth-plan.html': ['profile.update'],

            // Baby pages
            '/baby/baby-care.html': ['profile.read'],
            '/baby/vaccination-tracker.html': ['tracking.own.manage'],
            '/baby/growth-monitoring.html': ['tracking.own.manage'],
            '/baby/sleep-tracking.html': ['tracking.own.manage'],
            '/baby/government-schemes.html': ['profile.read'],
            '/baby/physical-activities.html': ['profile.read'],
            '/baby/nutrition-tracking.html': ['tracking.own.manage']
        };

        this.initializeAccessControl();
    }

    // Initialize access control
    initializeAccessControl() {
        // Check access on page load
        document.addEventListener('DOMContentLoaded', () => {
            this.checkPageAccess();
            this.hideUnauthorizedElements();
        });

        // Intercept navigation
        this.interceptNavigation();
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!window.authService || !window.authService.isAuthenticated()) {
            return false;
        }

        const user = window.authService.userData;
        if (!user || !user.role) {
            return false;
        }

        const role = this.roles[user.role];
        if (!role) {
            return false;
        }

        return role.permissions.includes(permission) || role.permissions.includes('all.access');
    }

    // Check if user can access a page
    canAccessPage(pagePath) {
        if (!window.authService || !window.authService.isAuthenticated()) {
            return false;
        }

        const user = window.authService.userData;
        if (!user || !user.role) {
            return false;
        }

        const role = this.roles[user.role];
        if (!role) {
            return false;
        }

        // Check if user role can access this page category
        const pageCategory = this.getPageCategory(pagePath);
        if (!role.pages.some(allowedPage => pageCategory.startsWith(allowedPage))) {
            return false;
        }

        // Check specific page permissions
        const requiredPermissions = this.pagePermissions[pagePath];
        if (!requiredPermissions) {
            return true; // No specific permissions required
        }

        return requiredPermissions.some(permission => this.hasPermission(permission));
    }

    // Get page category from path
    getPageCategory(pagePath) {
        if (pagePath.includes('/admin/')) return '/admin/';
        if (pagePath.includes('/doctor/')) return '/doctor/';
        if (pagePath.includes('/mother/')) return '/mother/';
        if (pagePath.includes('/baby/')) return '/baby/';
        return '/';
    }

    // Check current page access
    checkPageAccess() {
        const currentPath = window.location.pathname;
        
        // Skip check for login page and public pages
        if (currentPath === '/login.html' || currentPath === '/' || currentPath === '/home.html') {
            return;
        }

        if (!this.canAccessPage(currentPath)) {
            this.handleUnauthorizedAccess(currentPath);
        }
    }

    // Handle unauthorized access
    handleUnauthorizedAccess(attemptedPath) {
        console.warn('Unauthorized access attempt to:', attemptedPath);
        
        // Log security event
        if (window.authService) {
            window.authService.logSecurityEvent('unauthorized_access', {
                attempted_path: attemptedPath,
                user_role: window.authService.userData?.role
            });
        }

        // Redirect to appropriate page based on user role
        const user = window.authService?.userData;
        if (!user) {
            window.location.href = '/login.html';
            return;
        }

        let redirectPath = '/home.html';
        switch (user.role) {
            case 'admin':
                redirectPath = '/admin/dashboard.html';
                break;
            case 'doctor':
                redirectPath = '/doctor/dashboard.html';
                break;
            case 'user':
                redirectPath = '/mother/pregnancy-care.html';
                break;
        }

        alert('You do not have permission to access this page. Redirecting to your dashboard.');
        window.location.href = redirectPath;
    }

    // Hide unauthorized elements
    hideUnauthorizedElements() {
        // Hide elements based on data-permission attribute
        document.querySelectorAll('[data-permission]').forEach(element => {
            const requiredPermission = element.getAttribute('data-permission');
            if (!this.hasPermission(requiredPermission)) {
                element.style.display = 'none';
            }
        });

        // Hide elements based on data-role attribute
        document.querySelectorAll('[data-role]').forEach(element => {
            const requiredRole = element.getAttribute('data-role');
            const user = window.authService?.userData;
            if (!user || user.role !== requiredRole) {
                element.style.display = 'none';
            }
        });

        // Hide navigation items based on access
        this.hideUnauthorizedNavigation();
    }

    // Hide unauthorized navigation items
    hideUnauthorizedNavigation() {
        document.querySelectorAll('a[href]').forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('/') && !this.canAccessPage(href)) {
                link.style.display = 'none';
                // Also hide parent li if it exists
                const parentLi = link.closest('li');
                if (parentLi) {
                    parentLi.style.display = 'none';
                }
            }
        });
    }

    // Intercept navigation attempts
    interceptNavigation() {
        // Intercept clicks on links
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[href]');
            if (link) {
                const href = link.getAttribute('href');
                if (href && href.startsWith('/') && !this.canAccessPage(href)) {
                    event.preventDefault();
                    this.handleUnauthorizedAccess(href);
                }
            }
        });

        // Intercept programmatic navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = function(state, title, url) {
            if (url && !window.rbacSystem.canAccessPage(url)) {
                window.rbacSystem.handleUnauthorizedAccess(url);
                return;
            }
            return originalPushState.apply(this, arguments);
        };

        history.replaceState = function(state, title, url) {
            if (url && !window.rbacSystem.canAccessPage(url)) {
                window.rbacSystem.handleUnauthorizedAccess(url);
                return;
            }
            return originalReplaceState.apply(this, arguments);
        };
    }

    // Get user's accessible pages
    getAccessiblePages() {
        if (!window.authService || !window.authService.isAuthenticated()) {
            return [];
        }

        const user = window.authService.userData;
        if (!user || !user.role) {
            return [];
        }

        const accessiblePages = [];
        for (const [pagePath, permissions] of Object.entries(this.pagePermissions)) {
            if (this.canAccessPage(pagePath)) {
                accessiblePages.push(pagePath);
            }
        }

        return accessiblePages;
    }

    // Get user's permissions
    getUserPermissions() {
        if (!window.authService || !window.authService.isAuthenticated()) {
            return [];
        }

        const user = window.authService.userData;
        if (!user || !user.role) {
            return [];
        }

        const role = this.roles[user.role];
        return role ? role.permissions : [];
    }

    // Check if user can perform action
    canPerformAction(action, resource = null) {
        const permission = resource ? `${resource}.${action}` : action;
        return this.hasPermission(permission);
    }

    // Secure function execution
    secureExecute(requiredPermission, callback, unauthorizedCallback = null) {
        if (this.hasPermission(requiredPermission)) {
            return callback();
        } else {
            console.warn('Unauthorized function execution attempt:', requiredPermission);
            if (window.authService) {
                window.authService.logSecurityEvent('unauthorized_function_call', {
                    required_permission: requiredPermission
                });
            }
            if (unauthorizedCallback) {
                return unauthorizedCallback();
            }
            throw new Error('Insufficient permissions');
        }
    }
}

// Create global instance
window.rbacSystem = new RBACSystem();

// Global helper functions
window.hasPermission = (permission) => window.rbacSystem.hasPermission(permission);
window.canAccessPage = (pagePath) => window.rbacSystem.canAccessPage(pagePath);
window.canPerformAction = (action, resource) => window.rbacSystem.canPerformAction(action, resource);
window.secureExecute = (permission, callback, unauthorizedCallback) => 
    window.rbacSystem.secureExecute(permission, callback, unauthorizedCallback);

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RBACSystem;
}
