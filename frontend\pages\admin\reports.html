<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Reports - Admin Dashboard</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .reports-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .report-filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .report-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.875rem;
        }

        .export-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .export-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-export {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-pdf {
            background: #dc3545;
            color: white;
        }

        .btn-excel {
            background: #28a745;
            color: white;
        }

        .btn-csv {
            background: #007bff;
            color: white;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .data-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            font-weight: 600;
        }

        .table-row {
            padding: 1rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            border-bottom: 1px solid #eee;
            align-items: center;
        }

        .table-row:hover {
            background: #f8f9fa;
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
        }

        .trend-up {
            color: #28a745;
        }

        .trend-down {
            color: #dc3545;
        }

        .trend-stable {
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }

            .export-buttons {
                flex-direction: column;
            }

            .stats-summary {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="reports-container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1>📊 System Reports</h1>
                <p>Comprehensive analytics and data insights</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="generateReport()">
                    🔄 Refresh Data
                </button>
                <a href="users.html" class="btn btn-secondary">
                    ← Back to Users
                </a>
            </div>
        </div>

        <!-- Report Filters -->
        <div class="report-filters">
            <h3>📅 Report Filters</h3>
            <div class="filter-row">
                <div class="form-group">
                    <label for="date-from">From Date</label>
                    <input type="date" id="date-from" onchange="updateReports()">
                </div>
                <div class="form-group">
                    <label for="date-to">To Date</label>
                    <input type="date" id="date-to" onchange="updateReports()">
                </div>
                <div class="form-group">
                    <label for="report-type">Report Type</label>
                    <select id="report-type" onchange="updateReports()">
                        <option value="all">All Reports</option>
                        <option value="users">User Analytics</option>
                        <option value="appointments">Appointments</option>
                        <option value="health">Health Records</option>
                        <option value="system">System Usage</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-secondary" onclick="resetFilters()">Reset Filters</button>
                </div>
            </div>
        </div>

        <!-- Statistics Summary -->
        <div class="stats-summary">
            <div class="stat-item">
                <div class="stat-number" id="total-users-stat">0</div>
                <div class="stat-label">Total Users</div>
                <div class="trend-indicator trend-up">
                    <span>↗️</span>
                    <span>+12% this month</span>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-appointments-stat">0</div>
                <div class="stat-label">Appointments</div>
                <div class="trend-indicator trend-up">
                    <span>↗️</span>
                    <span>+8% this week</span>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-records-stat">0</div>
                <div class="stat-label">Health Records</div>
                <div class="trend-indicator trend-stable">
                    <span>➡️</span>
                    <span>Stable</span>
                </div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="active-doctors-stat">0</div>
                <div class="stat-label">Active Doctors</div>
                <div class="trend-indicator trend-up">
                    <span>↗️</span>
                    <span>+2 new</span>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section">
            <h3>📤 Export Reports</h3>
            <p>Download reports in various formats for external analysis</p>
            <div class="export-buttons">
                <button class="btn-export btn-pdf" onclick="exportToPDF()">
                    📄 Export to PDF
                </button>
                <button class="btn-export btn-excel" onclick="exportToExcel()">
                    📊 Export to Excel
                </button>
                <button class="btn-export btn-csv" onclick="exportToCSV()">
                    📋 Export to CSV
                </button>
                <button class="btn-export btn-primary" onclick="scheduleReport()">
                    ⏰ Schedule Report
                </button>
            </div>
        </div>

        <!-- Charts Grid -->
        <div class="reports-grid">
            <!-- User Registration Chart -->
            <div class="report-card">
                <div class="report-header">
                    <h3>👥 User Registrations</h3>
                    <span class="trend-indicator trend-up">↗️ +15%</span>
                </div>
                <div class="chart-container">
                    <canvas id="userRegistrationChart"></canvas>
                </div>
            </div>

            <!-- Appointments Chart -->
            <div class="report-card">
                <div class="report-header">
                    <h3>📅 Appointment Trends</h3>
                    <span class="trend-indicator trend-up">↗️ +8%</span>
                </div>
                <div class="chart-container">
                    <canvas id="appointmentChart"></canvas>
                </div>
            </div>

            <!-- Health Records Chart -->
            <div class="report-card">
                <div class="report-header">
                    <h3>📋 Health Records</h3>
                    <span class="trend-indicator trend-stable">➡️ Stable</span>
                </div>
                <div class="chart-container">
                    <canvas id="healthRecordsChart"></canvas>
                </div>
            </div>

            <!-- System Usage Chart -->
            <div class="report-card">
                <div class="report-header">
                    <h3>💻 System Usage</h3>
                    <span class="trend-indicator trend-up">↗️ +22%</span>
                </div>
                <div class="chart-container">
                    <canvas id="systemUsageChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity Table -->
        <div class="data-table">
            <div class="table-header">
                <span>Date</span>
                <span>Activity</span>
                <span>User</span>
                <span>Type</span>
                <span>Status</span>
            </div>
            <div id="activity-list">
                <!-- Activity data will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Application Scripts -->
    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        // Report Management System
        let reportData = {};
        let charts = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeDateFilters();
            loadReportData();
            initializeCharts();
        });

        // Initialize date filters with default values
        function initializeDateFilters() {
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('date-from').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('date-to').value = today.toISOString().split('T')[0];
        }

        // Load report data
        async function loadReportData() {
            try {
                // Mock data for demonstration
                reportData = {
                    users: {
                        total: 1247,
                        new: 156,
                        active: 1089,
                        registrations: [45, 52, 48, 61, 55, 67, 72]
                    },
                    appointments: {
                        total: 3456,
                        completed: 2987,
                        cancelled: 234,
                        pending: 235,
                        daily: [23, 31, 28, 35, 29, 42, 38]
                    },
                    healthRecords: {
                        total: 5678,
                        recent: 234,
                        categories: [1234, 987, 876, 765, 654, 543]
                    },
                    systemUsage: {
                        pageViews: 45678,
                        sessions: 12345,
                        avgSessionTime: 8.5,
                        hourly: [12, 8, 5, 3, 2, 4, 8, 15, 25, 35, 42, 38, 45, 48, 52, 49, 46, 41, 35, 28, 22, 18, 15, 13]
                    },
                    activities: [
                        { date: '2024-01-25', activity: 'User Registration', user: 'Mary Johnson', type: 'User', status: 'Completed' },
                        { date: '2024-01-25', activity: 'Appointment Scheduled', user: 'Dr. Smith', type: 'Appointment', status: 'Confirmed' },
                        { date: '2024-01-25', activity: 'Health Record Updated', user: 'Sarah Wilson', type: 'Health', status: 'Updated' },
                        { date: '2024-01-24', activity: 'Doctor Registration', user: 'Dr. Brown', type: 'Doctor', status: 'Pending' },
                        { date: '2024-01-24', activity: 'System Backup', user: 'System', type: 'System', status: 'Completed' }
                    ]
                };

                updateStatistics();
                updateActivityTable();
                updateCharts();
            } catch (error) {
                console.error('Error loading report data:', error);
                showNotification('Error loading report data', 'error');
            }
        }

        // Update statistics
        function updateStatistics() {
            document.getElementById('total-users-stat').textContent = reportData.users.total;
            document.getElementById('total-appointments-stat').textContent = reportData.appointments.total;
            document.getElementById('total-records-stat').textContent = reportData.healthRecords.total;
            document.getElementById('active-doctors-stat').textContent = '24';
        }

        // Update activity table
        function updateActivityTable() {
            const activityList = document.getElementById('activity-list');
            activityList.innerHTML = reportData.activities.map(activity => `
                <div class="table-row">
                    <span>${formatDate(activity.date)}</span>
                    <span>${activity.activity}</span>
                    <span>${activity.user}</span>
                    <span>${activity.type}</span>
                    <span class="status-${activity.status.toLowerCase()}">${activity.status}</span>
                </div>
            `).join('');
        }

        // Initialize charts
        function initializeCharts() {
            // User Registration Chart
            const userCtx = document.getElementById('userRegistrationChart').getContext('2d');
            charts.userRegistration = new Chart(userCtx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'New Registrations',
                        data: reportData.users.registrations,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Appointment Chart
            const appointmentCtx = document.getElementById('appointmentChart').getContext('2d');
            charts.appointment = new Chart(appointmentCtx, {
                type: 'bar',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Appointments',
                        data: reportData.appointments.daily,
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Health Records Chart
            const healthCtx = document.getElementById('healthRecordsChart').getContext('2d');
            charts.healthRecords = new Chart(healthCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Prenatal', 'Postnatal', 'Vaccination', 'Growth', 'General', 'Emergency'],
                    datasets: [{
                        data: reportData.healthRecords.categories,
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#6f42c1',
                            '#fd7e14'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // System Usage Chart
            const systemCtx = document.getElementById('systemUsageChart').getContext('2d');
            charts.systemUsage = new Chart(systemCtx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                    datasets: [{
                        label: 'Active Users',
                        data: reportData.systemUsage.hourly,
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // Update charts with new data
        function updateCharts() {
            Object.values(charts).forEach(chart => {
                if (chart) {
                    chart.update();
                }
            });
        }

        // Filter functions
        function updateReports() {
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;
            const reportType = document.getElementById('report-type').value;

            console.log('Updating reports with filters:', { dateFrom, dateTo, reportType });
            
            // In a real application, you would filter the data based on these parameters
            loadReportData();
        }

        function resetFilters() {
            initializeDateFilters();
            document.getElementById('report-type').value = 'all';
            updateReports();
        }

        function generateReport() {
            showNotification('Refreshing report data...', 'info');
            loadReportData();
        }

        // Export functions
        function exportToPDF() {
            showNotification('Generating PDF report...', 'info');
            // In a real application, you would generate and download a PDF
            setTimeout(() => {
                showNotification('PDF report generated successfully!', 'success');
            }, 2000);
        }

        function exportToExcel() {
            showNotification('Generating Excel report...', 'info');
            // In a real application, you would generate and download an Excel file
            setTimeout(() => {
                showNotification('Excel report generated successfully!', 'success');
            }, 2000);
        }

        function exportToCSV() {
            showNotification('Generating CSV report...', 'info');
            // In a real application, you would generate and download a CSV file
            setTimeout(() => {
                showNotification('CSV report generated successfully!', 'success');
            }, 2000);
        }

        function scheduleReport() {
            const schedule = prompt('Enter schedule (daily/weekly/monthly):');
            if (schedule) {
                showNotification(`Report scheduled ${schedule}!`, 'success');
            }
        }

        // Utility functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 2rem;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                border-radius: 4px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
