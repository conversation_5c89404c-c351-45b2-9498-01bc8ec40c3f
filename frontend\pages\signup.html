<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Preg and Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }
        
        body {
            font-family: 'Segoe <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #ffeef7 0%, #f0f9ff 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .signup-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            position: relative;
            overflow: hidden;
        }
        
        .signup-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo i {
            font-size: 3rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }
        
        .logo h1 {
            color: var(--dark);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .logo p {
            color: var(--gray);
            font-size: 1rem;
        }
        
        .form-title {
            text-align: center;
            color: var(--dark);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-group label i {
            color: var(--primary);
            width: 20px;
        }
        
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 12px;
            font-size: 1rem;
            transition: var(--transition);
            background: #fafafa;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .strength-bar {
            height: 4px;
            background: var(--light-gray);
            border-radius: 2px;
            margin: 0.5rem 0;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            width: 0%;
            transition: var(--transition);
            border-radius: 2px;
        }
        
        .strength-weak { background: #f44336; width: 25%; }
        .strength-fair { background: #ff9800; width: 50%; }
        .strength-good { background: #2196f3; width: 75%; }
        .strength-strong { background: #4caf50; width: 100%; }
        
        .btn-signup {
            width: 100%;
            padding: 1rem;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .btn-signup:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
        }
        
        .btn-signup:disabled {
            background: var(--gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .login-link {
            text-align: center;
            color: var(--gray);
        }
        
        .login-link a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .terms {
            font-size: 0.875rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }
        
        .terms a {
            color: var(--primary);
            text-decoration: none;
        }
        
        .terms a:hover {
            text-decoration: underline;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .signup-container {
                padding: 2rem;
            }
            
            .form-title {
                font-size: 1.5rem;
            }
            
            .logo h1 {
                font-size: 1.5rem;
            }
        }
        
        /* Animation */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .signup-container {
            animation: slideIn 0.6s ease-out;
        }
        
        /* Message styles */
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="logo">
            <i class="fas fa-baby-carriage"></i>
            <h1>Preg and Baby Care</h1>
            <p>Your trusted pregnancy & baby care assistant</p>
        </div>
        
        <h2 class="form-title">Create Account</h2>
        
        <form class="signup-form" id="signupForm">
            <div class="form-group">
                <label for="fullName">
                    <i class="fas fa-user"></i>
                    Full Name
                </label>
                <input type="text" id="fullName" name="fullName" required>
            </div>
            
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" id="password" name="password" required>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <span id="strengthText">Enter a password</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">
                    <i class="fas fa-lock"></i>
                    Confirm Password
                </label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <div class="terms">
                By creating an account, you agree to our 
                <a href="#">Terms of Service</a> and 
                <a href="#">Privacy Policy</a>
            </div>
            
            <button type="submit" class="btn-signup">
                <i class="fas fa-user-plus"></i> Create Account
            </button>
            
            <div class="login-link">
                Already have an account? <a href="login.html">Sign In</a>
            </div>
        </form>
    </div>

    <!-- Include API Client -->
    <script src="../js/api-client.js"></script>

    <script>
        // Global variables for debugging
        let apiClientReady = false;
        let systemStatus = {
            apiClient: false,
            backend: false,
            frontend: true
        };

        // Enhanced API client loading
        function waitForAPIClient(maxAttempts = 100) {
            return new Promise((resolve, reject) => {
                let attempts = 0;

                function checkAPIClient() {
                    attempts++;
                    console.log(`🔍 Checking API Client (attempt ${attempts}/${maxAttempts})`);

                    if (typeof apiClient !== 'undefined' && apiClient.baseURL) {
                        console.log('✅ API Client loaded successfully');
                        apiClientReady = true;
                        systemStatus.apiClient = true;
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        console.error('❌ API Client failed to load after maximum attempts');
                        reject(new Error('API Client failed to load'));
                    } else {
                        setTimeout(checkAPIClient, 50);
                    }
                }

                checkAPIClient();
            });
        }

        // Test backend connectivity
        async function testBackendConnectivity() {
            try {
                console.log('🔍 Testing backend connectivity...');
                const response = await fetch('http://localhost:5000/api/health', { timeout: 5000 });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Backend connected:', data.message);
                    systemStatus.backend = true;
                    return true;
                } else {
                    console.error('❌ Backend responded with error:', response.status);
                    systemStatus.backend = false;
                    return false;
                }
            } catch (error) {
                console.error('❌ Backend connectivity failed:', error.message);
                systemStatus.backend = false;
                return false;
            }
        }

        // Enhanced authentication check
        async function checkInitialAuth() {
            try {
                console.log('🚀 Starting signup page initialization...');

                // Test backend connectivity
                const backendOk = await testBackendConnectivity();
                if (!backendOk) {
                    showMessage('Backend server is not responding. Please ensure the backend is running on port 5000.', 'error');
                    return;
                }

                // Wait for API client
                await waitForAPIClient();

                // Don't auto-redirect if already authenticated - let user choose
                if (apiClient.isAuthenticated()) {
                    console.log('ℹ️ User already authenticated');
                    showMessage('You are already logged in. You can proceed to signup a new account or go to home.', 'info');
                } else {
                    console.log('ℹ️ User not authenticated, signup form ready');
                    showMessage('Signup form ready. Please fill in your details.', 'info');
                }

            } catch (error) {
                console.error('❌ Error during signup page initialization:', error);
                showMessage('Error initializing signup system. Please refresh the page.', 'error');
            }
        }

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Signup page loaded');

            setTimeout(() => {
                checkInitialAuth();
            }, 500);
        });

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            let strength = 0;
            let text = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthFill.className = 'strength-fill';
            
            switch (strength) {
                case 0:
                case 1:
                    strengthFill.classList.add('strength-weak');
                    text = 'Weak password';
                    break;
                case 2:
                    strengthFill.classList.add('strength-fair');
                    text = 'Fair password';
                    break;
                case 3:
                case 4:
                    strengthFill.classList.add('strength-good');
                    text = 'Good password';
                    break;
                case 5:
                    strengthFill.classList.add('strength-strong');
                    text = 'Strong password';
                    break;
            }
            
            strengthText.textContent = text;
        });

        // Form submission
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fullName = document.getElementById('fullName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // Validation
            if (!fullName || !email || !password || !confirmPassword) {
                showMessage('Please fill in all fields', 'error');
                return;
            }
            
            if (password !== confirmPassword) {
                showMessage('Passwords do not match', 'error');
                return;
            }
            
            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long', 'error');
                return;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('.btn-signup');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating account...';
            submitBtn.disabled = true;
            
            try {
                console.log('📝 Registration attempt for:', email);
                console.log('📊 System Status:', systemStatus);

                let registrationData;

                // Try API client first if available
                if (apiClientReady && typeof apiClient !== 'undefined') {
                    console.log('🔑 Using API Client for registration...');
                    try {
                        registrationData = await apiClient.register({
                            email,
                            password,
                            full_name: fullName,
                            role: 'user'
                        });
                        console.log('✅ API Client registration successful:', registrationData.user);
                    } catch (apiError) {
                        console.warn('⚠️ API Client registration failed, trying fallback:', apiError.message);
                        throw apiError; // Re-throw to trigger fallback
                    }
                } else {
                    console.log('🔧 API Client not ready, using fallback method...');
                    throw new Error('API Client not available'); // Trigger fallback
                }

                // If we get here, API client registration was successful
                if (!registrationData) {
                    throw new Error('No registration data received');
                }

                showMessage('Account created successfully! Redirecting...', 'success');

                // Redirect after successful registration
                setTimeout(() => {
                    window.location.href = '../home.html';
                }, 1500);

            } catch (error) {
                console.error('❌ Primary registration method failed:', error);

                // Try fallback direct API call
                try {
                    console.log('🔧 Attempting fallback registration...');

                    const response = await fetch('/api/register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            email,
                            password,
                            full_name: fullName,
                            role: 'user'
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || `HTTP ${response.status}`);
                    }

                    const fallbackData = await response.json();
                    console.log('✅ Fallback registration successful:', fallbackData.user);

                    // Manually store token and user data
                    localStorage.setItem('access_token', fallbackData.access_token);
                    localStorage.setItem('user_data', JSON.stringify(fallbackData.user));

                    showMessage('Account created successfully! Redirecting...', 'success');

                    // Redirect after successful registration
                    setTimeout(() => {
                        window.location.href = '../home.html';
                    }, 1500);

                    return; // Exit successfully

                } catch (fallbackError) {
                    console.error('❌ Fallback registration also failed:', fallbackError);

                    // Provide specific error messages
                    let errorMessage = 'Registration failed. Please try again.';

                    if (fallbackError.message.includes('already exists') || fallbackError.message.includes('409')) {
                        errorMessage = 'An account with this email already exists. Please use a different email or try logging in.';
                    } else if (fallbackError.message.includes('Network') || fallbackError.message.includes('fetch')) {
                        errorMessage = 'Network error. Please check your connection and try again.';
                    } else if (fallbackError.message.includes('500')) {
                        errorMessage = 'Server error. Please try again later.';
                    } else if (fallbackError.message) {
                        errorMessage = fallbackError.message;
                    }

                    showMessage(errorMessage, 'error');
                }

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });
        
        // Show message function
        function showMessage(message, type) {
            // Remove existing messages
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            const form = document.querySelector('.signup-form');
            form.insertBefore(messageDiv, form.firstChild);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
