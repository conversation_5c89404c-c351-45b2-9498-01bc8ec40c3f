<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Preparation - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">💉 MCHS - Vaccination Preparation</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Vaccination Preparation</h1>
                <p class="page-description">Prepare for your baby's vaccinations with comprehensive guides and checklists</p>
            </div>

            <!-- Vaccination Categories -->
            <div class="vaccination-categories mb-4">
                <div class="category-tabs">
                    <button class="category-tab active" onclick="showCategory('schedule')">Vaccination Schedule</button>
                    <button class="category-tab" onclick="showCategory('preparation')">Preparation Guide</button>
                    <button class="category-tab" onclick="showCategory('aftercare')">After Vaccination</button>
                    <button class="category-tab" onclick="showCategory('myths')">Myths & Facts</button>
                </div>
            </div>

            <!-- Vaccination Schedule Tab -->
            <div id="schedule-tab" class="vaccination-tab-content active">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Recommended Vaccination Schedule</h3>
                                <p class="text-secondary">WHO and CDC recommended vaccination timeline</p>
                            </div>
                            <div class="card-body">
                                <div class="vaccination-timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-marker birth">Birth</div>
                                        <div class="timeline-content">
                                            <h4>At Birth</h4>
                                            <div class="vaccines-list">
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hepatitis B (HepB)</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                            </div>
                                            <button onclick="showVaccineDetails('birth')" class="btn btn-sm btn-outline">View Details</button>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-marker">2 months</div>
                                        <div class="timeline-content">
                                            <h4>2 Months</h4>
                                            <div class="vaccines-list">
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">DTaP</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">IPV</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hib</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">PCV13</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">RV</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">HepB</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                            </div>
                                            <button onclick="showVaccineDetails('2months')" class="btn btn-sm btn-outline">View Details</button>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-marker">4 months</div>
                                        <div class="timeline-content">
                                            <h4>4 Months</h4>
                                            <div class="vaccines-list">
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">DTaP</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">IPV</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hib</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">PCV13</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">RV</span>
                                                    <span class="vaccine-dose">2nd dose</span>
                                                </div>
                                            </div>
                                            <button onclick="showVaccineDetails('4months')" class="btn btn-sm btn-outline">View Details</button>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-marker">6 months</div>
                                        <div class="timeline-content">
                                            <h4>6 Months</h4>
                                            <div class="vaccines-list">
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">DTaP</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">IPV</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hib</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">PCV13</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">RV</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">HepB</span>
                                                    <span class="vaccine-dose">3rd dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Influenza</span>
                                                    <span class="vaccine-dose">Annual</span>
                                                </div>
                                            </div>
                                            <button onclick="showVaccineDetails('6months')" class="btn btn-sm btn-outline">View Details</button>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-marker">12 months</div>
                                        <div class="timeline-content">
                                            <h4>12-15 Months</h4>
                                            <div class="vaccines-list">
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">MMR</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Varicella</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hib</span>
                                                    <span class="vaccine-dose">4th dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">PCV13</span>
                                                    <span class="vaccine-dose">4th dose</span>
                                                </div>
                                                <div class="vaccine-item">
                                                    <span class="vaccine-name">Hepatitis A</span>
                                                    <span class="vaccine-dose">1st dose</span>
                                                </div>
                                            </div>
                                            <button onclick="showVaccineDetails('12months')" class="btn btn-sm btn-outline">View Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Next Vaccination</h3>
                            </div>
                            <div class="card-body">
                                <div id="next-vaccination" class="next-vaccination">
                                    <!-- Next vaccination info will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-body">
                                <button onclick="scheduleVaccination()" class="btn btn-primary btn-block mb-2">
                                    📅 Schedule Vaccination
                                </button>
                                <button onclick="setReminder()" class="btn btn-outline btn-block mb-2">
                                    ⏰ Set Reminder
                                </button>
                                <button onclick="downloadSchedule()" class="btn btn-outline btn-block">
                                    📄 Download Schedule
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preparation Guide Tab -->
            <div id="preparation-tab" class="vaccination-tab-content">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Before the Appointment</h3>
                            </div>
                            <div class="card-body">
                                <div class="preparation-checklist">
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep1">
                                        <label for="prep1">Review vaccination schedule with your doctor</label>
                                    </div>
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep2">
                                        <label for="prep2">Bring your baby's vaccination record</label>
                                    </div>
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep3">
                                        <label for="prep3">Prepare questions about vaccines</label>
                                    </div>
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep4">
                                        <label for="prep4">Ensure baby is well (no fever or illness)</label>
                                    </div>
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep5">
                                        <label for="prep5">Bring comfort items (favorite toy, blanket)</label>
                                    </div>
                                    <div class="checklist-item">
                                        <input type="checkbox" id="prep6">
                                        <label for="prep6">Plan for post-vaccination care</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>What to Expect</h3>
                            </div>
                            <div class="card-body">
                                <div class="expectation-list">
                                    <div class="expectation-item">
                                        <div class="expectation-icon">💉</div>
                                        <div class="expectation-content">
                                            <h5>Multiple Shots</h5>
                                            <p>Your baby may receive several vaccines in one visit</p>
                                        </div>
                                    </div>
                                    <div class="expectation-item">
                                        <div class="expectation-icon">😢</div>
                                        <div class="expectation-content">
                                            <h5>Crying</h5>
                                            <p>Brief crying is normal and expected</p>
                                        </div>
                                    </div>
                                    <div class="expectation-item">
                                        <div class="expectation-icon">🩹</div>
                                        <div class="expectation-content">
                                            <h5>Injection Site</h5>
                                            <p>Mild redness or swelling at injection site</p>
                                        </div>
                                    </div>
                                    <div class="expectation-item">
                                        <div class="expectation-icon">📋</div>
                                        <div class="expectation-content">
                                            <h5>Documentation</h5>
                                            <p>Updated vaccination record</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h3>Comfort Strategies</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="comfort-strategy">
                                    <div class="strategy-icon">🤱</div>
                                    <h5>During Injection</h5>
                                    <ul>
                                        <li>Hold your baby securely</li>
                                        <li>Make eye contact and talk softly</li>
                                        <li>Breastfeed if possible</li>
                                        <li>Stay calm and reassuring</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="comfort-strategy">
                                    <div class="strategy-icon">🍼</div>
                                    <h5>Feeding</h5>
                                    <ul>
                                        <li>Feed before appointment</li>
                                        <li>Bring bottle or be ready to breastfeed</li>
                                        <li>Sugar water may help (ask doctor)</li>
                                        <li>Avoid feeding immediately after</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="comfort-strategy">
                                    <div class="strategy-icon">🧸</div>
                                    <h5>Distraction</h5>
                                    <ul>
                                        <li>Bring favorite toy</li>
                                        <li>Sing or play music</li>
                                        <li>Use colorful objects</li>
                                        <li>Gentle rocking motion</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- After Vaccination Tab -->
            <div id="aftercare-tab" class="vaccination-tab-content">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>Normal Reactions</h3>
                            </div>
                            <div class="card-body">
                                <div class="reaction-list">
                                    <div class="reaction-item normal">
                                        <div class="reaction-icon">🌡️</div>
                                        <div class="reaction-content">
                                            <h5>Low-grade Fever</h5>
                                            <p>Temperature up to 101°F (38.3°C)</p>
                                            <span class="duration">Duration: 1-2 days</span>
                                        </div>
                                    </div>
                                    <div class="reaction-item normal">
                                        <div class="reaction-icon">🔴</div>
                                        <div class="reaction-content">
                                            <h5>Injection Site Redness</h5>
                                            <p>Mild redness and swelling</p>
                                            <span class="duration">Duration: 2-3 days</span>
                                        </div>
                                    </div>
                                    <div class="reaction-item normal">
                                        <div class="reaction-icon">😴</div>
                                        <div class="reaction-content">
                                            <h5>Drowsiness</h5>
                                            <p>More sleepy than usual</p>
                                            <span class="duration">Duration: 1-2 days</span>
                                        </div>
                                    </div>
                                    <div class="reaction-item normal">
                                        <div class="reaction-icon">😢</div>
                                        <div class="reaction-content">
                                            <h5>Fussiness</h5>
                                            <p>More irritable than usual</p>
                                            <span class="duration">Duration: 1-2 days</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3>When to Call Doctor</h3>
                            </div>
                            <div class="card-body">
                                <div class="warning-signs">
                                    <div class="warning-item">
                                        <div class="warning-icon">🚨</div>
                                        <div class="warning-content">
                                            <h5>High Fever</h5>
                                            <p>Temperature above 104°F (40°C)</p>
                                        </div>
                                    </div>
                                    <div class="warning-item">
                                        <div class="warning-icon">😵</div>
                                        <div class="warning-content">
                                            <h5>Severe Reactions</h5>
                                            <p>Difficulty breathing, swelling, or seizures</p>
                                        </div>
                                    </div>
                                    <div class="warning-item">
                                        <div class="warning-icon">⏰</div>
                                        <div class="warning-content">
                                            <h5>Prolonged Symptoms</h5>
                                            <p>Symptoms lasting more than 3 days</p>
                                        </div>
                                    </div>
                                    <div class="warning-item">
                                        <div class="warning-icon">🍼</div>
                                        <div class="warning-content">
                                            <h5>Feeding Issues</h5>
                                            <p>Refusing to eat for more than 24 hours</p>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="contactDoctor()" class="btn btn-danger btn-block mt-3">
                                    📞 Contact Doctor Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h3>Post-Vaccination Care</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="care-section">
                                    <h5>💊 Pain Relief</h5>
                                    <ul>
                                        <li>Acetaminophen or ibuprofen (as directed)</li>
                                        <li>Cool compress on injection site</li>
                                        <li>Gentle massage around area</li>
                                        <li>Extra cuddles and comfort</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="care-section">
                                    <h5>🏠 Home Care</h5>
                                    <ul>
                                        <li>Monitor temperature regularly</li>
                                        <li>Keep injection site clean and dry</li>
                                        <li>Offer extra fluids</li>
                                        <li>Maintain normal routine</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="care-section">
                                    <h5>📝 Record Keeping</h5>
                                    <ul>
                                        <li>Update vaccination record</li>
                                        <li>Note any reactions</li>
                                        <li>Schedule next appointment</li>
                                        <li>Take photos of injection site if concerned</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Myths & Facts Tab -->
            <div id="myths-tab" class="vaccination-tab-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Vaccination Myths vs Facts</h3>
                        <p class="text-secondary">Get accurate information about vaccines</p>
                    </div>
                    <div class="card-body">
                        <div class="myths-facts-list">
                            <div class="myth-fact-item">
                                <div class="myth-section">
                                    <h5 class="myth-title">❌ Myth</h5>
                                    <p>"Vaccines cause autism"</p>
                                </div>
                                <div class="fact-section">
                                    <h5 class="fact-title">✅ Fact</h5>
                                    <p>Extensive research has shown no link between vaccines and autism. The original study claiming this connection was fraudulent and has been retracted.</p>
                                </div>
                            </div>

                            <div class="myth-fact-item">
                                <div class="myth-section">
                                    <h5 class="myth-title">❌ Myth</h5>
                                    <p>"Natural immunity is better than vaccine immunity"</p>
                                </div>
                                <div class="fact-section">
                                    <h5 class="fact-title">✅ Fact</h5>
                                    <p>While natural infection can provide immunity, it comes with serious risks. Vaccines provide immunity without the dangerous complications of diseases.</p>
                                </div>
                            </div>

                            <div class="myth-fact-item">
                                <div class="myth-section">
                                    <h5 class="myth-title">❌ Myth</h5>
                                    <p>"Too many vaccines overwhelm the immune system"</p>
                                </div>
                                <div class="fact-section">
                                    <h5 class="fact-title">✅ Fact</h5>
                                    <p>Babies' immune systems can handle many vaccines safely. The antigens in vaccines are a tiny fraction of what babies encounter daily.</p>
                                </div>
                            </div>

                            <div class="myth-fact-item">
                                <div class="myth-section">
                                    <h5 class="myth-title">❌ Myth</h5>
                                    <p>"Vaccines contain dangerous chemicals"</p>
                                </div>
                                <div class="fact-section">
                                    <h5 class="fact-title">✅ Fact</h5>
                                    <p>Vaccine ingredients are safe in the amounts used. Many substances that sound scary are actually found naturally in the body or environment.</p>
                                </div>
                            </div>

                            <div class="myth-fact-item">
                                <div class="myth-section">
                                    <h5 class="myth-title">❌ Myth</h5>
                                    <p>"Diseases were already declining before vaccines"</p>
                                </div>
                                <div class="fact-section">
                                    <h5 class="fact-title">✅ Fact</h5>
                                    <p>While some disease deaths decreased due to better healthcare, the dramatic decline in disease cases directly correlates with vaccine introduction.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Vaccine Details Modal -->
    <div id="vaccine-details-modal" class="modal">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h3 id="vaccine-details-title">Vaccine Details</h3>
                <span class="close" onclick="closeVaccineModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="vaccine-details-content">
                    <!-- Vaccine details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNextVaccination();
            loadPreparationProgress();
        });

        function showCategory(category) {
            // Hide all tab contents
            document.querySelectorAll('.vaccination-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all buttons
            document.querySelectorAll('.category-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab and activate button
            document.getElementById(`${category}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        async function loadNextVaccination() {
            try {
                const response = await apiCall('/mother/baby/next-vaccination', 'GET');
                if (response.success) {
                    displayNextVaccination(response.data);
                }
            } catch (error) {
                console.error('Error loading next vaccination:', error);
                document.getElementById('next-vaccination').innerHTML = 
                    '<div class="text-center text-secondary">Unable to load vaccination information</div>';
            }
        }

        function displayNextVaccination(vaccination) {
            const container = document.getElementById('next-vaccination');
            
            if (!vaccination) {
                container.innerHTML = `
                    <div class="text-center">
                        <div class="next-vaccine-icon">✅</div>
                        <h4>Up to Date!</h4>
                        <p>Your baby is current with all scheduled vaccinations.</p>
                    </div>
                `;
                return;
            }

            const daysUntil = Math.ceil((new Date(vaccination.due_date) - new Date()) / (1000 * 60 * 60 * 24));
            
            container.innerHTML = `
                <div class="next-vaccine-info">
                    <div class="vaccine-age">${vaccination.age}</div>
                    <h4>${vaccination.vaccines.join(', ')}</h4>
                    <div class="due-date">
                        <strong>Due: ${formatDate(vaccination.due_date)}</strong>
                        <span class="days-until">${daysUntil > 0 ? `in ${daysUntil} days` : 'Overdue'}</span>
                    </div>
                    <div class="vaccine-count">${vaccination.vaccines.length} vaccine${vaccination.vaccines.length > 1 ? 's' : ''}</div>
                </div>
            `;
        }

        function showVaccineDetails(ageGroup) {
            const vaccineDetails = {
                'birth': {
                    title: 'Birth Vaccinations',
                    vaccines: [
                        {
                            name: 'Hepatitis B (HepB)',
                            description: 'Protects against hepatitis B virus infection',
                            sideEffects: 'Mild soreness at injection site',
                            importance: 'Prevents serious liver infection'
                        }
                    ]
                },
                '2months': {
                    title: '2 Month Vaccinations',
                    vaccines: [
                        {
                            name: 'DTaP (Diphtheria, Tetanus, Pertussis)',
                            description: 'Protects against three serious diseases',
                            sideEffects: 'Mild fever, fussiness, soreness',
                            importance: 'Prevents potentially fatal diseases'
                        },
                        {
                            name: 'IPV (Inactivated Poliovirus)',
                            description: 'Protects against polio',
                            sideEffects: 'Minimal side effects',
                            importance: 'Prevents paralysis from polio'
                        }
                        // Add more vaccines...
                    ]
                }
                // Add more age groups...
            };

            const details = vaccineDetails[ageGroup];
            if (!details) return;

            document.getElementById('vaccine-details-title').textContent = details.title;
            
            const content = details.vaccines.map(vaccine => `
                <div class="vaccine-detail-card">
                    <h4>${vaccine.name}</h4>
                    <div class="vaccine-info">
                        <div class="info-section">
                            <h5>What it protects against:</h5>
                            <p>${vaccine.description}</p>
                        </div>
                        <div class="info-section">
                            <h5>Common side effects:</h5>
                            <p>${vaccine.sideEffects}</p>
                        </div>
                        <div class="info-section">
                            <h5>Why it's important:</h5>
                            <p>${vaccine.importance}</p>
                        </div>
                    </div>
                </div>
            `).join('');

            document.getElementById('vaccine-details-content').innerHTML = content;
            document.getElementById('vaccine-details-modal').style.display = 'block';
        }

        function closeVaccineModal() {
            document.getElementById('vaccine-details-modal').style.display = 'none';
        }

        function loadPreparationProgress() {
            // Load saved checklist progress from localStorage
            const savedProgress = localStorage.getItem('vaccination-prep-progress');
            if (savedProgress) {
                const progress = JSON.parse(savedProgress);
                Object.keys(progress).forEach(id => {
                    const checkbox = document.getElementById(id);
                    if (checkbox) {
                        checkbox.checked = progress[id];
                    }
                });
            }

            // Add event listeners to save progress
            document.querySelectorAll('.preparation-checklist input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', savePreparationProgress);
            });
        }

        function savePreparationProgress() {
            const progress = {};
            document.querySelectorAll('.preparation-checklist input[type="checkbox"]').forEach(checkbox => {
                progress[checkbox.id] = checkbox.checked;
            });
            localStorage.setItem('vaccination-prep-progress', JSON.stringify(progress));
        }

        function scheduleVaccination() {
            window.location.href = '../baby/vaccinations.html';
        }

        function setReminder() {
            showNotification('Vaccination reminder set successfully!', 'success');
        }

        function downloadSchedule() {
            showNotification('Downloading vaccination schedule...', 'info');
            // Implementation would generate and download PDF
        }

        function contactDoctor() {
            window.location.href = 'chat.html';
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
