/**
 * Quality Assurance System
 * Bug tracking, issue reporting, and quality monitoring
 */

class QASystem {
    constructor() {
        this.bugs = this.loadBugs();
        this.qualityMetrics = {};
        this.errorLog = [];
        this.performanceMetrics = {};
        
        this.initializeErrorTracking();
        this.initializePerformanceMonitoring();
        this.initializeQualityChecks();
    }

    // Initialize error tracking
    initializeErrorTracking() {
        // Global error handler
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'javascript_error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent
            });
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'unhandled_promise_rejection',
                message: event.reason?.message || 'Unhandled promise rejection',
                stack: event.reason?.stack,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent
            });
        });

        // Console error override
        const originalConsoleError = console.error;
        console.error = (...args) => {
            this.logError({
                type: 'console_error',
                message: args.join(' '),
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
            originalConsoleError.apply(console, args);
        };
    }

    // Initialize performance monitoring
    initializePerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.collectPerformanceMetrics();
            }, 1000);
        });

        // Monitor resource loading
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > 2000) {
                        this.logPerformanceIssue({
                            type: 'slow_resource',
                            name: entry.name,
                            duration: entry.duration,
                            timestamp: new Date().toISOString()
                        });
                    }
                }
            });
            observer.observe({ entryTypes: ['resource'] });
        }
    }

    // Initialize quality checks
    initializeQualityChecks() {
        // Run quality checks after page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                this.runQualityChecks();
            }, 2000);
        });
    }

    // Log error
    logError(error) {
        this.errorLog.push(error);
        
        // Keep only last 100 errors
        if (this.errorLog.length > 100) {
            this.errorLog.splice(0, this.errorLog.length - 100);
        }
        
        this.saveErrorLog();
        
        // Auto-create bug report for critical errors
        if (this.isCriticalError(error)) {
            this.createBugReport({
                title: `Critical Error: ${error.message}`,
                description: `Automatic bug report for critical error:\n\n${JSON.stringify(error, null, 2)}`,
                severity: 'critical',
                category: 'error',
                status: 'open',
                auto_generated: true
            });
        }
    }

    // Check if error is critical
    isCriticalError(error) {
        const criticalPatterns = [
            /cannot read property/i,
            /is not a function/i,
            /network error/i,
            /authentication/i,
            /permission denied/i
        ];
        
        return criticalPatterns.some(pattern => pattern.test(error.message));
    }

    // Log performance issue
    logPerformanceIssue(issue) {
        if (!this.performanceMetrics.issues) {
            this.performanceMetrics.issues = [];
        }
        
        this.performanceMetrics.issues.push(issue);
        this.savePerformanceMetrics();
    }

    // Collect performance metrics
    collectPerformanceMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        this.performanceMetrics = {
            ...this.performanceMetrics,
            pageLoad: {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                totalTime: navigation.loadEventEnd - navigation.fetchStart,
                firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
            },
            memory: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null,
            timestamp: new Date().toISOString()
        };
        
        this.savePerformanceMetrics();
    }

    // Run quality checks
    runQualityChecks() {
        const checks = {
            accessibility: this.checkAccessibility(),
            performance: this.checkPerformance(),
            seo: this.checkSEO(),
            security: this.checkSecurity(),
            usability: this.checkUsability()
        };
        
        this.qualityMetrics = {
            ...checks,
            timestamp: new Date().toISOString(),
            overallScore: this.calculateOverallScore(checks)
        };
        
        this.saveQualityMetrics();
        return this.qualityMetrics;
    }

    // Check accessibility
    checkAccessibility() {
        const issues = [];
        let score = 100;
        
        // Check for alt text on images
        const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
        if (imagesWithoutAlt.length > 0) {
            issues.push(`${imagesWithoutAlt.length} images missing alt text`);
            score -= imagesWithoutAlt.length * 5;
        }
        
        // Check for form labels
        const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
        const unlabeledInputs = Array.from(inputsWithoutLabels).filter(input => {
            return input.type !== 'hidden' && !input.labels?.length;
        });
        if (unlabeledInputs.length > 0) {
            issues.push(`${unlabeledInputs.length} form inputs missing labels`);
            score -= unlabeledInputs.length * 10;
        }
        
        // Check color contrast (simplified)
        const elements = document.querySelectorAll('*');
        let lowContrastElements = 0;
        for (const element of elements) {
            const styles = getComputedStyle(element);
            if (styles.color && styles.backgroundColor) {
                // Simplified contrast check
                if (this.isLowContrast(styles.color, styles.backgroundColor)) {
                    lowContrastElements++;
                }
            }
        }
        if (lowContrastElements > 0) {
            issues.push(`${lowContrastElements} elements may have low color contrast`);
            score -= Math.min(lowContrastElements * 2, 20);
        }
        
        return {
            score: Math.max(score, 0),
            issues,
            passed: issues.length === 0
        };
    }

    // Check performance
    checkPerformance() {
        const issues = [];
        let score = 100;
        
        // Check page load time
        if (this.performanceMetrics.pageLoad) {
            const loadTime = this.performanceMetrics.pageLoad.totalTime;
            if (loadTime > 3000) {
                issues.push(`Slow page load time: ${loadTime.toFixed(0)}ms`);
                score -= 20;
            }
        }
        
        // Check memory usage
        if (this.performanceMetrics.memory) {
            const memoryUsageMB = this.performanceMetrics.memory.used / 1024 / 1024;
            if (memoryUsageMB > 50) {
                issues.push(`High memory usage: ${memoryUsageMB.toFixed(1)}MB`);
                score -= 15;
            }
        }
        
        // Check DOM complexity
        const elementCount = document.querySelectorAll('*').length;
        if (elementCount > 1000) {
            issues.push(`Complex DOM: ${elementCount} elements`);
            score -= 10;
        }
        
        return {
            score: Math.max(score, 0),
            issues,
            passed: issues.length === 0
        };
    }

    // Check SEO
    checkSEO() {
        const issues = [];
        let score = 100;
        
        // Check title tag
        const title = document.querySelector('title');
        if (!title || title.textContent.length < 10) {
            issues.push('Missing or too short title tag');
            score -= 20;
        }
        
        // Check meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (!metaDescription || metaDescription.content.length < 50) {
            issues.push('Missing or too short meta description');
            score -= 15;
        }
        
        // Check heading structure
        const h1s = document.querySelectorAll('h1');
        if (h1s.length === 0) {
            issues.push('Missing H1 tag');
            score -= 15;
        } else if (h1s.length > 1) {
            issues.push('Multiple H1 tags found');
            score -= 10;
        }
        
        return {
            score: Math.max(score, 0),
            issues,
            passed: issues.length === 0
        };
    }

    // Check security
    checkSecurity() {
        const issues = [];
        let score = 100;
        
        // Check for HTTPS
        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
            issues.push('Site not served over HTTPS');
            score -= 30;
        }
        
        // Check for authentication
        if (!window.authService) {
            issues.push('Authentication service not found');
            score -= 20;
        }
        
        // Check for CSRF protection
        if (!localStorage.getItem('csrf_token')) {
            issues.push('CSRF protection not implemented');
            score -= 15;
        }
        
        return {
            score: Math.max(score, 0),
            issues,
            passed: issues.length === 0
        };
    }

    // Check usability
    checkUsability() {
        const issues = [];
        let score = 100;
        
        // Check for responsive design
        const viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            issues.push('Missing viewport meta tag');
            score -= 15;
        }
        
        // Check for loading indicators
        const loadingElements = document.querySelectorAll('.loading, .spinner, [data-loading]');
        if (loadingElements.length === 0) {
            issues.push('No loading indicators found');
            score -= 10;
        }
        
        // Check for error handling
        if (this.errorLog.length > 10) {
            issues.push(`High error count: ${this.errorLog.length} errors`);
            score -= 20;
        }
        
        return {
            score: Math.max(score, 0),
            issues,
            passed: issues.length === 0
        };
    }

    // Calculate overall quality score
    calculateOverallScore(checks) {
        const scores = Object.values(checks).map(check => check.score);
        return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
    }

    // Check if colors have low contrast (simplified)
    isLowContrast(color1, color2) {
        // This is a simplified check - in production, use a proper contrast ratio calculation
        return false;
    }

    // Create bug report
    createBugReport(bugData) {
        const bug = {
            id: Date.now() + Math.random(),
            ...bugData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        this.bugs.push(bug);
        this.saveBugs();
        
        return bug;
    }

    // Update bug report
    updateBugReport(bugId, updates) {
        const bugIndex = this.bugs.findIndex(bug => bug.id === bugId);
        if (bugIndex !== -1) {
            this.bugs[bugIndex] = {
                ...this.bugs[bugIndex],
                ...updates,
                updated_at: new Date().toISOString()
            };
            this.saveBugs();
            return this.bugs[bugIndex];
        }
        return null;
    }

    // Get bugs by status
    getBugsByStatus(status) {
        return this.bugs.filter(bug => bug.status === status);
    }

    // Get bugs by severity
    getBugsBySeverity(severity) {
        return this.bugs.filter(bug => bug.severity === severity);
    }

    // Load bugs from storage
    loadBugs() {
        try {
            return JSON.parse(localStorage.getItem('qa_bugs') || '[]');
        } catch (error) {
            return [];
        }
    }

    // Save bugs to storage
    saveBugs() {
        localStorage.setItem('qa_bugs', JSON.stringify(this.bugs));
    }

    // Save error log
    saveErrorLog() {
        localStorage.setItem('qa_error_log', JSON.stringify(this.errorLog));
    }

    // Save performance metrics
    savePerformanceMetrics() {
        localStorage.setItem('qa_performance_metrics', JSON.stringify(this.performanceMetrics));
    }

    // Save quality metrics
    saveQualityMetrics() {
        localStorage.setItem('qa_quality_metrics', JSON.stringify(this.qualityMetrics));
    }

    // Generate QA report
    generateQAReport() {
        return {
            qualityMetrics: this.qualityMetrics,
            performanceMetrics: this.performanceMetrics,
            errorLog: this.errorLog,
            bugs: this.bugs,
            summary: {
                totalBugs: this.bugs.length,
                openBugs: this.getBugsByStatus('open').length,
                criticalBugs: this.getBugsBySeverity('critical').length,
                totalErrors: this.errorLog.length,
                overallQualityScore: this.qualityMetrics.overallScore || 0
            },
            timestamp: new Date().toISOString()
        };
    }
}

// Create global instance
window.qaSystem = new QASystem();

// Global helper functions
window.runQualityChecks = () => window.qaSystem.runQualityChecks();
window.createBugReport = (bugData) => window.qaSystem.createBugReport(bugData);
window.getQAReport = () => window.qaSystem.generateQAReport();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QASystem;
}
