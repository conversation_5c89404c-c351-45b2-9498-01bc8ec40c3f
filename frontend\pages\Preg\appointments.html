<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <nav class="navbar">
                <div class="d-flex items-center gap-3">
                    <a href="profile.html" class="btn btn-outline" style="padding: 0.5rem;">← Back to Profile</a>
                    <div class="navbar-brand">🤱 MCHS - Appointments</div>
                </div>
                <div class="d-flex items-center gap-3">
                    <span id="user-name" class="text-secondary"></span>
                    <button onclick="logout()" class="btn btn-outline">Logout</button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Appointments</h1>
                <div class="page-actions">
                    <button onclick="scheduleAppointment()" class="btn btn-primary">Schedule New Appointment</button>
                    <button onclick="viewCalendar()" class="btn btn-outline">Calendar View</button>
                </div>
            </div>

            <!-- Appointment Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status-filter">Status</label>
                                <select id="status-filter" class="form-control" onchange="filterAppointments()">
                                    <option value="">All Appointments</option>
                                    <option value="scheduled">Scheduled</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                    <option value="rescheduled">Rescheduled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="type-filter">Type</label>
                                <select id="type-filter" class="form-control" onchange="filterAppointments()">
                                    <option value="">All Types</option>
                                    <option value="routine_checkup">Routine Checkup</option>
                                    <option value="ultrasound">Ultrasound</option>
                                    <option value="consultation">Consultation</option>
                                    <option value="emergency">Emergency</option>
                                    <option value="follow_up">Follow-up</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date-from">From Date</label>
                                <input type="date" id="date-from" class="form-control" onchange="filterAppointments()">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date-to">To Date</label>
                                <input type="date" id="date-to" class="form-control" onchange="filterAppointments()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Appointments -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3>Upcoming Appointments</h3>
                        </div>
                        <div class="card-body">
                            <div id="upcoming-appointments">
                                <!-- Upcoming appointments will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- All Appointments -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h3>All Appointments</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date & Time</th>
                                            <th>Doctor</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="appointments-table">
                                        <!-- Appointments will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-md-4">
                    <!-- Next Appointment -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Next Appointment</h3>
                        </div>
                        <div class="card-body" id="next-appointment">
                            <!-- Next appointment details will be loaded here -->
                        </div>
                    </div>

                    <!-- Appointment Reminders -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Reminders</h3>
                        </div>
                        <div class="card-body">
                            <div class="reminder-settings">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="email-reminder" checked>
                                        Email reminders
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="sms-reminder" checked>
                                        SMS reminders
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="reminder-time">Remind me</label>
                                    <select id="reminder-time" class="form-control">
                                        <option value="24">24 hours before</option>
                                        <option value="12">12 hours before</option>
                                        <option value="6">6 hours before</option>
                                        <option value="2">2 hours before</option>
                                        <option value="1">1 hour before</option>
                                    </select>
                                </div>
                                <button onclick="saveReminderSettings()" class="btn btn-sm btn-primary">Save Settings</button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3>Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions">
                                <button onclick="scheduleRoutineCheckup()" class="btn btn-outline btn-block mb-2">Schedule Routine Checkup</button>
                                <button onclick="scheduleUltrasound()" class="btn btn-outline btn-block mb-2">Schedule Ultrasound</button>
                                <button onclick="requestEmergencyAppointment()" class="btn btn-danger btn-block">Emergency Appointment</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Schedule Appointment Modal -->
    <div id="appointment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Schedule New Appointment</h3>
                <span class="close" onclick="closeAppointmentModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="appointment-form">
                    <div class="form-group">
                        <label for="appointment_type">Appointment Type</label>
                        <select id="appointment_type" name="type" class="form-control" required>
                            <option value="">Select Type</option>
                            <option value="routine_checkup">Routine Checkup</option>
                            <option value="ultrasound">Ultrasound</option>
                            <option value="consultation">Consultation</option>
                            <option value="follow_up">Follow-up</option>
                            <option value="emergency">Emergency</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="doctor_id">Preferred Doctor</label>
                        <select id="doctor_id" name="doctor_id" class="form-control" required>
                            <option value="">Select Doctor</option>
                            <!-- Doctors will be loaded here -->
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="appointment_date">Preferred Date</label>
                                <input type="date" id="appointment_date" name="date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="appointment_time">Preferred Time</label>
                                <select id="appointment_time" name="time" class="form-control" required>
                                    <option value="">Select Time</option>
                                    <!-- Available times will be loaded based on date and doctor -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reason">Reason for Visit</label>
                        <textarea id="reason" name="reason" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="symptoms">Current Symptoms (if any)</label>
                        <textarea id="symptoms" name="symptoms" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority" class="form-control">
                            <option value="normal">Normal</option>
                            <option value="urgent">Urgent</option>
                            <option value="emergency">Emergency</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notes">Additional Notes</label>
                        <textarea id="notes" name="notes" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Schedule Appointment</button>
                        <button type="button" onclick="closeAppointmentModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Appointment Details Modal -->
    <div id="appointment-details-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Appointment Details</h3>
                <span class="close" onclick="closeAppointmentDetailsModal()">&times;</span>
            </div>
            <div class="modal-body" id="appointment-details-content">
                <!-- Appointment details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Reschedule Modal -->
    <div id="reschedule-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Reschedule Appointment</h3>
                <span class="close" onclick="closeRescheduleModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="reschedule-form">
                    <input type="hidden" id="reschedule_appointment_id" name="appointment_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_date">New Date</label>
                                <input type="date" id="new_date" name="new_date" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="new_time">New Time</label>
                                <select id="new_time" name="new_time" class="form-control" required>
                                    <option value="">Select Time</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reschedule_reason">Reason for Rescheduling</label>
                        <textarea id="reschedule_reason" name="reason" class="form-control" rows="3" required></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Reschedule</button>
                        <button type="button" onclick="closeRescheduleModal()" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        let allAppointments = [];
        let filteredAppointments = [];

        // Load data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadAppointments();
            loadDoctors();
            setMinDate();
        });

        function setMinDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('appointment_date').min = today;
            document.getElementById('new_date').min = today;
        }

        async function loadAppointments() {
            try {
                const response = await apiCall('/mother/appointments', 'GET');
                if (response.success) {
                    allAppointments = response.data;
                    filteredAppointments = [...allAppointments];
                    displayAppointments();
                    displayUpcomingAppointments();
                    displayNextAppointment();
                }
            } catch (error) {
                console.error('Error loading appointments:', error);
            }
        }

        async function loadDoctors() {
            try {
                const response = await apiCall('/doctors', 'GET');
                if (response.success) {
                    const doctorSelect = document.getElementById('doctor_id');
                    doctorSelect.innerHTML = '<option value="">Select Doctor</option>';
                    
                    response.data.forEach(doctor => {
                        const option = document.createElement('option');
                        option.value = doctor.id;
                        option.textContent = `Dr. ${doctor.full_name} - ${doctor.specialization}`;
                        doctorSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading doctors:', error);
            }
        }

        function displayAppointments() {
            const tbody = document.getElementById('appointments-table');
            tbody.innerHTML = '';
            
            filteredAppointments.forEach(appointment => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDateTime(appointment.date, appointment.time)}</td>
                    <td>Dr. ${appointment.doctor_name}</td>
                    <td><span class="badge badge-${getTypeColor(appointment.type)}">${formatType(appointment.type)}</span></td>
                    <td><span class="badge badge-${getStatusColor(appointment.status)}">${formatStatus(appointment.status)}</span></td>
                    <td>
                        <button onclick="viewAppointmentDetails('${appointment.id}')" class="btn btn-sm btn-outline">View</button>
                        ${appointment.status === 'scheduled' ? `
                            <button onclick="rescheduleAppointment('${appointment.id}')" class="btn btn-sm btn-warning">Reschedule</button>
                            <button onclick="cancelAppointment('${appointment.id}')" class="btn btn-sm btn-danger">Cancel</button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function displayUpcomingAppointments() {
            const container = document.getElementById('upcoming-appointments');
            const upcoming = allAppointments
                .filter(apt => apt.status === 'scheduled' && new Date(apt.date) >= new Date())
                .sort((a, b) => new Date(a.date) - new Date(b.date))
                .slice(0, 3);

            if (upcoming.length === 0) {
                container.innerHTML = '<p class="text-secondary">No upcoming appointments</p>';
                return;
            }

            container.innerHTML = upcoming.map(appointment => `
                <div class="appointment-card">
                    <div class="appointment-date">
                        <div class="date">${formatDate(appointment.date)}</div>
                        <div class="time">${appointment.time}</div>
                    </div>
                    <div class="appointment-details">
                        <h4>Dr. ${appointment.doctor_name}</h4>
                        <p>${formatType(appointment.type)}</p>
                        <p class="text-secondary">${appointment.reason}</p>
                    </div>
                    <div class="appointment-actions">
                        <button onclick="viewAppointmentDetails('${appointment.id}')" class="btn btn-sm btn-primary">View</button>
                    </div>
                </div>
            `).join('');
        }

        function displayNextAppointment() {
            const container = document.getElementById('next-appointment');
            const next = allAppointments
                .filter(apt => apt.status === 'scheduled' && new Date(apt.date) >= new Date())
                .sort((a, b) => new Date(a.date) - new Date(b.date))[0];

            if (!next) {
                container.innerHTML = '<p class="text-secondary">No upcoming appointments</p>';
                return;
            }

            container.innerHTML = `
                <div class="next-appointment-details">
                    <h4>${formatDate(next.date)} at ${next.time}</h4>
                    <p><strong>Dr. ${next.doctor_name}</strong></p>
                    <p>${formatType(next.type)}</p>
                    <p class="text-secondary">${next.reason}</p>
                    <div class="countdown" id="countdown-${next.id}"></div>
                    <button onclick="viewAppointmentDetails('${next.id}')" class="btn btn-sm btn-primary mt-2">View Details</button>
                </div>
            `;

            // Start countdown
            startCountdown(next.date, next.time, `countdown-${next.id}`);
        }

        function filterAppointments() {
            const statusFilter = document.getElementById('status-filter').value;
            const typeFilter = document.getElementById('type-filter').value;
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;

            filteredAppointments = allAppointments.filter(appointment => {
                if (statusFilter && appointment.status !== statusFilter) return false;
                if (typeFilter && appointment.type !== typeFilter) return false;
                if (dateFrom && appointment.date < dateFrom) return false;
                if (dateTo && appointment.date > dateTo) return false;
                return true;
            });

            displayAppointments();
        }

        function scheduleAppointment() {
            document.getElementById('appointment-modal').style.display = 'block';
        }

        function closeAppointmentModal() {
            document.getElementById('appointment-modal').style.display = 'none';
            document.getElementById('appointment-form').reset();
        }

        function scheduleRoutineCheckup() {
            scheduleAppointment();
            document.getElementById('appointment_type').value = 'routine_checkup';
        }

        function scheduleUltrasound() {
            scheduleAppointment();
            document.getElementById('appointment_type').value = 'ultrasound';
        }

        function requestEmergencyAppointment() {
            scheduleAppointment();
            document.getElementById('appointment_type').value = 'emergency';
            document.getElementById('priority').value = 'emergency';
        }

        async function rescheduleAppointment(appointmentId) {
            document.getElementById('reschedule_appointment_id').value = appointmentId;
            document.getElementById('reschedule-modal').style.display = 'block';
        }

        function closeRescheduleModal() {
            document.getElementById('reschedule-modal').style.display = 'none';
            document.getElementById('reschedule-form').reset();
        }

        async function cancelAppointment(appointmentId) {
            if (confirm('Are you sure you want to cancel this appointment?')) {
                try {
                    const response = await apiCall(`/mother/appointments/${appointmentId}/cancel`, 'PUT');
                    if (response.success) {
                        showNotification('Appointment cancelled successfully!', 'success');
                        loadAppointments();
                    }
                } catch (error) {
                    showNotification('Error cancelling appointment: ' + error.message, 'error');
                }
            }
        }

        // Form submission handlers
        document.getElementById('appointment-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                
                const response = await apiCall('/mother/appointments', 'POST', data);
                if (response.success) {
                    showNotification('Appointment scheduled successfully!', 'success');
                    closeAppointmentModal();
                    loadAppointments();
                }
            } catch (error) {
                showNotification('Error scheduling appointment: ' + error.message, 'error');
            }
        });

        document.getElementById('reschedule-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const data = Object.fromEntries(formData);
                const appointmentId = data.appointment_id;
                
                const response = await apiCall(`/mother/appointments/${appointmentId}/reschedule`, 'PUT', data);
                if (response.success) {
                    showNotification('Appointment rescheduled successfully!', 'success');
                    closeRescheduleModal();
                    loadAppointments();
                }
            } catch (error) {
                showNotification('Error rescheduling appointment: ' + error.message, 'error');
            }
        });

        // Utility functions
        function formatDateTime(date, time) {
            return `${formatDate(date)} ${time}`;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function formatType(type) {
            return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatStatus(status) {
            return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function getTypeColor(type) {
            const colors = {
                'routine_checkup': 'primary',
                'ultrasound': 'info',
                'consultation': 'secondary',
                'emergency': 'danger',
                'follow_up': 'warning'
            };
            return colors[type] || 'secondary';
        }

        function getStatusColor(status) {
            const colors = {
                'scheduled': 'primary',
                'completed': 'success',
                'cancelled': 'danger',
                'rescheduled': 'warning'
            };
            return colors[status] || 'secondary';
        }

        function startCountdown(date, time, elementId) {
            const targetDate = new Date(`${date} ${time}`);
            const element = document.getElementById(elementId);
            
            if (!element) return;

            const timer = setInterval(() => {
                const now = new Date();
                const difference = targetDate - now;

                if (difference > 0) {
                    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

                    element.innerHTML = `<small class="text-primary">In ${days}d ${hours}h ${minutes}m</small>`;
                } else {
                    element.innerHTML = '<small class="text-danger">Appointment time has passed</small>';
                    clearInterval(timer);
                }
            }, 60000); // Update every minute
        }
    </script>
</body>
</html>
