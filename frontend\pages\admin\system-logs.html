<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs - Admin Dashboard</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .logs-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .logs-controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .controls-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .logs-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.875rem;
        }

        .stat-info { color: #007bff; }
        .stat-warning { color: #ffc107; }
        .stat-error { color: #dc3545; }
        .stat-success { color: #28a745; }

        .logs-viewer {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .logs-header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logs-content {
            background: #1e1e1e;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            height: 500px;
            overflow-y: auto;
            padding: 1rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .log-info {
            color: #8be9fd;
            background: rgba(139, 233, 253, 0.1);
        }

        .log-warning {
            color: #f1fa8c;
            background: rgba(241, 250, 140, 0.1);
        }

        .log-error {
            color: #ff5555;
            background: rgba(255, 85, 85, 0.1);
        }

        .log-success {
            color: #50fa7b;
            background: rgba(80, 250, 123, 0.1);
        }

        .log-debug {
            color: #bd93f9;
            background: rgba(189, 147, 249, 0.1);
        }

        .log-timestamp {
            color: #6272a4;
            font-weight: bold;
        }

        .log-level {
            font-weight: bold;
            padding: 0.125rem 0.375rem;
            border-radius: 3px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
        }

        .level-info { background: #007bff; color: white; }
        .level-warning { background: #ffc107; color: black; }
        .level-error { background: #dc3545; color: white; }
        .level-success { background: #28a745; color: white; }
        .level-debug { background: #6f42c1; color: white; }

        .logs-actions {
            padding: 1rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-highlight {
            background: #ffeb3b;
            color: #000;
            padding: 0.125rem;
            border-radius: 2px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: var(--primary-color);
            color: white;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .refresh-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .controls-row {
                grid-template-columns: 1fr;
            }

            .logs-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .logs-actions {
                flex-direction: column;
            }

            .logs-content {
                height: 400px;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="logs-container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1>📋 System Logs</h1>
                <p>Monitor system activity and troubleshoot issues</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="refreshLogs()">
                    🔄 Refresh
                </button>
                <a href="users.html" class="btn btn-secondary">
                    ← Back to Users
                </a>
            </div>
        </div>

        <!-- Log Statistics -->
        <div class="logs-stats">
            <div class="stat-card">
                <div class="stat-number stat-info" id="info-count">0</div>
                <div class="stat-label">Info Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number stat-warning" id="warning-count">0</div>
                <div class="stat-label">Warnings</div>
            </div>
            <div class="stat-card">
                <div class="stat-number stat-error" id="error-count">0</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number stat-success" id="success-count">0</div>
                <div class="stat-label">Success</div>
            </div>
        </div>

        <!-- Log Controls -->
        <div class="logs-controls">
            <h3>🔍 Log Filters & Controls</h3>
            <div class="controls-row">
                <div class="form-group">
                    <label for="log-level">Log Level</label>
                    <select id="log-level" onchange="filterLogs()">
                        <option value="">All Levels</option>
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="error">Error</option>
                        <option value="success">Success</option>
                        <option value="debug">Debug</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="date-from">From Date</label>
                    <input type="datetime-local" id="date-from" onchange="filterLogs()">
                </div>
                <div class="form-group">
                    <label for="date-to">To Date</label>
                    <input type="datetime-local" id="date-to" onchange="filterLogs()">
                </div>
                <div class="form-group">
                    <label for="search-logs">Search</label>
                    <input type="text" id="search-logs" placeholder="Search in logs..." oninput="searchLogs()">
                </div>
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div class="auto-refresh">
                        <input type="checkbox" id="auto-refresh" onchange="toggleAutoRefresh()">
                        <label for="auto-refresh">Auto Refresh</label>
                        <div class="refresh-indicator" id="refresh-indicator" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Viewer -->
        <div class="logs-viewer">
            <div class="logs-header">
                <h3>📄 System Logs</h3>
                <div>
                    <span id="logs-count">0 entries</span>
                    <span id="last-updated">Last updated: Never</span>
                </div>
            </div>
            <div class="logs-content" id="logs-content">
                <!-- Log entries will be displayed here -->
            </div>
            <div class="logs-actions">
                <button class="btn btn-primary" onclick="refreshLogs()">🔄 Refresh</button>
                <button class="btn btn-secondary" onclick="downloadLogs()">📥 Download</button>
                <button class="btn btn-secondary" onclick="exportLogs()">📤 Export</button>
                <button class="btn btn-danger" onclick="clearLogs()">🗑️ Clear Logs</button>
                <button class="btn btn-secondary" onclick="toggleWrap()">📝 Toggle Wrap</button>
                <button class="btn btn-secondary" onclick="scrollToBottom()">⬇️ Scroll to Bottom</button>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination">
            <button onclick="previousPage()" id="prev-btn">← Previous</button>
            <span id="page-info">Page 1 of 1</span>
            <button onclick="nextPage()" id="next-btn">Next →</button>
        </div>
    </div>

    <!-- Application Scripts -->
    <script src="../../js/script.js"></script>
    <script src="../../js/common-header.js"></script>
    <script>
        // System Logs Management
        let allLogs = [];
        let filteredLogs = [];
        let currentPage = 1;
        const logsPerPage = 100;
        let autoRefreshInterval = null;
        let searchTerm = '';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeDateFilters();
            loadLogs();
        });

        // Initialize date filters
        function initializeDateFilters() {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            document.getElementById('date-from').value = yesterday.toISOString().slice(0, 16);
            document.getElementById('date-to').value = now.toISOString().slice(0, 16);
        }

        // Load system logs
        async function loadLogs() {
            try {
                // Mock log data for demonstration
                allLogs = generateMockLogs();
                filteredLogs = [...allLogs];
                displayLogs();
                updateStatistics();
                updateLastUpdated();
            } catch (error) {
                console.error('Error loading logs:', error);
                showNotification('Error loading system logs', 'error');
            }
        }

        // Generate mock log data
        function generateMockLogs() {
            const logs = [];
            const levels = ['info', 'warning', 'error', 'success', 'debug'];
            const messages = [
                'User authentication successful',
                'Database connection established',
                'Failed to send email notification',
                'System backup completed',
                'High memory usage detected',
                'New user registration',
                'Appointment scheduled successfully',
                'File upload completed',
                'Cache cleared',
                'API request processed',
                'Session expired',
                'Password reset requested',
                'Data export completed',
                'System maintenance started',
                'Configuration updated'
            ];

            for (let i = 0; i < 500; i++) {
                const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
                const level = levels[Math.floor(Math.random() * levels.length)];
                const message = messages[Math.floor(Math.random() * messages.length)];
                
                logs.push({
                    id: i + 1,
                    timestamp: timestamp.toISOString(),
                    level: level,
                    message: message,
                    source: 'MCHS-System',
                    userId: Math.random() > 0.5 ? `user_${Math.floor(Math.random() * 100)}` : null
                });
            }

            return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        }

        // Display logs
        function displayLogs() {
            const logsContent = document.getElementById('logs-content');
            const startIndex = (currentPage - 1) * logsPerPage;
            const endIndex = startIndex + logsPerPage;
            const logsToShow = filteredLogs.slice(startIndex, endIndex);

            logsContent.innerHTML = logsToShow.map(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                const highlightedMessage = highlightSearchTerm(log.message, searchTerm);
                
                return `
                    <div class="log-entry log-${log.level}">
                        <span class="log-timestamp">[${timestamp}]</span>
                        <span class="log-level level-${log.level}">${log.level.toUpperCase()}</span>
                        <span class="log-source">${log.source}:</span>
                        <span class="log-message">${highlightedMessage}</span>
                        ${log.userId ? `<span class="log-user">(User: ${log.userId})</span>` : ''}
                    </div>
                `;
            }).join('');

            updatePagination();
            document.getElementById('logs-count').textContent = `${filteredLogs.length} entries`;
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
            document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
            document.getElementById('prev-btn').disabled = currentPage === 1;
            document.getElementById('next-btn').disabled = currentPage === totalPages;
        }

        // Update statistics
        function updateStatistics() {
            const stats = {
                info: 0,
                warning: 0,
                error: 0,
                success: 0
            };

            filteredLogs.forEach(log => {
                if (stats.hasOwnProperty(log.level)) {
                    stats[log.level]++;
                }
            });

            document.getElementById('info-count').textContent = stats.info;
            document.getElementById('warning-count').textContent = stats.warning;
            document.getElementById('error-count').textContent = stats.error;
            document.getElementById('success-count').textContent = stats.success;
        }

        // Filter logs
        function filterLogs() {
            const levelFilter = document.getElementById('log-level').value;
            const dateFrom = new Date(document.getElementById('date-from').value);
            const dateTo = new Date(document.getElementById('date-to').value);

            filteredLogs = allLogs.filter(log => {
                const logDate = new Date(log.timestamp);
                const matchesLevel = !levelFilter || log.level === levelFilter;
                const matchesDate = (!dateFrom || logDate >= dateFrom) && (!dateTo || logDate <= dateTo);
                const matchesSearch = !searchTerm || log.message.toLowerCase().includes(searchTerm.toLowerCase());

                return matchesLevel && matchesDate && matchesSearch;
            });

            currentPage = 1;
            displayLogs();
            updateStatistics();
        }

        // Search logs
        function searchLogs() {
            searchTerm = document.getElementById('search-logs').value;
            filterLogs();
        }

        // Highlight search term
        function highlightSearchTerm(text, term) {
            if (!term) return text;
            
            const regex = new RegExp(`(${term})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }

        // Pagination functions
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                displayLogs();
            }
        }

        function nextPage() {
            const totalPages = Math.ceil(filteredLogs.length / logsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                displayLogs();
            }
        }

        // Auto refresh
        function toggleAutoRefresh() {
            const autoRefresh = document.getElementById('auto-refresh').checked;
            const indicator = document.getElementById('refresh-indicator');

            if (autoRefresh) {
                autoRefreshInterval = setInterval(() => {
                    loadLogs();
                }, 5000); // Refresh every 5 seconds
                indicator.style.display = 'block';
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                indicator.style.display = 'none';
            }
        }

        // Utility functions
        function refreshLogs() {
            loadLogs();
            showNotification('Logs refreshed', 'success');
        }

        function downloadLogs() {
            const logText = filteredLogs.map(log => {
                const timestamp = new Date(log.timestamp).toISOString();
                return `[${timestamp}] ${log.level.toUpperCase()} ${log.source}: ${log.message}`;
            }).join('\n');

            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            showNotification('Logs downloaded', 'success');
        }

        function exportLogs() {
            const csvContent = [
                'Timestamp,Level,Source,Message,User',
                ...filteredLogs.map(log => 
                    `"${log.timestamp}","${log.level}","${log.source}","${log.message}","${log.userId || ''}"`
                )
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
            showNotification('Logs exported to CSV', 'success');
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all system logs? This action cannot be undone.')) {
                allLogs = [];
                filteredLogs = [];
                displayLogs();
                updateStatistics();
                showNotification('System logs cleared', 'success');
            }
        }

        function toggleWrap() {
            const logsContent = document.getElementById('logs-content');
            const currentWrap = logsContent.style.whiteSpace;
            logsContent.style.whiteSpace = currentWrap === 'pre-wrap' ? 'nowrap' : 'pre-wrap';
        }

        function scrollToBottom() {
            const logsContent = document.getElementById('logs-content');
            logsContent.scrollTop = logsContent.scrollHeight;
        }

        function updateLastUpdated() {
            document.getElementById('last-updated').textContent = `Last updated: ${new Date().toLocaleString()}`;
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 2rem;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                border-radius: 4px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
