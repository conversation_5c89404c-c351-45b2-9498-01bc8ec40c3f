<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Maternal-Child Health System</title>
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 2rem;
        }

        .auth-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .auth-content {
            display: flex;
            min-height: 600px;
        }

        .auth-form {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .auth-banner {
            flex: 1;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
            color: white;
            padding: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-light);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .role-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .role-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid var(--border-light);
            border-radius: 0.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .role-option:hover {
            border-color: var(--primary-color);
            background: var(--bg-light);
        }

        .role-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .role-option input[type="radio"] {
            display: none;
        }

        .btn-primary {
            width: 100%;
            padding: 0.75rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 2rem;
        }

        .mt-3 {
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .auth-content {
                flex-direction: column;
            }
            
            .auth-banner {
                order: -1;
                padding: 2rem;
            }
            
            .auth-form {
                padding: 2rem;
            }
            
            .role-selector {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-content">
                <!-- Form Section -->
                <div class="auth-form">
                    <div class="text-center mb-4">
                        <h1 style="color: var(--primary-color); margin-bottom: 0.5rem;">🤱 MCHS</h1>
                        <h2 style="margin-bottom: 0.5rem;">Create Your Account</h2>
                        <p style="color: var(--text-secondary);">Join the Maternal-Child Health System</p>
                    </div>

                    <form id="register-form">
                        <div class="form-group">
                            <label for="full_name">Full Name</label>
                            <input type="text" id="full_name" name="full_name" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" class="form-control" required minlength="6">
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">Confirm Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label>Account Type</label>
                            <div class="role-selector">
                                <div class="role-option selected" onclick="selectRole('user', this)">
                                    <input type="radio" name="role" value="user" checked>
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🤰</div>
                                    <div style="font-weight: 500;">Mother</div>
                                    <div style="font-size: 0.875rem; opacity: 0.8;">Track pregnancy & baby</div>
                                </div>
                                <div class="role-option" onclick="selectRole('doctor', this)">
                                    <input type="radio" name="role" value="doctor">
                                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🩺</div>
                                    <div style="font-weight: 500;">Doctor</div>
                                    <div style="font-size: 0.875rem; opacity: 0.8;">Manage patients</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional fields for doctors -->
                        <div id="doctor-fields" style="display: none;">
                            <div class="form-group">
                                <label for="license_number">Medical License Number</label>
                                <input type="text" id="license_number" name="license_number" class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="specialization">Specialization</label>
                                <select id="specialization" name="specialization" class="form-control">
                                    <option value="">Select Specialization</option>
                                    <option value="obstetrics">Obstetrics</option>
                                    <option value="gynecology">Gynecology</option>
                                    <option value="pediatrics">Pediatrics</option>
                                    <option value="family_medicine">Family Medicine</option>
                                    <option value="maternal_fetal_medicine">Maternal-Fetal Medicine</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="hospital_affiliation">Hospital/Clinic Affiliation</label>
                                <input type="text" id="hospital_affiliation" name="hospital_affiliation" class="form-control">
                            </div>
                        </div>

                        <button type="submit" class="btn-primary">Create Account</button>

                        <div class="text-center mt-3">
                            <p style="color: var(--text-secondary);">
                                Already have an account? 
                                <a href="login.html" style="color: var(--primary-color); text-decoration: none;">Sign In</a>
                            </p>
                        </div>

                        <div class="text-center">
                            <p style="color: var(--text-secondary); font-size: var(--font-size-sm);">
                                By creating an account, you agree to our Terms of Service and Privacy Policy.
                            </p>
                        </div>
                    </form>
                </div>

                <!-- Banner Section -->
                <div class="auth-banner">
                    <div>
                        <div style="font-size: 4rem; margin-bottom: 1rem;">👶</div>
                        <h2 style="margin-bottom: 1rem;">Join Our Community</h2>
                        <p style="opacity: 0.9; margin-bottom: 2rem;">
                            Connect with healthcare professionals, track your journey, and access 
                            comprehensive maternal-child health resources.
                        </p>
                        <div style="display: flex; justify-content: center; gap: 2rem; font-size: var(--font-size-sm);">
                            <div>✓ Secure Platform</div>
                            <div>✓ Expert Support</div>
                            <div>✓ 24/7 Access</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/common-header.js"></script>
    <script>
        // Role selection
        function selectRole(role, element) {
            // Remove selected class from all options
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selected class to clicked option
            element.classList.add('selected');
            
            // Update radio button
            document.querySelector(`input[value="${role}"]`).checked = true;
            
            // Show/hide doctor fields
            const doctorFields = document.getElementById('doctor-fields');
            if (role === 'doctor') {
                doctorFields.style.display = 'block';
                // Make doctor fields required
                document.getElementById('license_number').required = true;
                document.getElementById('specialization').required = true;
                document.getElementById('hospital_affiliation').required = true;
            } else {
                doctorFields.style.display = 'none';
                // Remove required from doctor fields
                document.getElementById('license_number').required = false;
                document.getElementById('specialization').required = false;
                document.getElementById('hospital_affiliation').required = false;
            }
        }

        // Form validation
        function validateForm(data) {
            if (data.password !== data.confirm_password) {
                throw new Error('Passwords do not match');
            }
            
            if (data.password.length < 6) {
                throw new Error('Password must be at least 6 characters long');
            }
            
            if (data.role === 'doctor') {
                if (!data.license_number || !data.specialization || !data.hospital_affiliation) {
                    throw new Error('Please fill in all doctor-specific fields');
                }
            }
        }

        // Registration form handler
        FormHandler.handleSubmit('register-form', async (data) => {
            const submitBtn = document.querySelector('#register-form button[type="submit"]');
            const hideLoading = UIUtils.showLoading(submitBtn);

            try {
                // Validate form
                validateForm(data);
                
                // Remove confirm_password from data
                delete data.confirm_password;
                
                const response = await AuthAPI.register(data);
                
                if (response.success) {
                    UIUtils.showAlert('Registration successful! Please login to continue.', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else {
                    UIUtils.showAlert(response.message || 'Registration failed', 'error');
                }
            } catch (error) {
                UIUtils.showAlert(error.message || 'Registration failed', 'error');
            } finally {
                hideLoading();
            }
        });
    </script>
</body>
</html>
